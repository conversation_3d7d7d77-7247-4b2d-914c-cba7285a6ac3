import { useLocation, useNavigate } from "react-router-dom";
import { useLockFn, useMount } from "ahooks";
import { ComponentType, FC, useEffect } from "react";

// 判断当前路径是否匹配 PUBLIC_ROUTES 中的路径（支持动态参数）
const isPublicRoute = (pathname: string, PublicRoutes: string[]) => {
    let isPublic = false;

    PublicRoutes.forEach((route) => {
        const isExcluded = route.startsWith("!");
        const normalizedRoute = isExcluded ? route.slice(1) : route;

        const routeSegments = normalizedRoute.split("/");
        const pathSegments = pathname.split("/");

        // 路径长度不一致时，跳过该规则
        if (routeSegments.length !== pathSegments.length) {
            return;
        }

        // 逐段检查路径是否匹配
        const match = routeSegments.every(
            (segment, i) => segment === "*" || segment === pathSegments[i]
        );

        if (match) {
            // 如果匹配到以 ! 开头的路径，则说明这是非公共路由
            isPublic = !isExcluded;
        }
    });

    return isPublic;
};

type RouteGuardOptions = {
    publicRoutes?: string[];
};
// 定义 HOC 函数
const withRouteGuard = <P extends object>(
    Component: ComponentType<P>,
    options: RouteGuardOptions = {}
): FC<P> => {
    // 定义包装后的组件
    const WrappedComponent: FC<P> = (props) => {
        const navigate = useNavigate();

        const { pathname } = useLocation();

        const checkAuth = useLockFn(async () => {
            const token = await window.localForage.getItem("cna-token");

            if (pathname === "/") {
                if (token) {
                    navigate("/member/profile", { replace: true });
                } else {
                    navigate("/account/login", { replace: true });
                }
                return;
            }

            if (!isPublicRoute(pathname, options?.publicRoutes ?? [])) {
                if (!token) {
                    navigate("/account/login", { replace: true });
                    return;
                }
            }
        });

        useEffect(() => {
            checkAuth();
        }, [pathname]);

        useMount(() => {
            checkAuth();
        });

        return <Component {...props} />;
    };

    // 设置显示名称
    WrappedComponent.displayName = `withRouteGuard(${
        Component.displayName || Component.name || "Component"
    })`;

    return WrappedComponent;
};

export default withRouteGuard;
