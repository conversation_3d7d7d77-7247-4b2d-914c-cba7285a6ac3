import api from "@/apis";
import { t } from "@code.8cent/i18n";
import {
    Group,
    Image,
    Modal,
    Stack,
    Text,
    Box,
    ActionIcon,
    LoadingOverlay,
    Popover,
    Badge,
} from "@mantine/core";
import useModalStore from "@/store/modal";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn, useRequest } from "ahooks";
import {
    Check,
    Eye,
    DownloadSimple,
    FilePdf,
    FileDoc,
    File as FileIcon,
    Info,
} from "@phosphor-icons/react";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import noty from "@code.8cent/react/noty";
import { downloadFile } from "@/utils/files";
import { useEffect, useState, memo, useRef } from "react";
import CnaAdminButton from "@/components/common/CnaAdminButton";

// 文件类型
interface UploadFileItem {
    id: string;
    file_id: string;
    file_name: string;
    file_path: string;
    file_owner: string;
    gsp_id: string;
    created_at: string | null;
    updated_at: string | null;
    profile_id?: string;
    status?: number;
}

interface CategoryItem {
    id: number;
    allow_online_form: number;
    allow_template_form: number;
    allow_upload_file: number;
    db_name: string;
    type: number;
    template_file: string;
    category_zh_cn: string;
    description_zh_cn: string;
    upload: UploadFileItem[];
}

const FileItemImage = memo(
    ({
        file,
        onRefresh,
        gspId,
    }: {
        file: UploadFileItem;
        onRefresh: () => void;
        gspId: string;
    }) => {
        const { openFileView } = useFileViewer();
        const openConfirm = useModalStore.use.openConfirm();

        // 下载
        const handleDownload = useMemoizedFn(async () => {
            try {
                await downloadFile(file.file_path, "local");
            } catch (error) {
                noty.error("下载失败，请稍后重试");
            }
        });
        const confirmDownload = useMemoizedFn(() => {
            openConfirm({
                title: "确定下载此文件吗？",
                onConfirm: handleDownload,
            });
        });
        // 预览
        const previewFile = useMemoizedFn(() => {
            openFileView(
                `${window.api_base_url}/api/v1/admin/firm/files?path=${file.file_path}&type=local`,
                {
                    title: "查看文件",
                }
            );
        });
        // 文件类型
        const getFileExtension = (filePath: string): string =>
            filePath.split(".").pop()?.toLowerCase() || "";
        const extension = getFileExtension(file.file_path);
        let icon = (
            <FileIcon
                size={48}
                weight="fill"
            />
        );
        let color = "#95a5a6";
        let label = "文件";
        if (["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension)) {
            icon = null;
            color = "";
            label = "";
        } else if (extension === "pdf") {
            icon = (
                <FilePdf
                    size={48}
                    weight="fill"
                />
            );
            color = "#e74c3c";
            label = "PDF";
        } else if (["doc", "docx"].includes(extension)) {
            icon = (
                <FileDoc
                    size={48}
                    weight="fill"
                />
            );
            color = "#4285f4";
            label = "Word";
        }
        // 格式化时间
        const formatDate = (dateStr: string | null) => {
            if (!dateStr) return "暂无";
            return new Date(dateStr).toLocaleString("zh-CN", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
            });
        };
        // 审核
        const handleReview = useMemoizedFn((status: number) => {
            const statusText = {
                1: "通过",
                2: "失败",
            }[status];

            openConfirm({
                title: "确定审核",
                message: `确定将该文件状态设置为"${statusText}"吗？`,
                onConfirm: async () => {
                    try {
                        const res = await api.gsp.checkReportFile({
                            id: gspId,
                            status,
                            file_id: parseInt(file.id),
                        });
                        if (res) {
                            noty.success(`审核${statusText}成功`);
                            onRefresh();
                        } else {
                            noty.error("审核失败");
                        }
                    } catch {
                        noty.error("审核失败");
                    }
                },
            });
        });
        return (
            <Box className="tw-relative tw-group tw-mb-2">
                {icon !== null ? (
                    <Box
                        className="tw-flex tw-items-center tw-justify-center tw-bg-gray-100 tw-cursor-pointer"
                        h={100}
                        w={100}
                        onClick={previewFile}
                    >
                        <div style={{ color }}>{icon}</div>
                        <Text
                            size="xs"
                            className="tw-absolute tw-bottom-1 tw-text-center"
                        >
                            {label}
                        </Text>
                        <Badge
                            className="tw-absolute tw-top-1 tw-right-1"
                            color={
                                file.status === 0
                                    ? "yellow"
                                    : file.status === 1
                                    ? "green"
                                    : file.status === 2
                                    ? "red"
                                    : "gray"
                            }
                            size="sm"
                        >
                            {file.status === 0
                                ? "待审核"
                                : file.status === 1
                                ? "通过"
                                : file.status === 2
                                ? "失败"
                                : "已废弃"}
                        </Badge>
                    </Box>
                ) : (
                    <Box className="tw-relative">
                        <Image
                            h={100}
                            w={100}
                            src={`https://doc.corporate-advisory.cn/${file.file_path}`}
                            className="tw-cursor-pointer"
                            onClick={previewFile}
                        />
                        <Badge
                            className="tw-absolute tw-top-1 tw-right-1"
                            color={
                                file.status === 0
                                    ? "yellow"
                                    : file.status === 1
                                    ? "green"
                                    : file.status === 2
                                    ? "red"
                                    : "gray"
                            }
                            size="sm"
                        >
                            {file.status === 0
                                ? "待审核"
                                : file.status === 1
                                ? "通过"
                                : file.status === 2
                                ? "失败"
                                : "已废弃"}
                        </Badge>
                    </Box>
                )}
                <Box className="tw-absolute tw-inset-0 tw-bg-black/30 tw-opacity-0 group-hover:tw-opacity-100 tw-transition-opacity tw-flex tw-items-center tw-justify-center">
                    <Group justify="center">
                        <Popover
                            withArrow
                            shadow="md"
                            position="top"
                            closeOnClickOutside
                            withinPortal
                        >
                            <Popover.Target>
                                <ActionIcon
                                    variant="filled"
                                    color="dark"
                                    title="详情"
                                >
                                    <Info size={20} />
                                </ActionIcon>
                            </Popover.Target>
                            <Popover.Dropdown>
                                <Stack>
                                    <Group justify="space-between">
                                        <Text size="sm">上传角色：</Text>
                                        <Text size="sm">
                                            {file.file_owner == "1"
                                                ? "合伙人"
                                                : file.file_owner == "2"
                                                ? "企业用户"
                                                : "其他"}
                                        </Text>
                                    </Group>
                                    <Group justify="space-between">
                                        <Text size="sm">上传时间：</Text>
                                        <Text size="sm">{formatDate(file.created_at)}</Text>
                                    </Group>
                                    <Group justify="space-between">
                                        <Text size="sm">文件类型：</Text>
                                        <Text size="sm">{label || "未知"}</Text>
                                    </Group>
                                </Stack>
                            </Popover.Dropdown>
                        </Popover>
                        <ActionIcon
                            variant="filled"
                            color="dark"
                            onClick={previewFile}
                            title="预览"
                        >
                            <Eye size={20} />
                        </ActionIcon>
                        <ActionIcon
                            variant="filled"
                            color="dark"
                            onClick={confirmDownload}
                            title="下载"
                        >
                            <DownloadSimple size={20} />
                        </ActionIcon>
                        {file.status !== 3 && (
                            <Popover
                                withArrow
                                shadow="md"
                                position="top"
                                closeOnClickOutside
                                withinPortal
                            >
                                <Popover.Target>
                                    <ActionIcon
                                        variant="filled"
                                        color="dark"
                                        title="审核"
                                    >
                                        <Check size={20} />
                                    </ActionIcon>
                                </Popover.Target>
                                <Popover.Dropdown>
                                    <Stack>
                                        <Text
                                            size="sm"
                                            fw={500}
                                        >
                                            请选择审核状态：
                                        </Text>
                                        <Group>
                                            <CnaAdminButton
                                                size="xs"
                                                color="green"
                                                onClick={() => {
                                                    handleReview(1);
                                                }}
                                            >
                                                通过
                                            </CnaAdminButton>
                                            <CnaAdminButton
                                                size="xs"
                                                color="red"
                                                onClick={() => {
                                                    handleReview(2);
                                                }}
                                            >
                                                失败
                                            </CnaAdminButton>
                                        </Group>
                                    </Stack>
                                </Popover.Dropdown>
                            </Popover>
                        )}
                    </Group>
                </Box>
            </Box>
        );
    }
);
FileItemImage.displayName = "FileItemImage";

const CategoryRow = memo(
    ({ item, onRefresh, gspId }: { item: CategoryItem; onRefresh: () => void; gspId: string }) => {
        return (
            <Box className="tw-mb-6 tw-p-4 tw-bg-gray-50 tw-rounded">
                <Group
                    align="center"
                    mb={8}
                >
                    <Text fw={500}>{item.category_zh_cn}</Text>
                    <Popover
                        withArrow
                        shadow="md"
                        position="top"
                        closeOnClickOutside
                        withinPortal
                    >
                        <Popover.Target>
                            <ActionIcon
                                variant="light"
                                color="gray"
                                size="sm"
                            >
                                <Info size={18} />
                            </ActionIcon>
                        </Popover.Target>
                        <Popover.Dropdown>
                            <Text size="sm">{item.description_zh_cn}</Text>
                        </Popover.Dropdown>
                    </Popover>
                </Group>
                <Group>
                    {item.upload && item.upload.length > 0 ? (
                        item.upload.map((file, idx) => (
                            <FileItemImage
                                key={item.id + "-" + idx}
                                file={file}
                                onRefresh={onRefresh}
                                gspId={gspId}
                            />
                        ))
                    ) : (
                        <Text c="dimmed">暂无文件</Text>
                    )}
                </Group>
            </Box>
        );
    }
);
CategoryRow.displayName = "CategoryRow";

const ReportFilesV2 = () => {
    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.gspReportFilesModal,
            close: state.close,
        }))
    );
    const modalParams = useModalStore((state) => state.modalParams.gspReportFilesModal);
    const gspApplication = modalParams?.gspApplication;
    const closeModal = useMemoizedFn(() => {
        close("gspReportFilesModal");
    });
    const [data, setData] = useState<CategoryItem[]>([]);
    const [applicationId, setApplicationId] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [scrollPosition, setScrollPosition] = useState<number>(0);
    const modalRef = useRef<HTMLDivElement>(null);

    // 获取报告文件
    const { run: getReportFiles } = useRequest(
        async (id: string) => {
            if (!id) return;
            setIsLoading(true);
            try {
                const res = await api.gsp.getReportFiles(id);
                if (Array.isArray(res)) {
                    setData(res);
                    // 在数据更新后恢复滚动位置
                    setTimeout(() => {
                        if (modalRef.current) {
                            modalRef.current.scrollTop = scrollPosition;
                        }
                    }, 0);
                }
                return res;
            } catch (error) {
                noty.error("获取报告文件失败，请稍后重试");
            } finally {
                setIsLoading(false);
            }
        },
        { manual: true }
    );

    // 记录滚动位置
    const handleScroll = useMemoizedFn(() => {
        if (modalRef.current) {
            setScrollPosition(modalRef.current.scrollTop);
        }
    });

    useEffect(() => {
        if (isVisible && gspApplication?.id) {
            setApplicationId(gspApplication.id);
            getReportFiles(gspApplication.id);
        }
    }, [isVisible, gspApplication]);

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title="审核尽调报告文件"
            size="xl"
        >
            <div
                ref={modalRef}
                onScroll={handleScroll}
                style={{ maxHeight: "80vh", overflowY: "auto" }}
            >
                {isLoading && (
                    <LoadingOverlay
                        visible={isLoading}
                        zIndex={1000}
                    />
                )}
                {!isLoading && (
                    <Stack gap={0}>
                        {data.length > 0 ? (
                            data.map((item, idx) => (
                                <CategoryRow
                                    key={item.id + "-" + idx}
                                    item={item}
                                    onRefresh={() => getReportFiles(applicationId)}
                                    gspId={applicationId}
                                />
                            ))
                        ) : (
                            <Text
                                c="dimmed"
                                style={{ textAlign: "center" }}
                            >
                                暂无数据
                            </Text>
                        )}
                    </Stack>
                )}
            </div>
        </Modal>
    );
};

export default ReportFilesV2;
