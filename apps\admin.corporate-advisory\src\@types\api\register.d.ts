declare global {
    type RegisterParams = {
        email: string;
        phone: string;
        prefixID: string;
    };

    type ConfirmRegisterParams = RegisterParams & {
        addressCity: string;
        addressCountry: string;
        addressPostcode: string;
        addressStreet: string;
        addressUnit: string;
        addressState: string;
        birthDate: string;
        gender: string;
        idNumber: string;
        name: string;
        nationalityID: string;
        /** 是否三三制？ */
        is_team?: 0 | 1;
        /** 推荐人ID */
        recommend?: number;
    };

    type ReffererInfo = {
        is_team: 0 | 1;
        team_file: 0 | 1;
        profileID: number;
        profileName: string;
        profilePartnerCode: string;
    };
}

export {};
