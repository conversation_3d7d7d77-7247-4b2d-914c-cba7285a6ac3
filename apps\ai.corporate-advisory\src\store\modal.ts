import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";

export type ModalState = {
    alert: {
        show: boolean;
        title?: string;
        message?: string;
        variant?: "success" | "danger" | "warning" | "info";
    };
    upload: {
        show: boolean;
        title?: string;
        accept: string[];
        maxSize: number;
        required?: boolean;
    };
    languageSelect: boolean;
    passwordReset: boolean;
    profileValidate: boolean;
    calendarEventCreation: boolean;
    projectCompanyCreate: boolean;
    projectCompanyApplication: boolean;
    projectCompanyDetail: boolean;
    confirm: ModalState["alert"] & {
        onConfirm?: () => void;
    };
};

type ModalAction = {
    open: (modal: keyof ModalState) => void;
    close: (modal: keyof ModalState) => void;
    openConfirm: (props: {
        title?: string;
        message?: string;
        variant?: ModalState["alert"]["variant"];
        onConfirm?: () => void;
    }) => void;
    closeConfirm: () => void;
    openUpload: (opts: {
        title?: string;
        accept?: string[];
        maxSize: number;
        required?: boolean;
    }) => void;
    closeUpload: () => void;
};

const baseModalStore = create<ModalState & ModalAction>()(
    devtools(
        (set) => ({
            alert: {
                show: false,
                title: "",
                message: "",
                variant: "info",
            },
            confirm: {
                show: false,
                title: "",
                message: "",
                onConfirm: () => {},
            },
            upload: {
                title: "",
                show: false,
                accept: ["image/png", "image/jpeg"],
                maxSize: 5 * 1024 * 1024,
                required: true,
            },
            calendarEventCreation: false,
            languageSelect: false,
            passwordReset: false,
            profileValidate: false,
            projectCompanyCreate: false,
            projectCompanyApplication: false,
            projectCompanyDetail: false,
            open: (modal) => set((state) => ({ ...state, [modal]: true })),
            close: (modal) => set((state) => ({ ...state, [modal]: false })),
            openConfirm: ({
                title,
                message,
                variant = "info",
                onConfirm = () => {},
            }) => {
                set({
                    confirm: {
                        show: true,
                        title,
                        message,
                        variant,
                        onConfirm,
                    },
                });
            },
            closeConfirm: () => {
                set({
                    confirm: {
                        show: false,
                        title: "",
                        message: "",
                        variant: "info",
                        onConfirm: () => {},
                    },
                });
            },
            openUpload: ({
                title = "文件上传",
                accept = ["image/png", "image/jpeg"],
                required = true,
                maxSize,
            }) => {
                set({
                    upload: {
                        show: true,
                        title,
                        accept,
                        required,
                        maxSize,
                    },
                });
            },
            closeUpload: () => {
                set({
                    upload: {
                        show: false,
                        title: "",
                        accept: ["image/png", "image/jpeg"],
                        maxSize: 5 * 1024 * 1024,
                        required: true,
                    },
                });
            },
        }),
        {
            name: "modal-store",
        }
    )
);

const useModalStore = createSelectors(baseModalStore);

export default useModalStore;
