import CnaButton from "@code.8cent/react/components/CnaButton";
import CompleteSetup from "@/components/wizard/CompleteSetup";
import LanguageSetup from "@/components/wizard/LanguageSetup";
import SecuritySetup from "@/components/wizard/SecuritySetup";
import UserSetup from "@/components/wizard/UserSetup";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import { useEventBus } from "@/utils/eventBus";
import { Box, Group, Image, Stack, Text, Title } from "@mantine/core";
import { useMemoizedFn, useMount, useRequest, useUnmount } from "ahooks";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import ContractSign from "@/components/wizard/ContractSign";
import InvitePartner from "@/components/wizard/InvitePartner";
import ProfileNRIC from "@/components/wizard/ProfileNRIC";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";

const RenderWizardSetting = ({ settingName }: { settingName: string }) => {
    switch (settingName) {
        case "settingLanguage": {
            return <LanguageSetup />;
        }

        case "profilePassword": {
            return <SecuritySetup />;
        }

        case "profileAvatar": {
            return <UserSetup />;
        }

        case "profileNRIC": {
            return <ProfileNRIC />;
        }

        case "profileContract": {
            return <ContractSign />;
        }

        case "invitePartner": {
            return <InvitePartner />;
        }

        case "complete": {
            return <CompleteSetup />;
        }

        default: {
            return null;
        }
    }
};

const AccountWizardPage = () => {
    const bus = useEventBus();

    const lang = useSettingStore.use.lang();

    const navigate = useNavigate();

    const { state, setState, registerSetting, setRegisterSetting } = useWizardStore();

    const [loading, setLoading] = useState(false);

    const [disabled, setDisabled] = useState(false);

    const submit = useMemoizedFn(() => {
        bus.emit("wizard.submit.click");
    });

    const { run } = useRequest(async () => {
        const { result, error } = await cnaRequest<{ register_setting: string[] }>(
            "/api/v1/user/registerSetting",
            "GET",
            {}
        );

        if (!error) {
            const settings = result.data?.register_setting ?? [];
            setRegisterSetting([...settings, "invitePartner", "complete"]);
        }
    });

    useMount(async () => {
        let token = (await window.localForage.getItem("cna-token")) as string;

        if (!token || !token.length) {
            navigate("/account/login");
            return;
        }

        bus.on("wizard.submitting", setLoading);

        bus.on("wizard.submit.disabled", setDisabled);
    });

    useUnmount(() => {
        bus.off("wizard.submitting", setLoading);

        bus.off("wizard.submit.disabled", setDisabled);
    });

    return (
        <div className="tw-flex tw-h-[100vh] tw-w-[100vw]">
            <div className="md:tw-w-[200px] md:tw-flex lg:tw-w-[240px] tw-border-r tw-bg-basic-5 tw-hidden tw-flex-col tw-w-0 tw-transition-all">
                <div className="tw-mb-2 tw-mt-5">
                    <Image
                        src="/images/C&A-Logo-Icon-White.svg"
                        w={60}
                        className="tw-mx-auto tw-my-4"
                    />
                    <Text className="tw-text-center tw-mt-3 tw-text-xl tw-text-white tw-tracking-[3px] tw-font-bold">
                        {/* {t("dashboard.title", lang)} */}
                        AI 办公室
                    </Text>
                </div>
                <div className="tw-flex-1 tw-py-3 tw-overflow-y-auto">
                    {registerSetting.map((setting, index) => {
                        return (
                            <div
                                className={`tw-flex tw-px-8 tw-py-4 tw-items-center tw-justify-start ${
                                    index === state
                                        ? "tw-text-gray-50 tw-font-bold tw-bg-basic-7"
                                        : "tw-text-gray-300"
                                } tw-cursor-pointer`}
                                key={index}
                            >
                                <Text className="tw-flex-1 tw-text-left tw-ml-6">
                                    {t(`wizard.${setting}`, lang)}
                                </Text>
                            </div>
                        );
                    })}
                </div>
            </div>
            <div className="tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden">
                <Stack
                    className="tw-bg-white tw-p-6 md:tw-py-6 md:tw-px-24 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0"
                    align="center"
                >
                    <Box className="tw-w-full">
                        <Title order={2}>设置启动</Title>
                        <Text size="sm">请按照以下说明完成您的账户设置。</Text>

                        <Box className="tw-shadow-lg tw-border tw-px-6 tw-rounded-sm tw-mt-[18px]">
                            <Stack gap={0}>
                                <Group className="tw-py-4 tw-border-b">
                                    <Title
                                        order={3}
                                        fw="normal"
                                    >
                                        {t(`wizard.${registerSetting[state]}`, lang)}
                                    </Title>
                                </Group>

                                <Box className="tw-py-4 tw-border-b">
                                    <RenderWizardSetting settingName={registerSetting[state]} />
                                </Box>

                                <Group
                                    className="tw-py-4"
                                    justify={
                                        registerSetting[state] === "complete"
                                            ? "center"
                                            : "space-between"
                                    }
                                >
                                    {state !== 0 ? (
                                        <CnaButton
                                            color="basic"
                                            miw={100}
                                            onClick={() => {
                                                setState(state - 1);
                                            }}
                                        >
                                            上一步
                                        </CnaButton>
                                    ) : (
                                        <Box />
                                    )}
                                    <CnaButton
                                        color="basic"
                                        miw={100}
                                        onClick={submit}
                                        loading={loading}
                                        disabled={disabled}
                                    >
                                        {registerSetting[state] === "complete"
                                            ? "进入"
                                            : t("common.next.page", lang)}
                                    </CnaButton>
                                </Group>
                            </Stack>
                        </Box>
                    </Box>
                </Stack>
            </div>
        </div>
    );
};

export default AccountWizardPage;
