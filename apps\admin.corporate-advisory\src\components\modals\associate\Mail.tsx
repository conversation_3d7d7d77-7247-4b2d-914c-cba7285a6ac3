import useModalStore from "@/store/modal";
import React, { useState, useMemo, useEffect } from "react";
import {
    Group,
    Modal,
    Stack,
    Text,
    TextInput,
    SimpleGrid,
    CopyButton,
    Tooltip,
    ActionIcon,
    Input,
} from "@mantine/core";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Copy, Check, X } from "@phosphor-icons/react";
import { CnaButton } from "@code.8cent/react/components";
import { RichTextEditor, Link } from "@mantine/tiptap";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useShallow } from "zustand/react/shallow";
import ModalFooter from "@/components/common/ModalFooter";
import api from "@/apis";
import noty from "@code.8cent/react/noty";

const Mail = React.memo(() => {
    const lang = useSettingStore.use.lang();
    const openConfirm = useModalStore.use.openConfirm();

    const mailTemplateParams = useModalStore((state) => state.modalParams.mailSettingModal);
    const template = mailTemplateParams?.template;

    useEffect(() => {
        setTemplateData((prevData) => {
            // Only update if the data has changed
            if (
                prevData.ZH.title !== template?.emailTitleZH ||
                prevData.ZH.content !== template?.emailDescriptionZH
            ) {
                return {
                    ZH: {
                        title: template?.emailTitleZH || "",
                        content: template?.emailDescriptionZH || "",
                    },
                    ZT: {
                        title: template?.emailTitleZT || "",
                        content: template?.emailDescriptionZT || "",
                    },
                    EN: {
                        title: template?.emailTitleEN || "",
                        content: template?.emailDescriptionEN || "",
                    },
                    MS: {
                        title: template?.emailTitleMS || "",
                        content: template?.emailDescriptionMS || "",
                    },
                };
            }
            return prevData;
        });
    }, [template]);

    const BTN_MAP = [
        { key: "ZH", title: "中文 (简体)" },
        { key: "ZT", title: "中文 (繁体)" },
        { key: "EN", title: "英文" },
        { key: "MS", title: "马来文" },
    ];

    const [activeLanguage, setActiveLanguage] = useState("ZH");

    const [templateData, setTemplateData] = useState({
        ZH: {
            title: template?.emailTitleZH || "",
            content: template?.emailDescriptionZH || "",
        },
        ZT: {
            title: template?.emailTitleZT || "",
            content: template?.emailDescriptionZT || "",
        },
        EN: {
            title: template?.emailTitleEN || "",
            content: template?.emailDescriptionEN || "",
        },
        MS: {
            title: template?.emailTitleMS || "",
            content: template?.emailDescriptionMS || "",
        },
    });

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.mailSettingModal,
            close: state.close,
        }))
    );

    const templateTitle = templateData[activeLanguage]?.title || "";
    const templateContent = templateData[activeLanguage]?.content || "";

    const updateTemplateData = (key, field, value) => {
        setTemplateData((prev) => ({
            ...prev,
            [key]: {
                ...prev[key],
                [field]: value,
            },
        }));
    };

    const handleTitleChange = (e) => {
        updateTemplateData(activeLanguage, "title", e.target.value);
    };

    const onEditorUpdate = ({ editor }) => {
        const newContent = editor.getHTML();
        if (newContent !== templateData[activeLanguage].content) {
            updateTemplateData(activeLanguage, "content", newContent);
        }
    };

    const editor = useEditor({
        extensions: [StarterKit, Link],
        content: templateContent,
        onUpdate: onEditorUpdate,
    });

    // 监听 activeLanguage 的变化并更新编辑器内容
    useEffect(() => {
        if (editor && templateData[activeLanguage].content !== editor.getHTML()) {
            editor.commands.setContent(templateData[activeLanguage].content || ""); // 更新为当前语言的内容
        }
    }, [activeLanguage, editor, templateData]);

    const handleLanguageButtonClick = (key: string) => {
        setActiveLanguage(key);
    };

    const handleConfirmSave = async () => {
        try {
            const res = await api.emailTemplate.update({
                eventCode: template.eventCode,
                emailTitleEN: templateData.EN.title,
                emailTitleZH: templateData.ZH.title,
                emailTitleZT: templateData.ZT.title,
                emailTitleMS: templateData.MS.title,
                emailDescriptionEN: templateData.EN.content,
                emailDescriptionZH: templateData.ZH.content,
                emailDescriptionZT: templateData.ZT.content,
                emailDescriptionMS: templateData.MS.content,
            });

            if (res) {
                noty.success("保存成功");
                close("mailSettingModal");
            }
        } catch (error) {
            noty.error("保存失败");
            console.error("保存失败:", error);
        }
    };

    const handleSave = () => {
        openConfirm({
            title: "更新邮件模版内容",
            message: "您确定此操作么？",
            onConfirm: handleConfirmSave,
        });
    };

    const editorToolbarButtons = [
        {
            group: [
                <RichTextEditor.Undo
                    title="撤销"
                    key="undo"
                />,
                <RichTextEditor.Redo
                    title="重做"
                    key="redo"
                />,
            ],
        },
        {
            group: [
                <RichTextEditor.Bold
                    title="粗体"
                    key="bold"
                />,
                <RichTextEditor.Italic
                    title="斜体"
                    key="italic"
                />,
                <RichTextEditor.Strikethrough
                    title="删除线"
                    key="strikethrough"
                />,
                <RichTextEditor.ClearFormatting
                    title="清除格式"
                    key="clearFormatting"
                />,
                <RichTextEditor.Code
                    title="代码"
                    key="code"
                />,
            ],
        },
        {
            group: [
                <RichTextEditor.H1
                    title="标题1"
                    key="h1"
                />,
                <RichTextEditor.H2
                    title="标题2"
                    key="h2"
                />,
                <RichTextEditor.H3
                    title="标题3"
                    key="h3"
                />,
                <RichTextEditor.H4
                    title="标题4"
                    key="h4"
                />,
            ],
        },
        {
            group: [
                <RichTextEditor.Blockquote
                    title="引用"
                    key="blockquote"
                />,
                <RichTextEditor.Hr
                    title="水平线"
                    key="hr"
                />,
                <RichTextEditor.BulletList
                    title="无序列表"
                    key="bulletList"
                />,
                <RichTextEditor.OrderedList
                    title="有序列表"
                    key="orderedList"
                />,
            ],
        },
        {
            group: [
                <RichTextEditor.Link
                    title="插入链接"
                    key="link"
                />,
                <RichTextEditor.Unlink
                    title="移除链接"
                    key="unlink"
                />,
            ],
        },
    ];

    const modalFooterButtons = [
        {
            key: "save",
            label: "保存",
            leftSection: <Check size={16} />,
            onClick: handleSave,
        },
        {
            key: "close",
            label: "关闭",
            style: "outline",
            leftSection: <X size={16} />,
            onClick: () => close("mailSettingModal"),
        },
    ];

    const languageButtons = Object.keys(templateData).map((langKey) => (
        <CnaButton
            size="sm"
            key={langKey}
            variant={langKey === activeLanguage ? "filled" : "default"}
            onClick={() => handleLanguageButtonClick(langKey)}
        >
            {BTN_MAP.find((item) => item.key === langKey)?.title}
        </CnaButton>
    ));

    return (
        <Modal
            opened={isVisible}
            onClose={() => close("mailSettingModal")}
            title="电邮模版 - 审核通过"
            size="xl"
        >
            <Stack gap="md">
                <SimpleGrid cols={4}>{languageButtons}</SimpleGrid>

                <TextInput
                    label="邮件标题"
                    labelProps={{
                        className: "profile-form-label",
                    }}
                    placeholder="请输入邮件标题"
                    value={templateTitle}
                    onChange={handleTitleChange}
                />

                <RichTextEditor
                    styles={{
                        content: {
                            height: 300, // 设置高度
                            overflow: "auto", // 允许滚动
                        },
                    }}
                    editor={editor}
                    className="tw-border tw-border-gray-300 tw-rounded"
                >
                    <RichTextEditor.Toolbar
                        sticky
                        stickyOffset={60}
                        className="tw-flex tw-items-center tw-gap-2 tw-p-2 tw-border-b"
                    >
                        {editorToolbarButtons.map((buttonGroup, index: number) => (
                            <RichTextEditor.ControlsGroup
                                className="tw-border tw-rounded-md tw-px-1 [&>button]:!tw-w-6"
                                key={index}
                            >
                                {buttonGroup.group}
                            </RichTextEditor.ControlsGroup>
                        ))}
                    </RichTextEditor.Toolbar>
                    <RichTextEditor.Content />
                </RichTextEditor>

                <Input.Wrapper
                    labelProps={{
                        className: "profile-form-label",
                    }}
                    label="参数"
                >
                    <Stack gap={"xs"}>
                        {/* todo 参数接口未返回，需要添加 */}
                        <Group>
                            <Text>用户名字</Text>
                            <Text>{"{{username}}"}</Text>
                            <CopyButton
                                value="{{username}}"
                                timeout={2000}
                            >
                                {({ copied, copy }) => (
                                    <Tooltip
                                        label={copied ? "Copied" : "Copy"}
                                        withArrow
                                        position="right"
                                    >
                                        <ActionIcon
                                            color={copied ? "teal" : "gray"}
                                            variant="subtle"
                                            onClick={copy}
                                        >
                                            {copied ? <Check size={16} /> : <Copy size={16} />}
                                        </ActionIcon>
                                    </Tooltip>
                                )}
                            </CopyButton>
                        </Group>
                    </Stack>
                </Input.Wrapper>

                <ModalFooter
                    // timelineContent="最近修改: Amos Wu (2024-11-18 12:00:00)"
                    buttons={modalFooterButtons}
                />
            </Stack>
        </Modal>
    );
});

export default Mail;
