import { <PERSON><PERSON><PERSON>utton } from "@code.8cent/react/components";
import { Box, Checkbox, Group, LoadingOverlay, Modal, Stack, Text, Title } from "@mantine/core";
import { useAsyncEffect, useMemoizedFn, useMount, useRequest, useUnmount } from "ahooks";
import { useEffect, useRef, useState, useMemo, createContext, useContext } from "react";
import { SignatureData, useSignatureModal } from "@code.8cent/react/SignatureModal";
import { useEventBus } from "@/utils/eventBus";
import useWizardStore from "@code.8cent/store/wizard";
import { getFileByUrl, PDFViewer } from "@code.8cent/react/FileViewer";
import { useCountDown } from "@code.8cent/react/hooks";
import { cnaRequest, sleepMs } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import { sign } from "crypto";
import React from "react";
import { DownloadSimple } from "@phosphor-icons/react";

type TContractSignCtx = {
    getContractFile: (url: string) => void | null;
    setGetContractFile: (fn: Function) => void;
};

const initialContextValue: TContractSignCtx = {
    getContractFile: null,
    setGetContractFile: (fn) => {},
};

const SignContractContext = createContext<TContractSignCtx>(initialContextValue);

const ContractFile = React.memo(() => {
    const { setGetContractFile } = useContext(SignContractContext);

    const {
        run: getContractFile,
        loading: fetching,
        data: contractFile = null,
        mutate,
    } = useRequest(
        async (url: string) => {
            try {
                mutate(null);

                let __file = await getFileByUrl(url);

                if (!__file) {
                    noty.error("获取文件失败");
                }

                return __file;
            } catch (err) {
                console.log(err);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (typeof getContractFile === "function") {
            setGetContractFile(() => getContractFile);
        }
    }, [getContractFile]);

    return (
        <Box className="tw-h-[500px] tw-max-h-[60vh] tw-relative">
            {fetching ? <LoadingOverlay /> : null}
            {contractFile?.type === "application/pdf" ? <PDFViewer file={contractFile} /> : null}
        </Box>
    );
});

const ContractSignPanel = ({ disabled }: { disabled?: boolean }) => {
    const { getContractFile } = useContext(SignContractContext);

    const { open } = useSignatureModal();

    const { startCountdown, countdown } = useCountDown();

    const { state, setState } = useWizardStore();

    const [signData, setSignData] = useState<SignatureData>([]);

    const [signed, setSigned] = useState(false);

    const [checked, setChecked] = useState(false);
    const [agreementChecked, setAgreementChecked] = useState(false);

    const bus = useEventBus();

    const { run: submitSign } = useRequest(
        async (signature: string) => {
            const { result, error } = await cnaRequest("/api/v1/profileContract/create", "POST", {
                signature,
            });

            if (error) {
                noty.error(error.message);
            } else {
                setSigned(true);
                noty.success("签署成功");
                getContractFile(
                    `${window.api_base_url}/api/v1/profileContract/previewContractTemplate/${result.data}`
                );
                bus.emit("wizard.submit.disabled", false);
            }
        },
        { manual: true }
    );

    const toNext = () => {
        setState(state + 1);
    };

    const { run: getPDFTemplate } = useRequest(
        async () => {
            const { result, error } = await cnaRequest<string>(
                "/api/v1/profileContract/generateContractTemplateToken",
                "POST"
            );

            if (!error) {
                getContractFile(
                    `${window.api_base_url}/api/v1/profileContract/previewContractTemplate/${result.data}`
                );
            }
        },
        {
            manual: false,
            ready: typeof getContractFile === "function",
        }
    );

    useMount(() => {
        startCountdown(15);

        bus.emit("wizard.submit.disabled", true);

        bus.on("wizard.submit.click", toNext);
    });

    useUnmount(() => {
        bus.off("wizard.submit.click", toNext);
    });

    const openSignatureModal = useMemoizedFn(() => {
        open({
            onConfirm: async ({ data, svg, base64 }) => {
                setSignData(data);
                submitSign(base64);
            },
            signatureData: signData,
        });
    });

    return (
        <Stack>
            <Group>
                <Checkbox
                    label="我已阅读并同意签署《全球合伙人大联盟中国区域合伙人加盟合同》"
                    checked={checked}
                    onChange={(event) => {
                        // 只有在未签署时才允许改变复选框状态
                        if (!signed) {
                            setChecked(event.currentTarget.checked);
                        }
                    }}
                    disabled={signed} // 签署后禁用复选框
                />
                <Checkbox
                    label="上述填写的本人姓名、身份证号码、签名均正确无误。本人也将如实填写后续 C&A 中国所需的其他信息。若因个人提供的信息错误而导致的一切损失由本人承担。C&A 中国将确认填写信息是否真实及准确"
                    checked={agreementChecked}
                    onChange={(event) => {
                        // 只有在未签署时才允许改变复选框状态
                        if (!signed) {
                            setAgreementChecked(event.currentTarget.checked);
                        }
                    }}
                    disabled={signed} // 签署后禁用复选框
                />
            </Group>
            <Group justify="center">
                {signed === true ? (
                    // <CnaButton
                    //     leftSection={<DownloadSimple />}
                    //     onClick={() => {
                    //         window.location.href = `${location.origin}/files/合伙人加盟合同.pdf`;
                    //     }}
                    // >
                    //     下载已签署合同
                    // </CnaButton>
                    <CnaButton disabled>已签署合同</CnaButton>
                ) : (
                    <CnaButton
                        onClick={openSignatureModal}
                        disabled={countdown > 0 || checked !== true || agreementChecked !== true}
                    >
                        确认以上合同内容，并签名{" "}
                        {countdown > 0 ? `(${Math.floor(countdown / 1000)})` : ""}
                    </CnaButton>
                )}
            </Group>
        </Stack>
    );
};

const ContractSign = () => {
    const [getContractFile, setGetContractFile] = useState(null);

    return (
        <SignContractContext.Provider value={{ getContractFile, setGetContractFile }}>
            <Stack>
                <ContractFile />
                <ContractSignPanel />
            </Stack>
        </SignContractContext.Provider>
    );
};

export default ContractSign;
