import useModalStore from "@/store/modal";
import { Button, Group, Modal, Stack } from "@mantine/core";
import { X } from "@phosphor-icons/react";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

const ProjectCompanyDetailModal = () => {
    const opened = useModalStore.use.projectCompanyDetail();

    const close = useModalStore.use.close();

    const lang = useSettingStore.use.lang();

    return (
        <Modal
            size="lg"
            opened={opened}
            title="中国石油天然气集团有限公司"
            onClose={() => close("projectCompanyDetail")}
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-mt-6">
                <Button fullWidth variant="outline" color="dark.3">
                {t("project.details.company", lang)}
                </Button>
                <Button fullWidth variant="outline" color="dark.3">
                {t("project.details.business", lang)}
                </Button>
                <Button fullWidth variant="outline" color="dark.3">
                {t("project.details.record", lang)}
                </Button>
                <Button fullWidth variant="outline" color="dark.3">
                {t("project.details.payment", lang)}
                </Button>
                <Button fullWidth variant="outline" color="dark.3">
                {t("project.details.international", lang)}
                </Button>
            </Stack>

            <Group justify="end" className="tw-mt-5">
                <Button
                    variant="light"
                    color="dark"
                    leftSection={<X weight="bold" />}
                    onClick={() => close("projectCompanyDetail")}
                >
                    {t("common.close", lang)}
                    </Button>
            </Group>
        </Modal>
    );
};

export default ProjectCompanyDetailModal;
