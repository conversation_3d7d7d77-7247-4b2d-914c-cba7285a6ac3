import mitt, {
    WildcardHandler,
    type Emitter,
    type EventType,
    type Handler,
} from "mitt";

import React from "react";

type Events = Record<BusEventType, unknown>;

export const EventBusContext = React.createContext<Emitter<Events> | null>(
    null
);

export function useEventBus() {
    const ctx = React.useContext(EventBusContext);
    if (!ctx) throw new Error("useEventBus: missing context");
    return ctx;
}

export function useListener(name: BusEventType, listener: Handler) {
    const bus = useEventBus();
    React.useEffect(() => {
        bus.on(name, listener);
        return () => {
            bus.off(name, listener);
        };
    }, [bus, name, listener]);
}

export function EventBusProvider({ children }: { children: React.ReactNode }) {
    const [bus] = React.useState(() => mitt<Record<BusEventType, unknown>>());
    return (
        <EventBusContext.Provider value={bus}>
            {children}
        </EventBusContext.Provider>
    );
}
