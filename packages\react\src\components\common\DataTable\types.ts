import { ColumnDef, SortingState, ColumnFiltersState } from "@tanstack/react-table";

export interface DataTableProps<T extends object> {
    columns: ColumnDef<T, any>[]; // 列定义
    data: T[]; // 数据
    totalCount: number; // 总条数
    enableMultiSelect?: boolean; // 是否启用多选
    selectionActions?: React.ReactNode; // 多选后操作组
    filterTypes?: Record<string, "text" | "select" | "numberRange" | "dateRange" | "datetimeRange">; // 筛选类型
    filterOptions?: Record<string, { label: string; value: any }[]>; // 筛选选项
    loading?: boolean; // 是否显示加载中
    onFetch: (params: {
        page: number;
        pageSize: number;
        sorting: SortingState;
        columnFilters: ColumnFiltersState;
        globalFilters: Record<string, any>;
    }) => Promise<void>; // 数据获取
    onExport?: () => Promise<void>; // 导出
    defaultDensity?: "xs" | "sm" | "md"; // 表格密度
    serverSideSort?: boolean; // 是否使用服务器端排序
    // table 顶部全局搜索字段
    globalFilterFields?: Array<{
        field: string;
        label: string;
        type?: "text" | "select" | "numberRange" | "date" | "dateRange";
        options?: Array<{ label: string; value: any }>;
    }>;
    getRowId?: (row: T) => string; // 获取行唯一标识的函数
}

export interface DataTableRef {
    // 刷新数据
    refresh: () => Promise<void>;
    // 获取表格状态（全局搜索、列筛选、排序）
    getState: () => {
        globalFilters: Record<string, any>;
        columnFilters: Array<{ id: string; value: any }>;
        sorting: Array<{ id: string; desc: boolean }>;
        pageSize: number;
    };
    // 获取选中的行
    getSelectedRows: () => any[];
}

export interface DragResult {
    destination?: {
        index: number;
    };
    source: {
        index: number;
    };
}

export const DENSITY_OPTIONS = [
    { value: "xs", label: "紧凑" },
    { value: "sm", label: "中等" },
    { value: "md", label: "宽松" },
] as const;

export type DensityType = (typeof DENSITY_OPTIONS)[number]["value"];

export const PAGE_SIZE_OPTIONS = [
    { value: "5", label: "5条/页" },
    { value: "10", label: "10条/页" },
    { value: "20", label: "20条/页" },
] as const;
