import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import useSettingStore from "@code.8cent/store/setting";
import { Group, Modal, Select, Stack, Button } from "@mantine/core";
import { Check, X } from "@phosphor-icons/react";
import { useMemoizedFn, useRequest } from "ahooks";
import dayjs from "dayjs";
import { useContext, useState } from "react";
import { useShallow } from "zustand/react/shallow";
import SettingPageContext from "../context";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
const SettingRegionModal = () => {
    const { timeFormats, timezones, languages, dateFormats, currencies } = useDataStore();

    const {
        setLang,
        lang,
        settingCurrency,
        settingTimeFormat,
        settingTimezone,
        settingDateFormat,
        settingLanguage,
        updateSetting,
    } = useSettingStore();

    const { SettingRegionModal: show, close } = useContext(SettingPageContext);

    const { run: updateUserSetting, loading: saving } = useRequest(
        async () => {
            const { result, error } = await cnaRequest(
                "/api/v1/admin/setting/setLangArea",
                "POST",
                JSON.stringify({
                    settingCurrency,
                    settingTimeFormat,
                    settingTimezone,
                    settingDateFormat,
                    settingLanguage,
                })
            );

            if (error) {
                return false;
            } else {
                return true;
            }
        },
        {
            manual: true,
            onFinally(params, updateResult) {
                if (updateResult === true) {
                    setLang(settingLanguage);
                    noty.success(
                        t("notification.user.settings", settingLanguage),
                        t("notification.settings.saved.success", settingLanguage)
                    );
                    close("SettingRegionModal");
                } else {
                    noty.error(
                        t("notification.user.settings", lang),
                        t("notification.settings.saved.fail", lang)
                    );
                }
            },
        }
    );

    return (
        <Modal
            opened={show}
            onClose={() => close("SettingRegionModal")}
            title={t("setting.lang_area", lang)}
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-6">
                <Select
                    data={languages.map((lang) => ({
                        value: String(lang.languageCode),
                        label: String(lang.languageType),
                    }))}
                    value={settingLanguage}
                    label={t("setting.lang_area.label.system_language", lang)}
                    onChange={(e) => {
                        updateSetting("settingLanguage", e as LangCode);
                    }}
                />
                <Select
                    searchable
                    data={timezones.map((timezone) => ({
                        value: String(timezone.timeZone),
                        label: `${timezone.timeZone.replace("_", " ")} ${timezone.timeZoneGMT}`,
                    }))}
                    value={settingTimezone}
                    label={t("setting.lang_area.label.timezone", lang)}
                    onChange={(e) => {
                        updateSetting("settingTimezone", e);
                    }}
                />
                <Select
                    data={dateFormats.map((format) => ({
                        value: String(format.dateFormat),
                        label: `${format.dateFormat} ${dayjs().format(format.dateFormat)}`,
                    }))}
                    value={settingDateFormat}
                    label={t("setting.lang_area.label.date_format", lang)}
                    onChange={(e) => {
                        updateSetting("settingDateFormat", e);
                    }}
                />
                <Select
                    data={timeFormats.map((format) => ({
                        value: String(format.timeFormat),
                        label: `${format.timeFormat} ${dayjs().format(format.timeFormat)}`,
                    }))}
                    value={settingTimeFormat}
                    label={t("setting.lang_area.label.time_format", lang)}
                    onChange={(e) => {
                        updateSetting("settingTimeFormat", e);
                    }}
                />
                <Select
                    searchable
                    data={currencies.map((currency) => ({
                        value: String(currency.currencyCode),
                        label: `${currency.currencyName} - ${currency.currencyCode}`,
                    }))}
                    value={settingCurrency}
                    label={t("setting.lang_area.label.currency", lang)}
                    onChange={(e) => {
                        updateSetting("settingCurrency", e);
                    }}
                />
            </Stack>
            <Group
                justify="end"
                className="tw-border-t tw-pt-4 tw-mt-4"
            >
                <CnaButton
                    onClick={updateUserSetting}
                    loading={saving}
                    color="basic"
                    leftSection={<Check weight="bold" />}
                >
                    {t("common.save", lang)}
                </CnaButton>
                <CnaButton
                    color="basic"
                    variant="outline"
                    onClick={() => close("SettingRegionModal")}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default SettingRegionModal;
