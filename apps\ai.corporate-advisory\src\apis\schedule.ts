import noty from "@code.8cent/react/noty";
import { cnaRequest } from "@code.8cent/utils";



const schedule = {
    list: async(start_time: number, end_time: number) => {
        const { result, error } = await cnaRequest<ScheduleItem[]>(
            "/api/v1/calendar/list",
            "GET",
            {
                start_time,
                end_time
            }
        );

        if(!error){
            return result.data;
        }else{
            return [];
        }
    },
    add: async (eventItem: ScheduleItem) => {
        const { error } = await cnaRequest(
            "/api/v1/calendar/add",
            "POST",
            eventItem
        );

        if(!error){
            return true;
        }else{
            noty.error(error.message)
            return false;
        }
    }
};

export default schedule;
