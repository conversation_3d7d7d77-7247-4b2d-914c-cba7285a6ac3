import currencyApi from "@/apis/station/currency";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Modal, Stack, TextInput, NumberInput, Switch } from "@mantine/core";
import { useState, useEffect } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";
import { useMemoizedFn } from "ahooks";
import { useShallow } from "zustand/react/shallow";

const currencySchema = z.object({
    currency: z.string().min(1, "请输入货币名称"),
    currency_code: z.string().min(1, "请输入货币代码"),
    rate: z.number().min(0, "汇率不能为负数").optional(),
    is_default: z.boolean().default(false),
});

type CurrencyForm = z.infer<typeof currencySchema>;

const CurrencyModal = ({ onUpdateSuccess = () => {} }: { onUpdateSuccess?: () => void }) => {
    const { lang } = useSettingStore();
    const [loading, setLoading] = useState(false);
    const openConfirm = useModalStore.use.openConfirm();

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.currencyModal,
            close: state.close,
        }))
    );

    const modalParams = useModalStore((state) => state.modalParams.currencyModal);
    const currency = modalParams?.currency || {};

    const {
        handleSubmit,
        formState: { errors },
        control,
        reset,
        watch,
    } = useForm<CurrencyForm>({
        defaultValues: {
            currency: currency?.currency || "",
            currency_code: currency?.currency_code || "",
            rate: currency?.rate || undefined,
            is_default: currency?.is_default === 1 || false,
        },
        resolver: zodResolver(currencySchema),
    });

    // 当 currency 变化时重置表单
    useEffect(() => {
        if (currency && Object.keys(currency).length > 0) {
            reset({
                currency: currency?.currency || "",
                currency_code: currency?.currency_code || "",
                rate: currency?.rate || undefined,
                is_default: currency?.is_default === 1 || false,
            });
        }
    }, [currency, reset]);

    const submitForm: SubmitHandler<CurrencyForm> = useMemoizedFn(async (data) => {
        const postData = {
            ...data,
            is_default: data.is_default ? 1 : 0,
        };

        setLoading(true);
        try {
            let res = false;
            if (currency?.id) {
                res = await currencyApi.update(currency.id, postData);
            } else {
                res = await currencyApi.store(postData);
            }
            if (res) {
                noty.success("操作成功");
                onUpdateSuccess();
                closeModal();
            }
        } catch (error) {
            noty.error("操作失败，请重试");
        } finally {
            setLoading(false);
        }
    });

    const handleSave = useMemoizedFn(() => {
        openConfirm({
            title: "提示",
            message: currency?.id ? "您确定更新货币汇率么？" : "您确定新增货币汇率么？",
            onConfirm: handleSubmit(submitForm),
        });
    });

    const closeModal = useMemoizedFn(() => {
        reset({
            currency: "",
            currency_code: "",
            rate: undefined,
            is_default: false,
        });
        close("currencyModal");
    });

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title={currency?.id ? "编辑货币汇率" : "新增货币汇率"}
            size="md"
        >
            <form
                onSubmit={handleSubmit(handleSave, (error) => {
                    console.log(error);
                })}
            >
                <Stack gap={"lg"}>
                    <Controller
                        control={control}
                        name="currency"
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="货币名称"
                                placeholder="请输入货币名称"
                                {...field}
                                error={errors.currency?.message}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="currency_code"
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="货币代码"
                                placeholder="请输入货币代码（如：USD、CNY）"
                                {...field}
                                error={errors.currency_code?.message}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="rate"
                        render={({ field }) => (
                            <NumberInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="汇率"
                                placeholder="请输入汇率"
                                min={0}
                                decimalScale={4}
                                {...field}
                                onChange={(value) => field.onChange(value)}
                                error={errors.rate?.message}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="is_default"
                        render={({ field }) => (
                            <Switch
                                label="设为默认货币"
                                description="设置为系统默认货币"
                                checked={field.value}
                                onChange={field.onChange}
                            />
                        )}
                    />

                    <ModalFooter
                        buttons={[
                            {
                                key: "save",
                                label: currency?.id ? "更新" : "保存",
                                style: "outline",
                                type: "submit",
                                loading: loading,
                                leftSection: <Check size={16} />,
                            },
                            {
                                key: "cancel",
                                label: "取消",
                                leftSection: <X size={16} />,
                                onClick: closeModal,
                            },
                        ]}
                    />
                </Stack>
            </form>
        </Modal>
    );
};

export default CurrencyModal;
