import api from "@/apis";
import SuccessTexts from "@/components/team/SuccessText";
import { t } from "@code.8cent/i18n";
import { CnaButton, PhoneInput } from "@code.8cent/react/components";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import noty from "@code.8cent/react/noty";
import useDataStore from "@code.8cent/store/data";
import { sleepMs } from "@code.8cent/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    BackgroundImage,
    Box,
    Center,
    Grid,
    Group,
    Overlay,
    ScrollArea,
    Image,
    Stack,
    Text,
    TextInput,
    Title,
    Input,
} from "@mantine/core";
import { CheckCircle, Download, Warning } from "@phosphor-icons/react";
import { useMemoizedFn, useRequest } from "ahooks";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { z } from "zod";

type TAddToTeamResult =
    | {
          status: "idle" | "ok";
      }
    | {
          status: "fail";
          message: string;
      };

const JoinTeamSchema = z.object({
    pre_phone: z
        .string()
        .regex(/^1[3-9]\d{9}$/g, "form.phone.number.incorrect"),
    phone: z.string().regex(/^1[3-9]\d{9}$/g, "form.phone.number.incorrect"),
    name: z.string().min(1, "form.enter.name"),
    email: z.string().email("form.email.incorrect"),
});

const TeamJoinPage = () => {
    const params = useParams<{ phone: string }>();

    const { filteredCountryDatas } = useDataStore();

    const navigate = useNavigate();

    const { openFileView } = useFileViewer();

    const [isLastSonLevel, setIsLastSonLevel] = useState<boolean>(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
        getValues,
        reset,
    } = useForm<TeamAddInput>({
        defaultValues: {
            pre_phone: params.phone,
            phone: "",
            name: "",
            email: "",
        },
        resolver: zodResolver(JoinTeamSchema),
    });

    const {
        run: addToTeam,
        data: addToTeamStatus = {
            status: "idle",
        },
    } = useRequest<TAddToTeamResult, [TeamAddInput]>(
        async (data) => {
            let addStatus = await api.team.add(data);

            reset();

            if (addStatus.error) {
                return {
                    status: "fail",
                    message: addStatus.error.message,
                };
            } else {
                return {
                    status: "ok",
                };
            }
        },
        {
            manual: true,
        }
    );

    const { run: handleFileButtonClick } = useRequest(
        async (type: 1 | 2 | 3 | 4) => {
            let token = await api.team.gerneateDocToken({
                type,
                phone: getValues("pre_phone"),
            });

            if (token) {
                window.open(
                    `${window.api_base_url}/api/v1/team/downloadDoc/${token}`,
                    "_blank"
                );
            }
        },
        {
            manual: true,
        }
    );

    const { data, loading } = useRequest(async () => {
        let { result, error } = await api.team.checkPhone(params.phone);

        return {
            info: result?.data,
            error,
        };
    });

    useEffect(() => {
        if (data?.info?.son_rank_id === 1) {
            setIsLastSonLevel(true);
        } else {
            setIsLastSonLevel(false);
        }
    }, [data]);

    if (loading === true) {
        return <Box className="tw-h-screen tw-w-screen" />;
    }

    if (data.error) {
        return (
            <Center className="team-page-container">
                <Stack className="tw-w-full" align="center" gap={0}>
                    <Warning size={150} weight="fill" color="#fff" />
                    <Box>
                        <Text ta="center" size="lg" c="#fff">
                            抱歉，此操作无法进行...
                        </Text>
                        <Text ta="center" size="lg" c="#fff">
                            {data.error.message}
                        </Text>
                    </Box>
                    <CnaButton
                        className="tw-mt-8 tw-min-w-[200px]"
                        variant="default"
                        onClick={() => navigate("/team", { replace: true })}
                    >
                        返回
                    </CnaButton>
                </Stack>
            </Center>
        );
    }

    if (addToTeamStatus.status === "fail") {
        return (
            <Center className="team-page-container">
                <Stack className="tw-w-full" align="center" gap={0}>
                    <Warning size={150} weight="fill" color="#fff" />
                    <Box>
                        <Text ta="center" size="lg" c="#fff">
                            抱歉，此操作无法进行...
                        </Text>
                        <Text ta="center" size="lg" c="#fff">
                            {addToTeamStatus.message}
                        </Text>
                    </Box>
                    <CnaButton
                        className="tw-mt-8 tw-min-w-[200px]"
                        variant="default"
                        onClick={() => navigate("/team", { replace: true })}
                    >
                        返回
                    </CnaButton>
                </Stack>
            </Center>
        );
    }

    if (addToTeamStatus.status === "ok") {
        return (
            <Center className="team-page-container">
                <Stack className="tw-w-full" align="center" gap={0}>
                    <CheckCircle size={150} weight="fill" color="#fff" />
                    <Box className="tw-w-[800px] tw-max-w-[95%]">
                        {SuccessTexts?.[`${data.info.son_rank_id}`]}
                    </Box>
                    <CnaButton
                        className="tw-mt-8 tw-min-w-[200px]"
                        variant="default"
                        onClick={() => navigate("/team", { replace: true })}
                    >
                        返回
                    </CnaButton>
                </Stack>
            </Center>
        );
    }

    return (
        <Box className="tw-h-screen tw-w-screen tw-bg-basic-5">
            <Grid
                className="tw-h-full md:tw-overflow-hidden"
                classNames={{ inner: "tw-h-full", col: "md:tw-h-full" }}
                gutter={0}
            >
                <Grid.Col span={{ base: 12, md: 5, lg: 4 }}>
                    <Center className="tw-w-full tw-h-full tw-py-5 md:tw-py-5">
                        <Box>
                            <Box className="tw-w-[200px] tw-h-[200px] tw-mx-auto tw-rounded-full tw-bg-secondary-5 tw-overflow-hidden">
                                <Title
                                    size={isLastSonLevel ? 32 : 120}
                                    c="basic"
                                    ta="center"
                                    className="tw-leading-[200px]"
                                >
                                    {data.info.son_rank_name}
                                </Title>
                            </Box>
                            {!isLastSonLevel && (
                                <Title
                                    ta="center"
                                    c="secondary"
                                    className="tw-mt-5"
                                    lts={10}
                                >
                                    三个{data.info.grandson_rank_name}
                                </Title>
                            )}
                        </Box>
                    </Center>
                </Grid.Col>
                <Grid.Col
                    span={{ base: 12, md: 7, lg: 8 }}
                    className="tw-bg-white"
                >
                    <ScrollArea
                        w="100%"
                        h="100%"
                        scrollbars="y"
                        scrollHideDelay={500}
                        scrollbarSize={5}
                        classNames={{
                            viewport: "[&>div]:tw-h-full [&>div]:!tw-block",
                        }}
                        component={"form"}
                        onSubmit={handleSubmit(addToTeam)}
                    >
                        <Stack
                            className="tw-py-5 !tw-max-w-[85%] tw-w-[600px] tw-mx-auto tw-min-h-full"
                            justify="center"
                        >
                            <TextInput
                                label={"推荐人"}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                value={data.info.name}
                                leftSection={
                                    <Text fw="bolder">
                                        {data.info.rank_name}
                                    </Text>
                                }
                                readOnly
                            />
                            <Input.Wrapper
                                label={"下载参照文件"}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                            >
                                <Group grow>
                                    <BackgroundImage
                                        src="/images/team/hire.png"
                                        radius="md"
                                        className="tw-cursor-pointer tw-select-none tw-bg-[length:200%] tw-overflow-hidden"
                                        onClick={() => {
                                            handleFileButtonClick(2);
                                        }}
                                    >
                                        <Center className="tw-py-10  tw-relative">
                                            <Text
                                                className="tw-z-[201]"
                                                c="white"
                                            >
                                                <Download
                                                    size={24}
                                                    className="tw-mr-3 tw-inline-block"
                                                    stroke="#fff"
                                                />
                                                <Text span>招募合伙人</Text>
                                            </Text>
                                            <Overlay bg="basic" opacity={0.5} />
                                        </Center>
                                    </BackgroundImage>
                                    {!isLastSonLevel && (
                                        <BackgroundImage
                                            src="/images/team/create.png"
                                            radius="md"
                                            className="tw-cursor-pointer tw-select-none tw-bg-[length:200%] tw-overflow-hidden"
                                            onClick={() => {
                                                handleFileButtonClick(4);
                                            }}
                                        >
                                            <Center className="tw-py-10  tw-relative">
                                                <Text
                                                    className="tw-z-[201]"
                                                    c="white"
                                                >
                                                    <Download
                                                        size={24}
                                                        className="tw-mr-3 tw-inline-block"
                                                        stroke="#fff"
                                                    />
                                                    <Text span>
                                                        组建合伙人团队
                                                    </Text>
                                                </Text>
                                                <Overlay
                                                    bg="basic"
                                                    opacity={0.5}
                                                />
                                            </Center>
                                        </BackgroundImage>
                                    )}
                                </Group>
                                <Group className="tw-mt-3" grow>
                                    <BackgroundImage
                                        src="/images/team/hire.png"
                                        radius="md"
                                        className="tw-cursor-pointer tw-select-none tw-bg-[length:200%] tw-overflow-hidden"
                                        onClick={() => {
                                            handleFileButtonClick(1);
                                        }}
                                    >
                                        <Center className="tw-py-10  tw-relative">
                                            <Text
                                                className="tw-z-[201]"
                                                c="white"
                                            >
                                                <Download
                                                    size={24}
                                                    className="tw-mr-3 tw-inline-block"
                                                    stroke="#fff"
                                                />
                                                <Text span>C&A简介PPT</Text>
                                            </Text>
                                            <Overlay bg="basic" opacity={0.5} />
                                        </Center>
                                    </BackgroundImage>
                                    <BackgroundImage
                                        src="/images/team/create.png"
                                        radius="md"
                                        className="tw-cursor-pointer tw-select-none tw-bg-[length:200%] tw-overflow-hidden"
                                        onClick={() => {
                                            handleFileButtonClick(3);
                                        }}
                                    >
                                        <Center className="tw-py-10  tw-relative">
                                            <Text
                                                className="tw-z-[201]"
                                                c="white"
                                            >
                                                <Download
                                                    size={24}
                                                    className="tw-mr-3 tw-inline-block"
                                                    stroke="#fff"
                                                />
                                                <Text span>
                                                    绿智地球业务PPT
                                                </Text>
                                            </Text>
                                            <Overlay bg="basic" opacity={0.5} />
                                        </Center>
                                    </BackgroundImage>
                                </Group>
                            </Input.Wrapper>
                            <TextInput
                                label={t(
                                    "introduction.label.last_name",
                                    window.app_prefer_lang
                                )}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                {...register("name")}
                                error={
                                    errors.name
                                        ? t(
                                              errors.name.message,
                                              window.app_prefer_lang
                                          )
                                        : false
                                }
                            />
                            <PhoneInput
                                wrapperProps={{
                                    label: t(
                                        "introduction.label.phone",
                                        window.app_prefer_lang
                                    ),
                                    labelProps: {
                                        className: "profile-form-label",
                                    },
                                }}
                                data={filteredCountryDatas()}
                                prefixLabelKey="countryCode"
                                prefixFlagKey="countryISOCode2"
                                prefixValueKey="countryID"
                                prefixProps={{
                                    w: 120,
                                    value: window.team_prefix_id,
                                    readOnly: true,
                                }}
                                inputProps={{
                                    ...register("phone"),
                                    error: errors.phone
                                        ? t(
                                              errors.phone.message,
                                              window.app_prefer_lang
                                          )
                                        : false,
                                }}
                            />
                            <TextInput
                                label={t(
                                    "form.email.address",
                                    window.app_prefer_lang
                                )}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                {...register("email")}
                                error={
                                    errors.email
                                        ? t(
                                              errors.email.message,
                                              window.app_prefer_lang
                                          )
                                        : false
                                }
                            />
                            <Group className="tw-mt-5">
                                <CnaButton
                                    size="md"
                                    color="basic"
                                    fullWidth
                                    type="submit"
                                >
                                    提交
                                </CnaButton>
                            </Group>
                        </Stack>
                    </ScrollArea>
                </Grid.Col>
            </Grid>
        </Box>
    );
};

export default TeamJoinPage;
