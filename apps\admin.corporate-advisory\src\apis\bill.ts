import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const bill = {
    list: async (params: TBillSearchParams) => {
        const { error, result } = await cnaRequest<TBillsResponse>(
            "/api/v1/admin/bills/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 合伙人付款记录
    associatePayout: async (params) => {
        const { error, result } = await cnaRequest<any>(
            "/api/v1/admin/partner/payout",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default bill;
