import api from "@/apis";
import CnaButton from "@code.8cent/react/components/CnaButton";
import useModalStore from "@/store/modal";
import useSettingStore from "@code.8cent/store/setting";
import { useEventBus } from "@/utils/eventBus";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    Button,
    Group,
    Input,
    Modal,
    Select,
    Stack,
    TextInput,
} from "@mantine/core";
import {
    useGetState,
    useMemoizedFn,
    useRequest,
    useUnmount,
    useUpdateEffect,
} from "ahooks";
import { useForm, SubmitHandler } from "react-hook-form";
import { z } from "zod";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import PhoneInput from "@code.8cent/react/components/PhoneInput";
import noty from "@code.8cent/react/noty";
import { CountrySelect } from "@code.8cent/react/components";

// Define the type for the form values
type ProjectCompanyCreateParams = {
    projectID: string;
    companyName: string;
    companyRegisterCode: string;
    companyCategoriesID: string;
    countryID: string;
    email: string;
    mobilePrefixID: string;
    phone: string;
};

// Create the validation schema using Zod
const createCompanySchema = (lang) =>
    z.object({
        projectID: z.string().min(1, {
            message: `${t("introduction.label.select", lang)} ${t(
                "project.add.title",
                lang
            )}`,
        }),
        companyName: z.string().min(1, {
            message: `${t("project.fill", lang)} ${t(
                "project.add.company",
                lang
            )}`,
        }),
        companyRegisterCode: z.string().min(1, {
            message: `${t("project.fill", lang)} ${t(
                "project.add.register",
                lang
            )}`,
        }),
        companyCategoriesID: z.string().min(1, {
            message: `${t("introduction.label.select", lang)} ${t(
                "project.add.type",
                lang
            )}`,
        }),
        countryID: z.string().min(1, {
            message: `${t("introduction.label.select", lang)} ${t(
                "project.add.nationality",
                lang
            )}`,
        }),
        email: z.string().email({
            message: `${t("project.fill", lang)} ${t(
                "project.add.email",
                lang
            )}`,
        }),
        mobilePrefixID: z.string().min(1, {
            message: `${t("introduction.label.select", lang)} ${t(
                "project.add.phone",
                lang
            )}`,
        }),
        phone: z.string().min(1, {
            message: `${t("project.fill", lang)} ${t(
                "project.add.phone",
                lang
            )}`,
        }),
    });

// Initial form values
const initialCreateCompanyFormValues: ProjectCompanyCreateParams = {
    projectID: "",
    companyName: "",
    companyRegisterCode: "",
    companyCategoriesID: "",
    countryID: "",
    email: "",
    mobilePrefixID: "",
    phone: "",
};

const ProjectCreateCompanyModal = () => {
    const [creating, setCreating, getCreating] = useGetState<boolean>(false);
    const filteredCountryDatas = useDataStore.use.filteredCountryDatas();
    const lang = useSettingStore.use.lang();
    const opened = useModalStore.use.projectCompanyCreate();
    const close = useModalStore.use.close();
    const bus = useEventBus();

    const { data: { companyCategories = [], projectList = [] } = {} } =
        useRequest(
            async () => {
                const [companyCategories, projectList] = await Promise.all([
                    api.gernal.getCompanyCategoryList(),
                    api.gernal.getProjectSelectList(),
                ]);
                return {
                    companyCategories,
                    projectList,
                };
            },
            {
                ready: opened,
            }
        );

    const {
        register,
        setValue,
        handleSubmit,
        formState: { errors, isValid },
        reset,
        clearErrors,
        getValues,
    } = useForm<ProjectCompanyCreateParams>({
        defaultValues: initialCreateCompanyFormValues,
        resolver: zodResolver(createCompanySchema(lang)),
        mode: "all",
    });

    const createProjectCompany: SubmitHandler<ProjectCompanyCreateParams> =
        useMemoizedFn(async (data) => {
            if (getCreating()) return;

            setCreating(true);
            const res = await api.project.createProjectCompany(data);
            setCreating(false);

            if (res === true) {
                noty.success(
                    t("project.success", lang),
                    t("project.success.details", lang)
                );
                reset();
                close("projectCompanyCreate");
                bus.emit("project.list.refresh");
            } else {
                noty.error(
                    t("project.fail", lang),
                    t("project.fail.details", lang)
                );
            }
        });

    useUpdateEffect(() => {
        if (!opened) reset();
    }, [opened]);

    // Helper function to handle select changes
    const handleSelectChange =
        (fieldName: keyof ProjectCompanyCreateParams) => (value: string) => {
            clearErrors(fieldName);
            setValue(fieldName, value);
        };

    return (
        <Modal
            size="lg"
            closeOnClickOutside={false}
            opened={opened}
            onClose={() => close("projectCompanyCreate")}
            title={t("project.add.title", lang)}
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-my-5" gap={10}>
                <Select
                    label={t("project.add.name", lang)}
                    allowDeselect={false}
                    {...register("projectID")}
                    data={
                        projectList?.map?.((project) => ({
                            label: project[`projectCategoriesName${lang}`] ?? "",
                            value: `${project.projectCategoriesID}`,
                        })) || []
                    }
                    onChange={handleSelectChange("projectID")}
                    error={errors.projectID?.message}
                />
                <TextInput
                    label={t("project.add.company", lang)}
                    {...register("companyName")}
                    error={errors.companyName?.message}
                />
                <TextInput
                    label={t("project.add.register", lang)}
                    {...register("companyRegisterCode")}
                    error={errors.companyRegisterCode?.message}
                />
                <Select
                    label={t("project.add.type", lang)}
                    allowDeselect={false}
                    {...register("companyCategoriesID")}
                    data={
                        companyCategories?.map?.((item) => ({
                            label: item[`companyCategoriesName${lang}`] ?? "",
                            value: `${item.companyCategoriesID}`,
                        })) || []
                    }
                    onChange={handleSelectChange("companyCategoriesID")}
                    error={errors.companyCategoriesID?.message}
                />

                <CountrySelect<CountryDataItem>
                    labelKey={`country${lang}` as keyof CountryDataItem}
                    valueKey="countryID"
                    data={filteredCountryDatas()}
                    flagKey="countryISOCode2"
                    label={t("project.add.nationality", lang)}
                    {...register("countryID")}
                    value={getValues("countryID")}
                    onChange={handleSelectChange("countryID")}
                    error={errors.countryID?.message}
                />

                <TextInput
                    label={t("project.add.email", lang)}
                    {...register("email")}
                    error={errors.email?.message}
                />

                <PhoneInput<CountryDataItem>
                    label={t("project.add.phone", lang)}
                    prefixFlagKey="countryISOCode2"
                    prefixLabelKey="countryCode"
                    prefixValueKey="countryID"
                    data={filteredCountryDatas()}
                    prefixProps={{
                        w: 120,
                        allowDeselect: false,
                        searchable: true,
                        ...register("mobilePrefixID"),
                        onChange: handleSelectChange("mobilePrefixID"),
                        value: getValues("mobilePrefixID"),
                        error: errors.mobilePrefixID ? true : false,
                    }}
                    inputProps={{
                        ...register("phone"),
                        error: errors.phone ? true : false,
                    }}
                />
            </Stack>
            <Group justify="end">
                <CnaButton
                    onClick={handleSubmit(createProjectCompany)}
                    loading={creating}
                    color="basic"
                    // disabled={!isValid}
                >
                    {t("common.create", lang)}
                </CnaButton>
                <Button
                    variant="default"
                    onClick={() => close("projectCompanyCreate")}
                >
                    {t("common.close", lang)}
                </Button>
            </Group>
        </Modal>
    );
};

export default ProjectCreateCompanyModal;
