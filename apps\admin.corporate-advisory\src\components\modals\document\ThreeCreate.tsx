// export default ThreeCreate;
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import {
    Group,
    Modal,
    Stack,
    TextInput,
    Textarea,
    Text,
    Select,
    Input,
    Tooltip,
    Divider,
    Radio,
    NumberInput,
} from "@mantine/core";
import { useEffect, useState } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm, Controller } from "react-hook-form";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Dropzone, MIME_TYPES } from "@mantine/dropzone";
import { FileText, Eye, DownloadSimple, CalendarBlank, Check, X } from "@phosphor-icons/react";
import { useMemoizedFn, useRequest } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { cnaRequest } from "@code.8cent/utils";
import { useShallow } from "zustand/react/shallow";
import dayjs from "dayjs";

const threeSchema = z
    .object({
        level: z.string().min(1, "请输入文档等级"),
        file_version: z.string().min(1, "请输入文档版本"),
        file: z
            .any()
            .optional()
            .refine((file) => file.length == 0 || file instanceof File, "请选择上传文件")
            .refine(
                (file) =>
                    file.length == 0 ||
                    (file.size <= 1024 * 1024 * 10 &&
                        [MIME_TYPES.pdf, MIME_TYPES.doc, MIME_TYPES.docx].includes(file.type)),
                "文件必须是PDF、Word文档，且大小不能超过10MB"
            ),
        // documentVersionID: z
        //     .number()
        //     .optional()
        //     .refine((v) => v === undefined || v === 0 || typeof v === "number", "请选择版本"),
    })
    .refine(
        (data) => {
            // If a version is selected, file is not required
            // if (data.documentVersionID && data.documentVersionID !== 0) {
            //     return true;
            // }
            if(data.file_version){
                return true;
            }
            // If no version is selected, file is required
            return data.file.length != 0;
        },
        {
            message: "请选择文件",
            path: ["file"],
        }
    );

type ThreeForm = z.infer<typeof threeSchema>

const Create = ({ onUpdateSuccess = () => {} }: { onUpdateSuccess?: () => void }) => {
    const { lang } = useSettingStore();
    const [loading, setLoading] = useState(false);
    const [files, setFiles] = useState<File[]>([]);

    const openConfirm = useModalStore.use.openConfirm();
    const { openFileView } = useFileViewer();

    const modalParams = useModalStore((state) => state.modalParams.threeCreateModal);
    const threeList = modalParams?.threeList;
    const three = modalParams?.three;

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.threeCreateModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        // 清空表单数据
        setValue("level", "");
        setValue("file_version", "");
        setValue("file", []);
        reset();
        setFiles([]);
        close("threeCreateModal");
    });

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
        setValue,
        control,
    } = useForm<ThreeForm>({
        defaultValues: {
            level: three?.level !== null ? String(three?.level) : "",
            file_version: three?.file_version || dayjs().format("YYYY-MM-DD"),
        },
        resolver: zodResolver(threeSchema),
    });

    useEffect(() => {
        if (three) {
            setValue(
                "level",
                three?.level !== null ? String(three?.level) : ""
            );
            setValue("file_version", three.file_version || dayjs().format("YYYY-MM-DD"));

            if(three.file) {
                getDocumentFile(three.id);
            }
        }
    }, [three, setValue]);

    const submitForm: SubmitHandler<ThreeForm> = useMemoizedFn(async (data) => {
        console.log("🚀 ~ Create.tsx:158 ~ Create ~ data:", data)
        setLoading(true);
        try {
            const payload: any = {
                level: data.level,
                file_version: data.file_version,
                file: null
            };

            if (data.file.length != 0) {
                payload.file = data.file;
            } else {
                // payload.versionid = data.documentVersionID;
            }

            let res: Boolean;
            // if (three?.documentID) {
            //     res = await api.document.update(three.documentID, payload);
            // } else {
            //     res = await api.document.store(payload);
            // }
            res = await api.document.three.store(payload);

            if (res) {
                noty.success("操作成功");
                onUpdateSuccess();
                closeModal();
            }
        } catch (error) {
            noty.error("操作失败");
            console.error(error);
        } finally {
            setLoading(false);
        }
    });

    const handleSave = useMemoizedFn(() => {
        openConfirm({
            title: "提示",
            message: "您确定更新的文档信息么？",
            onConfirm: handleSubmit(submitForm),
        });
    });

    const actionText = three?.documentTitle ? "更新" : "创建";

    const { run: getDocumentFile } = useRequest(
        async (id: number) => {
            let response = await cnaRequest(
                `/api/v1/admin/desk/three/getFile`,
                "POST",
                {
                    id,
                },
                // 指定响应类型为 Blob
                { responseType: "blob" }
            );

            if (!response.error) {
                const blob = response.result?.data;
                // 将Blob转换为File对象
                const res = new File([blob], `${three.job}_${three.file_version}.pdf`, { type: blob.type || 'application/pdf' });
                console.log('xx', blob, res)
                
                setValue('file', res);
                setFiles([res]);
            } else {
                noty.error(response.error.message);
            }
        },
        {
            manual: true,
        }
    );

    const previewDocumentFile = () => {
        openFileView(files[0], {
            title: t('查看文档', lang),
        });
    }

    const downloadDocumentFile = () => {
        if (files[0]) {
            const url = URL.createObjectURL(files[0]);
            const a = document.createElement('a');
            a.href = url;
            a.download = files[0].name;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }
    
    // const { run: previewDocumentFile } = useRequest(
    //     async (id: number, label: string) => {
    //         let response = await cnaRequest(
    //             `/api/v1/admin/desk/three/getFile`,
    //             "POST",
    //             {
    //                 id,
    //             },
    //             // 指定响应类型为 Blob
    //             { responseType: "blob" }
    //         );

    //         if (!response.error) {
    //             const blob = response.result?.data;
    //             // 将Blob转换为File对象
    //             const res = new File([blob], `document_${id}.pdf`, { type: blob.type || 'application/pdf' });
    //             console.log('xxx', res, res instanceof File === true)
    //             openFileView(res, {
    //                 title: t(label, lang),
    //             });
    //         } else {
    //             noty.error(response.error.message);
    //         }
    //     },
    //     {
    //         manual: true,
    //     }
    // );

    // const { run: downloadDocumentFile } = useRequest(
    //     async (id: number) => {
    //         let response = await cnaRequest(
    //             `/api/v1/admin/desk/three/getFile`,
    //             "POST",
    //             {
    //                 id,
    //             },
    //             // 指定响应类型为 Blob
    //             { responseType: "blob" }
    //         );

    //         if (!response.error) {
    //             const fileToken = response.result.data;
    //             window.open(
    //                 `${window.api_base_url}/api/v1/admin/documents/download/${fileToken}`,
    //                 "_blank"
    //             );
    //         } else {
    //             noty.error(response.error.message);
    //         }
    //     },
    //     {
    //         manual: true,
    //     }
    // );

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title={actionText + "文档资料"}
            size="xl"
        >
            <form onSubmit={handleSubmit(handleSave, (errors) => console.log(errors))}>
                <Stack gap={"lg"}>
                    <Controller
                        control={control}
                        name="level"
                        render={({ field: { onChange, value, ...field } }) => (
                            <Select
                                {...field}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="级别"
                                placeholder="请选择级别"
                                data={threeList?.map((item) => ({
                                    value: item.level.toString(),
                                    label: item.job,
                                }))}
                                disabled={three?.level}
                                onChange={(value) => onChange(value ? value : 0)}
                                value={value ? String(value) : null}
                                error={errors.level?.message}
                            />
                        )}
                    />
                    <Controller
                        name="file_version"
                        control={control}
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="文档版本"
                                placeholder="请输入文档版本"
                                {...field}
                                error={errors.file_version?.message}
                            />
                        )}
                    />
                    <Input.Wrapper
                        labelProps={{
                            className: "profile-form-label",
                        }}
                        label="文档附件"
                        {...register("file")}
                    >
                        <Dropzone
                            onDrop={(files: File[]) => {
                                console.log("🚀 ~ ThreeCreate.tsx:502 ~ files:", files)
                                const file = files[0];
                                console.log("🚀 ~ ThreeCreate.tsx:504 ~ file:", file, typeof file)
                                // 限制文件大小
                                if (file.size > 1024 * 1024 * 10) {
                                    noty.error("文件大小不能超过10MB");
                                    return;
                                }
                                setValue("file", file);
                                setFiles(files);
                            }}
                            multiple={false}
                            accept={[MIME_TYPES.pdf, MIME_TYPES.doc, MIME_TYPES.docx]}
                        >
                            <Stack className="tw-flex tw-items-center">
                                <FileText
                                    size={36}
                                    className="tw-text-dimmed tw-mx-auto tw-mb-3"
                                />
                                <Text
                                    size="sm"
                                    c="dimmed"
                                >
                                    {files[0] ? files[0].name : t("upload.files", lang)}
                                </Text>
                                {files[0] && <Group
                                    style={{ pointerEvents: 'auto' }}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                    }}
                                >
                                                    <Tooltip label="查看">
                                                        <div
                                                            style={{ pointerEvents: 'auto' }}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                e.preventDefault();
                                                                previewDocumentFile();
                                                            }}
                                                        >
                                                            <Eye
                                                                size={18}
                                                                className="tw-cursor-pointer"
                                                                color="#868e96"
                                                            />
                                                        </div>
                                                    </Tooltip>
                                                    <Divider orientation="vertical" />
                                                    <Tooltip label="下载">
                                                        <div
                                                            style={{ pointerEvents: 'auto' }}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                e.preventDefault();
                                                                downloadDocumentFile();
                                                            }}
                                                        >
                                                            <DownloadSimple
                                                                size={18}
                                                                className="tw-cursor-pointer"
                                                                color="#868e96"
                                                            />
                                                        </div>
                                                    </Tooltip>
                                                </Group>}
                            </Stack>
                        </Dropzone>
                        {errors?.file && (
                            <Input.Error>
                                {typeof errors.file.message === "string"
                                    ? errors.file.message
                                    : String(errors.file.message)}
                            </Input.Error>
                        )}
                    </Input.Wrapper>
                    {/* {three?.allFiles?.length > 0 && (
                        <Stack gap="0">
                            <Controller
                                name="documentVersionID"
                                control={control}
                                render={({ field }) => (
                                    <Radio.Group
                                        {...field}
                                        value={field.value?.toString()}
                                        onChange={(value) => field.onChange(parseInt(value, 10))}
                                        label="文档版本"
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                    >
                                        {three?.allFiles.map((version, index) => (
                                            <Group
                                                key={index}
                                                className="tw-border tw-border-solid tw-border-gray-300 tw-rounded-md tw-p-2 tw-mb-2"
                                                justify="space-between"
                                                align="center"
                                            >
                                                <Radio
                                                    value={version.documentXID.toString()}
                                                    label={
                                                        version.file_version +
                                                        (version.isShow ? "（当前版本）" : "")
                                                    }
                                                />
                                                
                                            </Group>
                                        ))}
                                    </Radio.Group>
                                )}
                            />
                        </Stack>
                    )} */}

                    <ModalFooter
                        buttons={[
                            {
                                key: "submit",
                                label: actionText,
                                style: "outline",
                                type: "submit",
                                loading: loading,
                                leftSection: <Check size={16} />,
                            },
                            {
                                key: "cancel",
                                label: "取消",
                                leftSection: <X size={16} />,
                                onClick: closeModal,
                            },
                        ]}
                    />
                </Stack>
            </form>
        </Modal>
    );
};

export default Create;
