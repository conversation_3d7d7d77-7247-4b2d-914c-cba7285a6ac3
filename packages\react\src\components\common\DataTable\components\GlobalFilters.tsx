import { Stack, Grid, Group, NumberInput, Select, TextInput } from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";

export function GlobalFilters({ globalFilterFields, localGlobalFilters, setLocalGlobalFilters }) {
    return (
        <Stack className="tw-overflow-hidden">
            <Grid gutter={10}>
                {globalFilterFields.map(({ field, label, type = "", options = [] }) => (
                    <Grid.Col
                        key={field}
                        span={{ base: 12, md: 3 }}
                    >
                        {type === "text" && (
                            <TextInput
                                size="xs"
                                placeholder={`${label}...`}
                                value={localGlobalFilters[field] || ""}
                                onChange={(e) =>
                                    setLocalGlobalFilters((prev) => ({
                                        ...prev,
                                        [field]: e.target.value || undefined,
                                    }))
                                }
                            />
                        )}
                        {type === "select" && (
                            <Select
                                size="xs"
                                placeholder={`选择 ${label}`}
                                data={options}
                                value={localGlobalFilters[field] || ""}
                                onChange={(value) =>
                                    setLocalGlobalFilters((prev) => ({
                                        ...prev,
                                        [field]: value || undefined,
                                    }))
                                }
                                clearable
                                searchable
                            />
                        )}
                        {type === "numberRange" && (
                            <Group
                                gap="xs"
                                grow
                            >
                                <NumberInput
                                    size="xs"
                                    placeholder="最小值"
                                    value={(localGlobalFilters[field] || [])[0] ?? ""}
                                    onChange={(value) =>
                                        setLocalGlobalFilters((prev) => ({
                                            ...prev,
                                            [field]: [value, (prev[field] || [])[1]],
                                        }))
                                    }
                                    hideControls
                                />
                                <NumberInput
                                    size="xs"
                                    placeholder="最大值"
                                    value={(localGlobalFilters[field] || [])[1] ?? ""}
                                    onChange={(value) =>
                                        setLocalGlobalFilters((prev) => ({
                                            ...prev,
                                            [field]: [(prev[field] || [])[0], value],
                                        }))
                                    }
                                    hideControls
                                />
                            </Group>
                        )}
                        {type === "date" && (
                            <DatePickerInput
                                size="xs"
                                placeholder="选择日期"
                                valueFormat="YYYY-MM-DD"
                                value={localGlobalFilters[field] || null}
                                onChange={(value) =>
                                    setLocalGlobalFilters((prev) => ({
                                        ...prev,
                                        [field]: value || undefined,
                                    }))
                                }
                            />
                        )}
                        {type === "dateRange" && (
                            <DatePickerInput
                                type="range"
                                allowSingleDateInRange
                                size="xs"
                                clearable
                                placeholder={`选择${label}范围`}
                                valueFormat="YYYY-MM-DD"
                                value={localGlobalFilters[field] || [null, null]}
                                onChange={(value) =>
                                    setLocalGlobalFilters((prev) => ({
                                        ...prev,
                                        [field]: value || undefined,
                                    }))
                                }
                            />
                        )}
                    </Grid.Col>
                ))}
            </Grid>
        </Stack>
    );
}
