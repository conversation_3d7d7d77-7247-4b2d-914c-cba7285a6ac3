import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const emailTemplate = {
    list: async () => {
        const { error, result } = await cnaRequest<TEmailTemplateResponse>(
            "/api/v1/admin/emailTemplate/index",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    detail: async (id: number) => {
        const { error, result } = await cnaRequest<TEmailTemplate>(
            `/api/v1/admin/emailTemplate/edit/${id}`,
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    update: async (params: TEmailTemplateUpdateParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/emailTemplate/edit",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default emailTemplate;
