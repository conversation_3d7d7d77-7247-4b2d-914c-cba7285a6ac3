{"name": "@code.8cent/react", "version": "0.0.0", "private": true, "sideEffects": ["**/*.css"], "files": ["dist"], "exports": {"./components": "./src/components/index.ts", "./components/CnaButton": "./src/components/common/CnaButton.tsx", "./components/AuthFormCard": "./src/components/common/AuthFormCard.tsx", "./components/RouteError": "./src/components/common/RouteError.tsx", "./components/Offline": "./src/components/common/Offline.tsx", "./components/ScrollArea": "./src/components/common/ScrollArea.tsx", "./components/ProfileAvatar": "./src/components/common/ProfileAvatar.tsx", "./components/FlagComponent": "./src/components/common/FlagComponent/index.tsx", "./components/PageHeader": "./src/components/common/PageHeader.tsx", "./components/PhoneInput": "./src/components/inputs/PhoneInput.tsx", "./components/CountrySelect": "./src/components/inputs/CountrySelect.tsx", "./components/AddressInput": "./src/components/inputs/AddressInput.tsx", "./components/PasswordCheckerInput": "./src/components/inputs/PasswordCheckerInput.tsx", "./components/PasswordMatcherInput": "./src/components/inputs/PasswordMatcherInput.tsx", "./components/PasswordResetForm": "./src/components/forms/PasswordResetForm.tsx", "./components/PasswordResetFormNoCurrent": "./src/components/forms/PasswordResetFormNoCurrent.tsx", "./hoc/withRouteGuard": "./src/hoc/withRouteGuard.tsx", "./layouts": "./src/layouts/index.ts", "./layouts/AuthenticationLayout": "./src/layouts/AuthenticationLayout/index.tsx", "./pages": "./src/pages/index.ts", "./pages/LoginPage": "./src/pages/LoginPage/index.tsx", "./pages/ForgetPasswordPage": "./src/pages/ForgetPasswordPage/index.tsx", "./pages/SettingPage": "./src/pages/Setting/index.tsx", "./hooks": "./src/hooks/index.ts", "./hooks/useaReachedBottom": "./src/hooks/useaReachedBottom.tsx", "./hooks/useResetPasswordModal": "./src/hooks/useResetPasswordModal.tsx", "./noty": "./src/library/noty/index.ts", "./FileViewer": "./src/library/FileViewer/index.ts", "./VerifyModal": "./src/modals/VerifyModal/index.ts", "./SignatureModal": "./src/modals/SignatureModal/index.ts", "./RouterStacks": "./src/library/RouterStacks/index.ts"}, "type": "module"}