import { PageHeader } from "@code.8cent/react/components";
import { Stack, Box } from "@mantine/core";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import InvitePartner from "@/components/wizard/InvitePartner";

const JoinPage = () => {
    const lang = useSettingStore.use.lang();
    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0 ">
            {/* 晋级管理合伙人 */}
            <PageHeader
                // title={t("billing.title", lang)}
                title={"晋级管理合伙人"}
                // desc={t("navigation.billing.system.quote", lang)}
                desc={
                    "此版块展示晋级合伙人的专属链接，可便捷分享给联盟成员，协助邀请加入，共同提升团队影响力。借助分享链接，晋级合伙人能吸纳更多成员，不断壮大团队规模，推动业务增长与高效协作。"
                }
            />
            <Box
                className="tw-shadow-lg tw-border 
            tw-px-6 tw-rounded-sm  tw-mx-auto tw-p-6 tw-mt-2  tw-w-full lg:tw-w-3/5"
            >
                <InvitePartner />
            </Box>
        </Stack>
    );
};

export default JoinPage;
