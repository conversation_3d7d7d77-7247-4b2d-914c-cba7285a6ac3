declare global {
    type TCurrency = {
        id: string;
        currency: string;
        currency_code: string;
        is_default: number;
        rate?: number;
    };

    type TCurrencySearchParams = {
        is_default?: number;
        rate?: number;
    } & TPageQueryParams;

    type TCurrencyResponse = {
        items: TCurrency[];
        paginate: BasePaginateResponse;
    };

    type TCurrencyStoreParams = Omit<TCurrency, "id" | "is_default">;

    type TCurrencyUpdateParams = Omit<TCurrency, "id">;
}

export {};
