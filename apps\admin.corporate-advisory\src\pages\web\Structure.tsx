import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Plus } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import StructureInfo from "./modals/StructureInfo";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";

const columnHelper = createColumnHelper<TWebStructure>();

const WebStructurePage = () => {
    const lang = useSettingStore.use.lang();

    const openModal = useModalStore.use.open();

    const tableRef = useRef<DataTableRef>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const pageButtons = [
        {
            key: "add",
            label: "新增",
            leftSection: <Plus size={14} />,
            onClick: () => openModal("webStructureModal"),
        },
    ];

    const tableColumns = [
        columnHelper.accessor("structureID", {
            header: "序号",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("structureCode", {
            header: "结构码",
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("structureZH", {
            header: "结构中文名",
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("structureZT", {
            header: "结构繁体名",
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("structureEN", {
            header: "结构英文名",
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("structureMS", {
            header: "结构马来文名",
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        }),
    ];

    const rowActions = (row) => [
        {
            key: "edit",
            label: "编辑",
            onClick: () => openModal("webStructureModal", { row }),
        },
    ];

    const handleFetch = useMemoizedFn(async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.web.structureList(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    });

    const refreshData = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="网页结构翻译"
                desc="查询网页结构翻译"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
            />

            <StructureInfo onUpdateSuccess={refreshData} />
        </Stack>
    );
};

export default WebStructurePage;
