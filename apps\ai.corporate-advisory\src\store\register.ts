import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";

export type RegisterInfoState = {
    email?: string;
    phone?: string;
    prefixID?: string;
    token?: string;
    refer?: string;
    nationalityID?: string;
};

type RegisterInfoAction = {
    setRegisterInfoValue: (
        key: keyof RegisterInfoState,
        value: number | string | boolean
    ) => void;
    setRegisterInfo: (user: RegisterInfoState) => void;
};

type RegisterInfoStateAndAction = RegisterInfoState & RegisterInfoAction;

const baseRegisterStore = create<RegisterInfoStateAndAction>()(
    devtools(
        (set) => ({
            email: "",
            phone: "",
            prefixID: "",
            token: "",
            refer: "",
            nationalityID: "",
            setRegisterInfoValue(key, value) {
                set((state) => ({
                    ...state,
                    [key]: value,
                }));
            },

            setRegisterInfo(user) {
                set((state) => ({
                    ...state,
                    ...user,
                }));
            },
        }),
        {
            name: "register-store",
        }
    )
);

const useRegisterStore = createSelectors(baseRegisterStore);

export default useRegisterStore;
