import api from "@/apis";
import useRegisterStore from "@/store/register";
import { CnaButton } from "@code.8cent/react/components";
import { AuthenticationLayout } from "@code.8cent/react/layouts";
import { cnaRequest } from "@code.8cent/utils";
import { Box, Stack, Text, Image } from "@mantine/core";
import { useMemoizedFn, useRequest } from "ahooks";
import { useNavigate } from "react-router-dom";
import fileNameMappings from "./RegisterV2/fileNameMappings";
import { useFileViewer } from "@code.8cent/react/FileViewer";

const RegisterDocuments = () => {
    const navigate = useNavigate();

    const { refer } = useRegisterStore();

    const { openFileView } = useFileViewer();

    const { run: getReffererByToken, data: refferer } = useRequest(
        async () => {
            const { result, error } = await cnaRequest<ReffererInfo>(
                "/api/v1/team/getRecommendInfo",
                "POST",
                {
                    token: refer,
                }
            );

            if (!error) {
                return result.data;
            } else {
                return null;
            }
        },
        {
            manual: false,
            ready: refer?.length > 0,
        }
    );

    const { run: handleBtnClick } = useRequest(
        async (type: 1 | 2 | 3 | 4 | 5 | 6) => {
            // if (type !== 4) {
            //     let download_url = `${location.origin}/files/${fileNameMappings[type]}.pdf`;
            //     let a = document.createElement("a");
            //     a.setAttribute("download", "");
            //     a.setAttribute("href", download_url);
            //     a.setAttribute("target", "_blank");
            //     document.body.appendChild(a);
            //     a.click();
            //     document.body.removeChild(a);

            //     return;
            // }

            if (type !== 4) {
                openFileView(`${location.origin}/files/${fileNameMappings[type]}.pdf`, {
                    title: fileNameMappings[type],
                });

                return;
            }

            let token = await api.team.gerneateDocToken({
                type,
                ...(type === 4
                    ? {
                          profileID: refferer?.profileID,
                      }
                    : {}),
            });

            if (token) {
                const url = `${window.api_base_url}/api/v1/team/downloadDoc/${token}`;

                openFileView(url, {
                    title: fileNameMappings[type],
                });
                // window.open(`${window.api_base_url}/api/v1/team/downloadDoc/${token}`, "_blank");

                // const { result, error } = await cnaRequest<Blob>(
                //     `/api/v1/team/downloadDoc/${token}`,
                //     "GET",
                //     {},
                //     {
                //         responseType: "blob",
                //     }
                // );

                // if (!error) {
                //     const file = new File([result.data], `${type}.pdf`, {
                //         type: result.data.type,
                //     });
                //     const url = URL.createObjectURL(file);
                //     const link = document.createElement("a");
                //     link.href = url;
                //     link.download = `${fileNameMappings[type]}.pdf`;
                //     link.click();
                //     URL.revokeObjectURL(url);
                // }
            }
        },
        {
            manual: true,
        }
    );

    const toRegister = useMemoizedFn(() => {
        navigate("/account/register");
    });

    return (
        <div className="tw-relative boot-bg">
            {/* <Image
                    className="tw-absolute  tw-z-10 tw-left-1/2  tw--translate-x-1/2 tw-top-12 sm:tw-top-24"
                    src="/images/auth-layout/form-header.png"
                    alt=""
                    w={420}
                /> */}
            <AuthenticationLayout>
                <Stack className="tw-w-full tw-h-full tw-items-center">
                    <Box className="tw-w-[800px] tw-max-w-[90%] tw-mx-auto tw-my-auto">
                        {/* <Text c="white">
                        这是一个更加冗长的示例中文文本，用于在布局中填充内容并观察其效果。此文本的长度旨在为设计人员和开发人员提供更为全面的视角，以确保文本在各种屏幕尺寸和设备上都能以最佳方式呈现。通过使用这样长的文本，设计团队可以更准确地预测用户与内容之间的交互方式，并做出必要的调整，以优化用户体验。此外，这种长文本可以帮助识别潜在的布局问题，例如文本溢出、换行不当，或在响应式设计中可能出现的其他问题。总之，使用这种冗长的文本有助于在产品发布前进行更为全面和细致的测试，从而提升应用程序的整体用户满意度。
                    </Text> */}
                        <Image
                            className="tw-mx-auto"
                            src="/images/auth-layout/form-header.png"
                            alt=""
                            w={420}
                        />

                        <Stack className="tw-mt-16 md:tw-px-20">
                            <CnaButton
                                size="lg"
                                classNames={{
                                    label: "tw-tracking-[0.5rem]",
                                }}
                                variant="white"
                                onClick={() => handleBtnClick(1)}
                            >
                                C&A 企业简介
                            </CnaButton>
                            <CnaButton
                                size="lg"
                                classNames={{
                                    label: "tw-tracking-[0.5rem]",
                                }}
                                variant="white"
                                onClick={() => handleBtnClick(3)}
                            >
                                合伙人加盟手册
                            </CnaButton>
                            <CnaButton
                                size="lg"
                                classNames={{
                                    label: "tw-tracking-[0.5rem]",
                                }}
                                variant="white"
                                onClick={() => handleBtnClick(2)}
                            >
                                绿智地球项目简介
                            </CnaButton>
                            <CnaButton
                                size="lg"
                                classNames={{
                                    label: "tw-tracking-[0.5rem]",
                                }}
                                variant="white"
                                onClick={() => handleBtnClick(6)}
                            >
                                专案业务收费指南
                            </CnaButton>
                            {refferer?.team_file === 1 && (
                                <CnaButton
                                    size="lg"
                                    classNames={{
                                        label: "tw-tracking-[0.5rem]",
                                    }}
                                    variant="white"
                                    onClick={() => handleBtnClick(4)}
                                >
                                    组建合伙人
                                </CnaButton>
                            )}
                            <CnaButton
                                size="lg"
                                classNames={{
                                    label: "tw-tracking-[0.5rem]",
                                }}
                                variant="white"
                                onClick={() => handleBtnClick(5)}
                            >
                                合伙人加盟合同
                            </CnaButton>

                            {/* <CnaButton
                                size="lg"
                                className="tw-border tw-border-white"
                                classNames={{
                                    label: "tw-tracking-[0.5rem]",
                                }}
                                onClick={toRegister}
                            >
                                加入我们
                            </CnaButton> */}
                            <div className="tw-flex tw-justify-end">
                                <div
                                    className="dynamicButton tw-w-full tw-tracking-[0.5rem]"
                                    onClick={toRegister}
                                >
                                    <div> 马上加入</div>
                                </div>
                            </div>
                        </Stack>
                    </Box>
                </Stack>
            </AuthenticationLayout>
            <div className="sm:tw-fixed tw-bottom-5 tw-w-full tw-text-center tw-text-white tw-py-2 tw-text-sm">
                陈玮伦合伙人事务所 版权所有 © 2009 - 2024
            </div>
        </div>
    );
};

export default RegisterDocuments;
