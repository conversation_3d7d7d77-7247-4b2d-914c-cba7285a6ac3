import axios, { AxiosRequestConfig } from "axios";
import useSettngStore from "@code.8cent/store/setting";

// 类型保护函数
function isBaseApiResponse<T>(
    response: T | BaseApiResponse<T>
): response is BaseApiResponse<T> {
    return (
        (response as BaseApiResponse<T>).status !== undefined &&
        (response as BaseApiResponse<T>).code !== undefined
    );
}

const cnaRequest = async <T = any>(
    url: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    data?: any,
    config?: Omit<AxiosRequestConfig, "url" | "method">
): Promise<{
    result: BaseApiResponse<T> | null;
    error: BaseApiResponse<any> | null;
}> => {
    const lang = useSettngStore.getState().lang ?? "ZH";

    const token =
        ((await window.localForage.getItem("cna-token")) as string) || "";

    const protocol = url.startsWith('http://') || url.startsWith('https://') ? '' : window.api_base_url;

    let axiosConfig: AxiosRequestConfig = {
        url: `${protocol}${url}`,
        method: method,
        withCredentials: false, // 类似于 jQuery 的 xhrFields.withCredentials
        responseType: "json", // 'json', 'blob', 'text', etc.
        ...config,
        headers: {
            "X-Api-Key": "greensmartplanet-my",
            "Accept-Language": lang,
            Authorization: token,
            ...config?.headers,
        },
    };

    // 如果是 POST 或 PUT 请求，处理请求体
    if (method === "POST" || method === "PUT") {
        axiosConfig.data = data;

        if (data instanceof FormData !== true) {
            axiosConfig.headers["Content-Type"] = "application/json";
        }
    } else if (method === "GET" && data) {
        axiosConfig.params = data; // GET 请求可以通过 params 传递数据
    }

    try {
        let res = await axios<BaseApiResponse<T>>(axiosConfig);

        console.log("isBaseApiResponse: ", isBaseApiResponse(res.data))

        if (isBaseApiResponse(res.data)) {
            if (res.data.status === false) {
                if (res.data.code === 401) {
                    console.log("401 error: ", axiosConfig.url);
                    // location.replace("/account/login");
                }
                // 处理失败情况
                return {
                    result: null,
                    error: res.data,
                } as const;
            } else {
                // 处理成功情况
                return {
                    result: res.data,
                    error: null,
                } as const;
            }
        } else {
            // 处理 T 类型的情况
            return {
                result: {
                    status: true,
                    data: res.data,
                    message: "",
                    code: 200,
                },
                error: null,
            } as const;
        }
    } catch (error) {
        return {
            result: null,
            error: {
                code: -1,
                message: error.message,
                data: error,
                status: false,
            },
        } as const;
    }
};

export default cnaRequest;
