import { ActionIcon, Stack } from "@mantine/core";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import dayjs from "dayjs";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import gsp from "@/apis/gsp";
import useModalStore from "@/store/modal";
import CnaAdminButton from "@/components/common/CnaAdminButton";
import PaymentVoucherExamine from "@/components/modals/gsp/PaymentVoucherExamine";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { ImageIcon } from "@phosphor-icons/react";

const columnHelper = createColumnHelper<TGspPaymentVoucher>();

const AdminPaymentVouchersPage = () => {
    const lang = useSettingStore.use.lang();

    const tableRef = useRef<DataTableRef | null>(null);
    const openModal = useModalStore.use.open();
    const [data, setData] = useState<TGspPaymentVoucher[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const { openFileView } = useFileViewer();

    // 获取状态显示文本
    const getStatusText = (status: number) => {
        switch (status) {
            case 0:
                return "待审核";
            case 1:
                return "审核通过";
            case 2:
                return "审核不通过";
            default:
                return "未知状态";
        }
    };

    // 获取状态颜色
    const getStatusColor = (status: number) => {
        switch (status) {
            case 0:
                return "gray";
            case 1:
                return "green";
            case 2:
                return "red";
            default:
                return "gray";
        }
    };

    // 审核弹窗
    const handleExamine = (paymentVoucherData: TGspPaymentVoucher) => {
        openModal("gspPaymentVoucherExamineModal", { paymentVoucherData });
    };

    // 查看文件
    const handleViewFile = (file_path: string, file_name: string) => {
        openFileView(
            `${window.api_base_url}/api/v1/admin/file/resource?path=${file_path}&type=remote`,
            {
                title: file_name,
            }
        );
    };

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("company_name", {
            header: "公司名称",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("file_path", {
            header: "查看文件",
            enableSorting: false,
            cell: (info) => (
                <ActionIcon
                    size="xs"
                    variant="transparent"
                    onClick={() => handleViewFile(info.getValue(), info.row.original.file_name)}
                >
                    <ImageIcon />
                </ActionIcon>
            ),
        }),
        columnHelper.accessor("status", {
            header: "状态",
            enableSorting: false,
            cell: (info) => (
                <span className={`tw-text-${getStatusColor(info.getValue())}-600`}>
                    {getStatusText(info.getValue())}
                </span>
            ),
        }),
        columnHelper.accessor("reject_reason", {
            header: "审核不通过原因",
            enableSorting: false,
            cell: (info) => info.getValue() || "-",
        }),
        columnHelper.accessor("created_at", {
            header: "创建时间",
            enableSorting: false,
            cell: (info) =>
                info.getValue() && dayjs(info.getValue() as string).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("updated_at", {
            header: "更新时间",
            enableSorting: false,
            cell: (info) =>
                info.getValue() && dayjs(info.getValue() as string).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                const row = info.row.original;
                return (
                    <div className="tw-flex tw-gap-2">
                        {row.status === 0 && (
                            <CnaAdminButton
                                size="xs"
                                variant="outline"
                                onClick={() => handleExamine(row)}
                            >
                                审核
                            </CnaAdminButton>
                        )}
                    </div>
                );
            },
        }),
    ];

    const handleFetch = async (params: any) => {
        setLoading(true);
        try {
            const { page, pageSize } = params;

            const requestParams = {
                page,
                page_size: pageSize,
            };

            const response = await gsp.paymentVoucherList(requestParams);
            if (response) {
                setData(response.items);
                setTotalCount(response.paginate.total);
            }
        } catch (error) {
            console.error("获取线下付款列表失败:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleExamineSuccess = () => {
        // 刷新表格数据
        tableRef.current?.refresh();
    };

    // 导出数据 todo
    const handleExport = async () => {
        console.log("export");
    };

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="绿智地球申请线下付款管理"
                desc="查询和管理绿智地球申请线下付款"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
            />

            <PaymentVoucherExamine onSubmitSuccess={handleExamineSuccess} />
        </Stack>
    );
};

export default AdminPaymentVouchersPage;
