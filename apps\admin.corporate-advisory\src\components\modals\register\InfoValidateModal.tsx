import api from "@/apis";
import useModalStore from "@/store/modal";
import useProfileStore from "@/store/profile";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMemoizedFn, useRequest } from "ahooks";
import React, { useContext, useEffect, useState } from "react";
import {
    Button,
    Group,
    Input,
    Modal,
    Select,
    Stack,
    TextInput,
} from "@mantine/core";
import { SubmitHand<PERSON>, useForm, UseFormGetValues } from "react-hook-form";
import { z } from "zod";
import { useShallow } from "zustand/react/shallow";
import useRegisterStore from "@/store/register";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { Check, Spinner } from "@phosphor-icons/react";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useDataStore from "@code.8cent/store/data";
import PhoneInput from "@code.8cent/react/components/PhoneInput";

type RegisterInformationFormInput = {
    name: string;
    idCard: string;
    sexual: string;
    birthdate: string;
    nationality: string;
    emailValidate: string;
    phoneValidate: string;
    // addressInfo: {}
};

type VerifyFormInput = {
    code: string;
};

const verifyFormSchema = z.object({
    code: z.string().min(1, "Verify code is required"),
});

const initalValues = {
    code: "",
};

const InfoValidateModal: React.FC<{
    type: "phone" | "email";
    onValidteSuccess: (type: "phone" | "email") => void;
}> = ({ type, onValidteSuccess }) => {
    const countryDatas = useDataStore.use.countryDatas();

    const { show, close, alert } = useModalStore(
        useShallow((state) => ({
            show: state.profileValidate,
            close: state.close,
            alert: state.openAlert,
        }))
    );

    const { phone, email, prefixID } = useRegisterStore(
        useShallow((state) => ({
            phone: state.phone,
            email: state.email,
            prefixID: state.prefixID,
        }))
    );
    const lang = useSettingStore.use.lang();
    const {
        register,
        handleSubmit,
        formState: { errors },
        setError,
        reset,
    } = useForm<VerifyFormInput>({
        defaultValues: initalValues,
        resolver: zodResolver(verifyFormSchema),
    });

    const [countdown, setCountdown] = useState<number | null>(null);

    const closeModal = () => {
        close("profileValidate");
        reset();
    };

    const { run: getEmailCode, loading: requestingEmailCode } = useRequest(
        async () => {
            let send_res = await api.register.sendVerifyEmail(email);

            if (send_res === true) {
                setCountdown(60);
            } else {
                alert(
                    t("validation.send.fail", lang),
                    t("validation.send.fail.details", lang),
                    "danger"
                );
            }
        },
        {
            manual: true,
        }
    );

    const { run: getPhoneCode, loading: requestingPhoneCode } = useRequest(
        async () => {
            let send_res = await api.register.sendVerifySMS(phone, prefixID);

            if (send_res === true) {
                setCountdown(60);
            } else {
                alert(
                    t("validation.send.fail", lang),
                    t("validation.send.fail.details", lang),
                    "danger"
                );
            }
        },
        {
            manual: true,
        }
    );

    const { run: verify, loading: verifying } = useRequest(
        async (data: VerifyFormInput) => {
            if (type === "email") {
                let verify_res = await api.register.verifyEmail(
                    email,
                    data.code
                );

                if (verify_res === true) {
                    alert(
                        t("validation.success", lang),
                        t("validation.email.success", lang),
                        "success"
                    );
                    onValidteSuccess("email");
                    closeModal();
                } else {
                    alert(
                        t("validation.fail", lang),
                        t("validation.input", lang),
                        "danger"
                    );
                }
            } else if (type === "phone") {
                let verify_res = await api.register.verifyPhone(
                    phone,
                    prefixID,
                    data.code
                );

                if (verify_res === true) {
                    alert(
                        t("validation.success", lang),
                        t("validation.phone.success", lang),
                        "success"
                    );
                    onValidteSuccess("phone");
                    closeModal();
                } else {
                    alert(
                        t("validation.fail", lang),
                        t("validation.input", lang),
                        "danger"
                    );
                }
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (countdown === null || countdown === 0) return;

        const timer = setTimeout(() => {
            setCountdown((prev) => (prev !== null ? prev - 1 : null));
        }, 1000);

        return () => clearTimeout(timer);
    }, [countdown]);

    useEffect(() => {
        if (show === false) {
            setCountdown(null);
        }
    }, [show]);

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            title={`${
                type === "phone"
                    ? t("project.company_form.label.phone", lang)
                    : t("project.company_form.label.mail", lang)
            }${t("common.verify", lang)}`}
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-my-5">
                {type === "email" && (
                    <>
                        <TextInput
                            readOnly
                            value={email}
                            label={t("validation.email", lang)}
                            rightSectionWidth={120}
                            rightSectionProps={{ className: "tw-justify-end" }}
                            rightSection={
                                countdown !== null && countdown > 0 ? (
                                    <Button
                                        variant="transparent"
                                        color="gray"
                                        className="disabled:tw-bg-transparent tw-font-normal"
                                        disabled
                                    >
                                        {`${t(
                                            "validate.before.second",
                                            lang
                                        )} ${countdown} ${t(
                                            "validate.after.second",
                                            lang
                                        )}`}
                                    </Button>
                                ) : (
                                    <Button
                                        variant="transparent"
                                        color="gray"
                                        onClick={getEmailCode}
                                        disabled={requestingEmailCode}
                                        className="disabled:tw-bg-transparent tw-font-normal"
                                        leftSection={
                                            requestingEmailCode === true && (
                                                <Spinner
                                                    className="tw-animate-spin"
                                                    size={16}
                                                />
                                            )
                                        }
                                    >
                                        {t(
                                            "forget_password.btn.send_validate_code",
                                            lang
                                        )}
                                    </Button>
                                )
                            }
                        />

                        <TextInput
                            error={errors.code ? true : false}
                            label={t("validation.code", lang)}
                            placeholder={t("validation.enter.code", lang)}
                            {...register("code")}
                        />
                    </>
                )}
                {type === "phone" && (
                    <>
                        <PhoneInput<CountryDataItem>
                            label={t("project.company_form.label.phone", lang)}
                            data={countryDatas}
                            prefixFlagKey="countryISOCode2"
                            prefixLabelKey="countryCode"
                            prefixValueKey="countryID"
                            prefixProps={{
                                w: 120,
                                value: String(prefixID),
                            }}
                            inputProps={{
                                value: String(phone),
                                rightSectionWidth: 120,
                                rightSectionProps: {
                                    className: "tw-justify-end",
                                },
                                rightSection:
                                    countdown !== null && countdown > 0 ? (
                                        <Button
                                            variant="transparent"
                                            color="gray"
                                            className="disabled:tw-bg-transparent tw-font-normal"
                                            disabled
                                        >
                                            {`${t(
                                                "validate.before.second",
                                                lang
                                            )} ${countdown} ${t(
                                                "validate.after.second",
                                                lang
                                            )}`}
                                        </Button>
                                    ) : (
                                        <Button
                                            variant="transparent"
                                            color="gray"
                                            onClick={getPhoneCode}
                                            disabled={requestingPhoneCode}
                                            className="disabled:tw-bg-transparent tw-font-normal"
                                            leftSection={
                                                requestingPhoneCode ===
                                                    true && (
                                                    <Spinner
                                                        className="tw-animate-spin"
                                                        size={16}
                                                    />
                                                )
                                            }
                                        >
                                            {t(
                                                "forget_password.btn.send_validate_code",
                                                lang
                                            )}
                                        </Button>
                                    ),
                            }}
                            readOnly
                        />
                        <TextInput
                            error={errors.code ? true : false}
                            label={t("validation.code", lang)}
                            placeholder={t("validation.enter.code", lang)}
                            {...register("code")}
                        />
                    </>
                )}
            </Stack>
            <Group className="tw-pt-5" justify="end">
                <CnaButton
                    color="cna"
                    loading={verifying}
                    onClick={handleSubmit(verify)}
                    leftSection={<Check />}
                >
                    {t("validation.submit", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default InfoValidateModal;
