import api from "@/apis";
import { Badge, Button, Box, Text, Group, Stack, Table, TextInput, Title } from "@mantine/core";
import {
    useDebounce,
    useGetState,
    useMemoizedFn,
    useMount,
    useRequest,
    useUnmount,
    useUpdateEffect,
    useVirtualList,
} from "ahooks";
import { useRef, useState } from "react";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import ScrollArea from "@code.8cent/react/components/ScrollArea";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { Eye, DownloadSimple } from "@phosphor-icons/react";
import { PageHeader } from "@code.8cent/react/components";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import dayjs from "dayjs";

const MemberDocumentPage = () => {
    const { lang, settingDateFormat } = useSettingStore();

    const containerRef = useRef<HTMLDivElement>(null);

    const wrapperRef = useRef(null);

    const [documents, setDocuments] = useState<DocumentItem[]>([]);

    const [inited, setInited] = useState(false);

    const [hasMore, setHasMore] = useState<boolean>(false);

    const [page, setPage, getPage] = useGetState<number>(1);

    const [keyword, setKeyword] = useState<string>("");

    const keywordValue = useDebounce(keyword, { wait: 500 });

    const { openFileView } = useFileViewer();

    const { run: getDocumentList, loading } = useRequest(
        async (page_param: number = 1, keyword_param: string = "") => {
            try {
                if (page !== 1) {
                    return;
                }

                const pageSize = 5;

                const docs = await api.user.getUserDocumentList(
                    page_param,
                    pageSize,
                    keyword_param
                );

                if (docs.length < pageSize) {
                    setHasMore(false);
                } else {
                    setHasMore(true);
                }

                console.log("fetch page: ", page_param);

                if (page_param === 1) {
                    setDocuments(docs);
                } else {
                    setDocuments((prev) => [...prev, ...docs]);
                }
            } catch (error) {
                console.error("Failed to fetch documents:", error);
            }
        },
        {
            ready: inited,
        }
    );

    const getMoreDocuments = useMemoizedFn(async () => {
        if (hasMore) {
            const new_page = page + 1;

            getDocumentList(new_page);

            setPage(new_page);
        }
    });

    const handleFile = useMemoizedFn(async (fileId: number, type: "view" | "download") => {
        let token = await api.user.getDocumentToken(fileId);

        if (type === "view") {
            openFileView(`${window.api_base_url}/api/v1/document/preview/${token}`);
        } else if (type === "download") {
            let download_url = `${window.api_base_url}/api/v1/document/download/${token}`;
            let a = document.createElement("a");
            a.setAttribute("download", "");
            a.setAttribute("href", download_url);
            a.setAttribute("target", "_blank");
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }
    });

    useMount(() => {
        setInited(true);
    });

    useUnmount(() => {
        console.log("unmounted");
        setInited(false);
    });

    useUpdateEffect(() => {
        setPage(1);
        getDocumentList(1, keywordValue);
    }, [keywordValue]);

    // UseVirtualList hook
    const [virtualDocumentList] = useVirtualList(documents, {
        containerTarget: containerRef,
        wrapperTarget: wrapperRef,
        itemHeight: 63 + 8, // Define your row height here
        overscan: 5, // Number of extra items rendered above and below the visible range
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title={t("navigation.data", lang)}
                desc={t("navigation.data.system.quote", lang)}
            />

            <ScrollArea
                onScrollBottom={getMoreDocuments}
                className="tw-border tw-flex-1"
                ref={containerRef}
                viewportRef={wrapperRef}
            >
                <Table verticalSpacing="md">
                    <Table.Thead className="tw-sticky tw-bg-white tw-shadow-sm">
                        <Table.Tr className="tw-w-full">
                            <Table.Th>{t("data.table.th.file", lang)}</Table.Th>
                            <Table.Th>{t("data.table.th.version", lang)}</Table.Th>
                            <Table.Th className="tw-text-right">
                                {t("document.table.th.action", lang)}
                            </Table.Th>
                        </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody className="tw-flex-1">
                        {!virtualDocumentList.length && (
                            <Table.Tr>
                                <Table.Td colSpan={6}>
                                    <Group
                                        justify="center"
                                        className="tw-mt-20"
                                    >
                                        {t("document.empty", lang)}
                                    </Group>
                                </Table.Td>
                            </Table.Tr>
                        )}

                        {virtualDocumentList.map(({ data: doc, index: doc_index }) => {
                            return (
                                <Table.Tr
                                    key={doc_index}
                                    style={{ height: 63 }}
                                    className="last:!tw-border-inherit last:!tw-border-solid"
                                >
                                    <Table.Td>{doc.documentTitle}</Table.Td>
                                    <Table.Td>
                                        {doc.documentVersion}
                                    </Table.Td>
                                    <Table.Td className="tw-text-right">
                                        <Group justify="flex-end">
                                            <CnaButton
                                                size="xs"
                                                color="dark.3"
                                                variant="outline"
                                                onClick={() => handleFile(doc.documentID, "view")}
                                                leftSection={<Eye size={16} />}
                                            >
                                                查看
                                            </CnaButton>
                                            {/* <CnaButton
                                                size="xs"
                                                color="dark.3"
                                                variant="outline"
                                                onClick={() =>
                                                    handleFile(doc.documentID, "download")
                                                }
                                            >
                                                <DownloadSimple size={16} />
                                            </CnaButton> */}
                                        </Group>
                                    </Table.Td>
                                </Table.Tr>
                            );
                        })}
                    </Table.Tbody>
                </Table>
            </ScrollArea>
        </Stack>
    );
};

export default MemberDocumentPage;
