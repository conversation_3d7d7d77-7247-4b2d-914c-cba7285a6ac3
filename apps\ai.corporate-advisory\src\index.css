@layer tailwind {
    @tailwind base;
}
@tailwind components;
@tailwind utilities;

@media print {
    * {
        overflow: visible !important;
    }
}

.custom-tabs {
    gap: 0;
    position: relative;
    border-bottom: 2px solid;
    @apply !tw-border-b-gray-300;
}

.custom-tabs .nav-link {
    padding: 0.5rem 1rem;
    margin-bottom: -2px;
    border-bottom: 2px solid transparent;
    @apply !tw-text-gray-500;
    @apply !tw-font-normal;
    @apply hover:!tw-border-b-gray-800;
    @apply hover:!tw-text-gray-800;
}

.custom-tabs .nav-link.active {
    @apply !tw-border-b-gray-800;
    @apply !tw-text-gray-800;
}

.table-fixed-header {
    overflow-y: auto;
}

.table-fixed-header thead th {
    position: sticky;
    top: 0;
    background: #f8f9fa; /* Background color for the header */
    z-index: 1;
}

.account-form-label {
    @apply tw-text-[16px] tw-font-normal md:tw-mb-1 tw-mb-0;
}

.profile-update-password-btn {
    @apply tw-border tw-bg-transparent tw-border-neutral-300 tw-text-black tw-font-normal;
    @apply hover:tw-border hover:!tw-bg-transparent hover:tw-border-neutral-300 hover:tw-text-black hover:tw-font-normal;
}

.mantine-Button-root {
    @apply tw-font-normal;
}

.profile-form-label {
    @apply tw-mb-2 tw-w-full tw-bg-basic-5 tw-text-gray-50 tw-py-1 tw-px-2;
}

/* TODO: 需要重构 - 优化表格样式的全局设置 */
table th:not(.mantine-DateInput-weekday):not(.mantine-DatePickerInput-weekday) {
    @apply !tw-bg-basic-5 !tw-text-gray-50;
}

.team-page-container {
    @apply tw-min-h-[100vh]
        tw-min-w-[100vw];

    @apply md:tw-h-screen md:tw-w-screen md:tw-overflow-hidden;
}

.boot-bg {
    @apply tw-bg-[url('/images/auth-layout/page-background.jpg')]
    tw-bg-no-repeat
    tw-bg-cover
    tw-bg-center;
}
.dynamicButton,
.dynamicButton *,
.dynamicButton :after,
.dynamicButton :before,
.dynamicButton:after,
.dynamicButton:before {
    border: 0 solid;
    box-sizing: border-box;
}

.dynamicButton {
    /* width: 40%; */
    height: 3.125rem;
    text-align: center;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: dynamicButton;
    background-color: #060d3d;
    background-image: none;
    color: #fff;
    cursor: pointer;
    font-size: 100%;
    line-height: 1.5;
    margin: 0;

    -webkit-mask-image: -webkit-radial-gradient(#000, #fff);
}

.dynamicButton:disabled {
    cursor: default;
}

.dynamicButton:-moz-focusring {
    outline: auto;
}

.dynamicButton svg {
    display: block;
    vertical-align: middle;
}

.dynamicButton [hidden] {
    display: none;
}

.dynamicButton {
    /* box-shadow: inset 0 0 0 2px #fff; */
    box-sizing: border-box;
    display: block;
    font-weight: 900;
    position: relative;
    text-transform: uppercase;
    transition: color 0.1s linear;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* .dynamicButton:hover {
    color: #fff;
    transition: color 0.1s linear 1s;
} */

.dynamicButton:after,
.dynamicButton:before {
    content: "";
    height: 0;
    position: absolute;
    width: 0;
}

.dynamicButton:before {
    border-right: var(--border);
    border-top: var(--border);
    left: 0;
    top: 0;
}

.dynamicButton:before {
    --border: 2px solid #fff;
    -webkit-animation: border-top-and-right 2s infinite;
    animation: border-top-and-right 2s infinite;
}

.dynamicButton:after {
    border-bottom: var(--border);
    border-left: var(--border);
    bottom: 0;
    right: 0;
    z-index: -1;
}
/* 鼠标悬停效果 */
/* .dynamicButton:hover {
    transform: scale(1.05);
   
} */
.dynamicButton:after {
    --border: 2px solid #fff;
    -webkit-animation: border-bottom-and-left 2s infinite;
    animation: border-bottom-and-left 2s infinite;
}

@-webkit-keyframes border-top-and-right {
    0% {
        height: 0;
        width: 0;
    }

    50% {
        height: 0;
        width: 100%;
    }

    to {
        height: 100%;
        width: 100%;
    }
}

@keyframes border-top-and-right {
    0% {
        height: 0;
        width: 0;
    }

    50% {
        height: 0;
        width: 100%;
    }

    to {
        height: 100%;
        width: 100%;
    }
}

@-webkit-keyframes border-bottom-and-left {
    0% {
        height: 0;
        width: 0;
    }

    50% {
        height: 0;
        width: 100%;
    }

    to {
        height: 100%;
        width: 100%;
    }
}

@keyframes border-bottom-and-left {
    0% {
        height: 0;
        width: 0;
    }

    50% {
        height: 0;
        width: 100%;
    }

    to {
        height: 100%;
        width: 100%;
    }
}

/* 按钮上的文本 */
.dynamicButton .text {
    text-align: center;
    line-height: 100%;
}

.bottomButton {
    display: flex;
    align-items: center;
    position: relative;
    background-color: #050823;
    /* 按钮背景色 */
    border-radius: 8px;
    padding: 10px 30px;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    overflow: hidden;
    /* 隐藏超出部分 */
    transition: transform 0.3s ease;
    /* 鼠标悬停时的平滑效果 */
    box-shadow: 5px 5px 4px #605b5b;
}

.box:before {
    content: "";
    width: 300px;
    height: 100px;
    background: linear-gradient(transparent 10%, rgba(255, 255, 255, 0.4), transparent 90%);
    transform: rotate(-45deg);
    position: absolute;
    top: -40px;
    left: -208px;
    transition: 0.5s;
}

/* 按钮上的文本 */
.bottomButton .text {
    z-index: 2;
    position: relative;
}

/* 鼠标悬停效果 */
.bottomButton:hover {
    transform: scale(1.05);
    /* 放大效果 */
}

.bottomButton {
    /* border: 1px solid; */
    overflow: hidden;
    position: relative;
}

/* 定义动画效果 */
@keyframes slideAnimation {
    0% {
        left: -75px;
        /* 从按钮左侧开始 */
    }

    /* 50% {
      left: 50%;
    } */

    100% {
        left: 120%;
        /* 移动到按钮右侧外部 */
    }
}

.bottomButton:after {
    background: linear-gradient(45deg, #e6e6e6, transparent);
    content: "";
    height: 90px;
    left: -75px;
    opacity: 0.2;
    position: absolute;
    top: -23px;
    -webkit-transform: rotate(35deg);
    -ms-transform: rotate(35deg);
    transform: rotate(35deg);
    -webkit-transition: all 550ms cubic-bezier(0.19, 1, 0.22, 1);
    width: 50px;

    /* z-index: -10; */
}
.emo-0:after {
    animation: slideAnimation 2s infinite !important;
    animation-delay: 0.2s !important;
}
.emo-1:after {
    animation: slideAnimation 2s infinite !important;
    animation-delay: 0.5s !important;
}
.emo-2:after {
    animation: slideAnimation 2s infinite !important;
    animation-delay: 0.9s !important;
}

.bottomButton:hover:after {
    left: 120%;
    -webkit-transition: all 550ms cubic-bezier(0.19, 1, 0.22, 1);
    transition: all 550ms cubic-bezier(0.19, 1, 0.22, 1);
}
