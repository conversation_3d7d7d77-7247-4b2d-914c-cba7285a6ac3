import cnaRequest from "@code.8cent/utils/cnaRequest";

const config = {
    getConfigOptions: async () => {
        let { result, error } = await cnaRequest<SettingConfigOptionResponse>(
            "/api/v1/configurations",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },

    getCountryDatas: async () => {
        let { result, error } = await cnaRequest<CountryDataItem[]>(
            "/api/v1/config/countries",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return [];
        }
    },

    getLanguageStructure: async () => {
        let { result, error } = await cnaRequest<LanguageStructure[]>(
            "/api/v1/config/structure",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    }
};

export default config;
