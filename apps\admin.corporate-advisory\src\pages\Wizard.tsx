import api from "@/apis";
import CnaButton from "@code.8cent/react/components/CnaButton";
import CompleteSetup from "@/components/wizard/CompleteSetup";
import LanguageSetup from "@/components/wizard/LanguageSetup";
import SecuritySetup from "@/components/wizard/SecuritySetup";
import UserSetup from "@/components/wizard/UserSetup";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import { useEventBus } from "@/utils/eventBus";
import { Box, Group, Image, Paper, Stack, Text, Title } from "@mantine/core";
import {
    CheckCircle,
    IconProps,
    NumberCircleOne,
    NumberCircleThree,
    NumberCircleTwo,
} from "@phosphor-icons/react";
import { useMemoizedFn, useMount, useUnmount } from "ahooks";
import React, { cloneElement, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

const WizardProgress = [
    {
        title: "语言设置", // "account.wizard.language",
        icon: <NumberCircleOne />,
    },
    {
        title: "安全设置", // "account.wizard.security",
        icon: <NumberCircleTwo />,
    },
    {
        title: "用户设置", //"account.wizard.user",
        icon: <NumberCircleThree />,
    },
    {
        title: "设置完成", // "account.wizard.complete",
        icon: <CheckCircle />,
    },
];

const AccountWizardPage = () => {
    const bus = useEventBus();

    const lang = useSettingStore.use.lang();

    const navigate = useNavigate();

    const { state, setState } = useWizardStore();

    const [loading, setLoading] = useState(false);

    const submit = useMemoizedFn(() => {
        bus.emit("wizard.submit.click");
    });

    useMount(async () => {
        let token = (await window.localForage.getItem("cna-token")) as string;

        if (!token || !token.length) {
            navigate("/account/login");
            return;
        }

        let wizardState = await api.user.getWizardState();

        if (wizardState) {
            setState(wizardState.state);
        }

        bus.on("wizard.submitting", (__loading: boolean) => {
            setLoading(__loading);
        });
    });

    useUnmount(() => {
        bus.off("wizard.submitting");
    });

    return (
        <div className="tw-flex tw-h-[100vh] tw-w-[100vw]">
            <div className="md:tw-w-[200px] md:tw-flex lg:tw-w-[240px] tw-border-r tw-bg-gray-50 tw-hidden tw-flex-col tw-w-0 tw-transition-all">
                <div className="tw-px-5 tw-py-3 tw-border-b">
                    <div className="tw-px-4">
                        <Image
                            src="/images/C&A-logo-icon-blue.svg"
                            w={80}
                            className="tw-mx-auto"
                        />
                    </div>
                    <Text className="tw-text-center tw-mt-3 tw-text-xl">
                        {t("dashboard.title", lang)}
                    </Text>
                </div>
                <div className="tw-flex-1 tw-py-3 tw-overflow-y-auto">
                    {WizardProgress.map((progress, index) => {
                        const icon = cloneElement(progress.icon, {
                            size: 32,
                            weight: "thin",
                        } as IconProps);

                        return (
                            <div
                                className={`tw-flex tw-px-8 tw-py-4 tw-items-center tw-justify-start ${
                                    index + 1 === state
                                        ? "tw-text-gray-800"
                                        : "tw-text-gray-500"
                                }`}
                                key={index}
                            >
                                {icon}
                                <Text className="tw-flex-1 tw-text-left tw-ml-6">
                                    {t(progress.title, lang)}
                                </Text>
                            </div>
                        );
                    })}
                </div>
            </div>
            <div className="tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden">
                <Stack
                    className="tw-bg-white tw-p-6 md:tw-py-16 md:tw-px-24 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0"
                    align="center"
                >
                    <Box className="tw-w-[640px] tw-max-w-full">
                        <Title order={2}>设置启动</Title>
                        <Text size="sm">请按照以下说明完成您的账户设置。</Text>

                        <Box className="tw-shadow-lg tw-border tw-px-6 tw-rounded-sm tw-mt-[48px]">
                            <Stack gap={0}>
                                <Group className="tw-py-4 tw-border-b">
                                    {cloneElement(
                                        WizardProgress[state - 1].icon,
                                        {
                                            size: 32,
                                            weight: "thin",
                                        } as IconProps
                                    )}
                                    <Title order={3} fw="normal">
                                        {WizardProgress[state - 1].title}
                                    </Title>
                                </Group>

                                <Box className="tw-py-4 tw-border-b">
                                    {state === 1 && <LanguageSetup />}
                                    {state === 2 && <SecuritySetup />}
                                    {state === 3 && <UserSetup />}
                                    {state === 4 && <CompleteSetup />}
                                </Box>

                                <Group
                                    className="tw-py-4"
                                    justify={state === 4 ? "center" : "end"}
                                >
                                    <CnaButton
                                        color="basic"
                                        miw={100}
                                        onClick={submit}
                                        loading={loading}
                                    >
                                        {state === 4
                                            ? "进入"
                                            : t("common.next.page", lang)}
                                    </CnaButton>
                                </Group>
                            </Stack>
                        </Box>
                    </Box>
                </Stack>
            </div>
        </div>
    );
};

export default AccountWizardPage;
