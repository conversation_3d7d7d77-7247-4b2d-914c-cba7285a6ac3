import { CnaButton } from "@code.8cent/react/components";
import { Grid, SimpleGrid, Text } from "@mantine/core";

interface ButtonProps {
    key: string;
    label: string;
    onClick?: () => void;
    style?: string;
    leftSection?: React.ReactNode;
    loading?: boolean;
    type?: "submit" | "button";
}

const ModalFooter: React.FC<{
    timelineContent?: string;
    buttons: ButtonProps[];
}> = ({ timelineContent = "", buttons = [] }) => {
    return (
        <Grid className="tw-mt-4">
            <Grid.Col span={6} className="tw-flex tw-items-center">
                <Text>{timelineContent}</Text>
            </Grid.Col>
            <Grid.Col span={6}>
                <SimpleGrid cols={buttons.length}>
                    {buttons.map((button) => (
                        <CnaButton
                            variant={button.style}
                            key={button.key}
                            leftSection={button.leftSection}
                            onClick={button.onClick}
                            type={button.type}
                            loading={button.loading}
                        >
                            {button.label}
                        </CnaButton>
                    ))}
                </SimpleGrid>
            </Grid.Col>
        </Grid>
    );
};

export default ModalFooter;
