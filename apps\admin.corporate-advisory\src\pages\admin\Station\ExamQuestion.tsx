import { Group, Stack } from "@mantine/core";
import { useRef, useState } from "react";
import { useMemoizedFn } from "ahooks";
import { PageHeader, DataTable, DataTableRef, CnaButton } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import useModalStore from "@/store/modal";
import { Plus } from "@phosphor-icons/react";
import examQuestion from "@/apis/station/examQuestion";
import { useConfirm } from "@/hooks/useConfirm";
import PageActionButtons from "@/components/common/PageActionButtons";
import noty from "@code.8cent/react/noty";
import Form from "@/components/modals/examQuestion/Form";

const columnHelper = createColumnHelper<TQuestion>();

const TYPE_MAP = {
    0: "单选题",
    1: "多选题",
    2: "文本填写",
    3: "判断题",
};

const ExamQuestionPage = () => {
    const [data, setData] = useState<TQuestion[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const tableRef = useRef<DataTableRef>(null);
    const { confirm } = useConfirm();
    const { open } = useModalStore();

    // 表格列定义
    const tableColumns = [
        columnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("exam.name", {
            header: "所属考试",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("type", {
            header: "类型",
            enableSorting: false,
            cell: (info) => {
                const type = info.getValue();
                return TYPE_MAP[type];
            },
        }),
        columnHelper.accessor("question", {
            header: "问题内容",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                const row = info.row.original;
                return (
                    <Group gap="xs">
                        <CnaButton
                            variant="outline"
                            size="xs"
                            onClick={() => handleEdit(row)}
                        >
                            编辑
                        </CnaButton>
                        <CnaButton
                            color="red"
                            size="xs"
                            onClick={() => handleDelete(row.id)}
                        >
                            删除
                        </CnaButton>
                    </Group>
                );
            },
        }),
    ];

    // 刷新表格数据
    const refreshTable = useMemoizedFn(() => {
        if (tableRef.current) {
            tableRef.current.refresh();
        }
    });

    // 获取数据
    const handleFetch = async (params: any) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                page,
                page_size: pageSize,
                type: globalFilters?.type || "",
                exam_id: globalFilters?.exam_id || "",
            };

            const result = await examQuestion.list(requestParams);
            if (result) {
                setData(result.list.data);
                setTotalCount(result.list.total);
            }
        } finally {
            setLoading(false);
        }
    };

    // 添加问题
    const handleAdd = useMemoizedFn(() => {
        open("examQuestionForm");
    });

    // 编辑问题
    const handleEdit = useMemoizedFn((row: TQuestion) => {
        open("examQuestionForm", row);
    });

    // 删除问题
    const handleDelete = useMemoizedFn(async (id: number) => {
        confirm({
            title: "确认删除",
            message: "您确定要删除这个考试问题吗？此操作不可撤销。",
            variant: "danger",
            onConfirm: async () => {
                try {
                    const success = await examQuestion.destroy(id);
                    if (success) {
                        noty.success("删除成功");
                        refreshTable();
                    }
                } catch (error) {
                    console.error("删除问题失败", error);
                    noty.error("删除失败");
                }
            },
        });
    });

    const pageButtons = [
        {
            key: "add",
            label: "新增",
            leftSection: <Plus size={14} />,
            onClick: handleAdd,
        },
    ];

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="考试问题管理"
                desc="管理考试问题和选项"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "exam_id",
                        label: "所属考试",
                        type: "select",
                        options: [
                            { value: "1", label: "管理合伙人晋升" },
                            { value: "2", label: "申请合伙人加盟手册牌照" },
                        ],
                    },
                    {
                        field: "type",
                        label: "问题类型",
                        type: "select",
                        options: [
                            { value: "0", label: "单选题" },
                            { value: "1", label: "多选题" },
                            { value: "2", label: "文本填写" },
                            { value: "3", label: "判断题" },
                        ],
                    },
                ]}
            />

            <Form refreshTable={refreshTable} />
        </Stack>
    );
};

export default ExamQuestionPage;
