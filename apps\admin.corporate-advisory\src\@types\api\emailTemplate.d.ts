declare global {
    type TEmailTemplateUpdateParams = {
        eventCode: string;
        emailTitleEN?: string;
        emailTitleZH?: string;
        emailTitleZT?: string;
        emailTitleMS?: string;
        emailDescriptionEN?: string;
        emailDescriptionZH?: string;
        emailDescriptionZT?: string;
        emailDescriptionMS?: string;
    };

    type TEmailTemplate = {
        id: number;
        eventCode: string;
        emailTitleEN: string | null;
        emailTitleZH: string | null;
        emailTitleZT: string | null;
        emailTitleMS: string | null;
        emailDescriptionEN: string | null;
        emailDescriptionZH: string | null;
        emailDescriptionZT: string | null;
        emailDescriptionMS: string | null;
        remark: string;
        createUser: number | null;
        createRole: number | null;
        editUser: number | null;
        editRole: number | null;
        editTime: string | null;
        created_at: string | null;
        updated_at: string | null;
    }

    type TEmailTemplateResponse = {
        items: TEmailTemplate[];
        paginate: BasePaginateResponse;
    };
}

export {};
