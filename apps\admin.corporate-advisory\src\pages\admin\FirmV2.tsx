import api from "@/apis";
import { Stack, Group, ActionIcon, Badge, Button, Tooltip } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { UploadSimple, GearSix, Eye } from "@phosphor-icons/react";
import { PageHeader } from "@code.8cent/react/components";
import PageActionButtons from "@/components/common/PageActionButtons";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import FirmInfo from "@/components/modals/firm/Info";
import { DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";

interface Firm {
    id: number;
    company_name: string;
    credit_code: string;
    status: string;
    income: number;
    created_at: string;
}

const columnHelper = createColumnHelper<Firm>();

const AdminFirmV2Page = () => {
    const { lang } = useSettingStore();

    const tableRef = useRef<DataTableRef | null>(null);
    const openModal = useModalStore.use.open();

    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const pageButtons = [
        {
            key: "export",
            label: "导出",
            leftSection: <UploadSimple size={14} />,
            onClick: () => {},
        },
        {
            key: "setting",
            label: "设置",
            leftSection: <GearSix size={14} />,
            onClick: () => {},
        },
    ];

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const response = await api.firm.list(params);
            setData(response.items);
            setTotalCount(response.paginate?.total);
        } finally {
            setLoading(false);
        }
    };

    const refreshData = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    const tableColumns = [
        columnHelper.accessor("company_name", {
            header: "企业名称",
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("credit_code", {
            header: "统一社会信用代码",
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("status", {
            header: "状态",
            cell: (info) => {
                const status = info.getValue();
                const statusMap = {
                    0: { label: "待审核", color: "yellow" },
                    1: { label: "已通过", color: "green" },
                    2: { label: "未通过", color: "red" },
                };
                return <Badge color={statusMap[status].color}>{statusMap[status].label}</Badge>;
            },
        }),
        columnHelper.accessor("income", {
            header: "收入",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("created_at", {
            header: "申请时间",
            cell: (info) => dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                const firm = info.row.original;
                return (
                    <Group gap="xs">
                        <Tooltip label="查看">
                            <ActionIcon
                                variant="subtle"
                                onClick={() => {
                                    openModal("firmInfoModal", {
                                        firmInfo: firm,
                                    });
                                }}
                            >
                                <Eye size={16} />
                            </ActionIcon>
                        </Tooltip>
                    </Group>
                );
            },
        }),
    ];

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="联号事务所"
                desc="查询联号事务所申请信息"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                filterTypes={{
                    company_name: "text",
                    credit_code: "text",
                    status: "select",
                    income: "numberRange",
                    created_at: "dateRange",
                }}
                filterOptions={{
                    status: [
                        { value: "0", label: "待审核" },
                        { value: "1", label: "已通过" },
                        { value: "2", label: "未通过" },
                    ],
                }}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                enableMultiSelect
                selectionActions={
                    <Button
                        size="xs"
                        onClick={() => {
                            const selectedRows = tableRef.current?.getSelectedRows();
                            console.log(selectedRows);
                        }}
                    >
                        批量审核
                    </Button>
                }
            />

            {/* 事务所申请信息详情 */}
            <FirmInfo onUpdateSuccess={refreshData} />
        </Stack>
    );
};

export default AdminFirmV2Page;
