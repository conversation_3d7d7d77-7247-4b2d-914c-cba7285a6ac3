import {
    Table,
    Group,
    Pagination,
    Stack,
    Select,
    Text,
    ScrollArea,
    LoadingOverlay,
} from "@mantine/core";
import {
    useReactTable,
    getCoreRowModel,
    getPaginationRowModel,
    flexRender,
    getSortedRowModel,
} from "@tanstack/react-table";
import { forwardRef, useEffect, useState, useImperativeHandle } from "react";
import { useMemoizedFn } from "ahooks";
import noty from "@code.8cent/react/noty";
import { DataTableRef, DataTableProps, DensityType, DragResult, PAGE_SIZE_OPTIONS } from "./types";
import { useDataTableState } from "./hooks/useDataTableState";
import { ActionBar } from "./components/ActionBar";
import { TableBody } from "./components/TableBody";
import { GlobalFilters } from "./components/GlobalFilters";
import { TableHeader } from "./components/TableHeader";

// 扩展tanstack/react-table的ColumnMeta，添加enableTooltip属性
declare module "@tanstack/react-table" {
    interface ColumnMeta<TData extends unknown, TValue> {
        enableTooltip?: boolean;
    }
}

export const DataTable = forwardRef<DataTableRef, DataTableProps<any>>(function DataTable<
    T extends object
>(
    {
        columns,
        data,
        totalCount,
        enableMultiSelect = false,
        selectionActions,
        filterTypes = {},
        filterOptions = {},
        loading = false,
        onFetch,
        onExport,
        defaultDensity = "sm",
        serverSideSort = true,
        globalFilterFields = [],
        getRowId = (row: any) => row.id, // 默认使用 id 作为唯一标识
    }: DataTableProps<T>,
    ref: React.Ref<DataTableRef>
) {
    // 状态管理
    const {
        sorting,
        setSorting,
        columnFilters,
        setColumnFilters,
        columnVisibility,
        setColumnVisibility,
        rowSelection,
        setRowSelection,
        columnOrder,
        setColumnOrder,
        localColumnFilters,
        setLocalColumnFilters,
        localGlobalFilters,
        setLocalGlobalFilters,
        globalFilter,
        setGlobalFilter,
        debouncedGlobalFilters,
        debouncedLocalFilters,
    } = useDataTableState();

    // 行间距控制
    const [density, setDensity] = useState<DensityType>(defaultDensity);
    // 全屏控制
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isFirstRender, setIsFirstRender] = useState(true);

    // 存储所有选中的行ID
    const [selectedRowIds, setSelectedRowIds] = useState<Set<string>>(new Set());
    // Map 存储选中行的完整数据
    const [selectedRowsData] = useState<Map<string, T>>(new Map());

    useImperativeHandle(ref, () => ({
        // 刷新数据
        refresh: fetchData,
        // 获取表格状态（全局搜索、列筛选、排序）
        getState: () => ({
            globalFilters: debouncedGlobalFilters,
            columnFilters,
            sorting,
            pageSize: table.getState().pagination.pageSize,
        }),
        // 获取选中的行
        getSelectedRows: () => Array.from(selectedRowIds).map((id) => selectedRowsData.get(id)),
    }));

    useEffect(() => {
        const newColumnFilters = table
            .getAllLeafColumns()
            .filter((column) => filterTypes[column.id])
            .map((column) => ({
                id: column.id,
                value: debouncedLocalFilters[column.id] || undefined,
            }))
            .filter((filter) => filter.value !== undefined);

        setColumnFilters(newColumnFilters);
    }, [debouncedLocalFilters]);

    useEffect(() => {
        const newGlobalFilter = Object.entries(debouncedGlobalFilters).reduce(
            (acc, [key, value]) => {
                if (value !== undefined && value !== "") {
                    acc[key] = value;
                }
                return acc;
            },
            {} as Record<string, any>
        );
        setGlobalFilter(newGlobalFilter);
    }, [debouncedGlobalFilters]);

    const table = useReactTable({
        data,
        columns,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            globalFilter: debouncedGlobalFilters,
            rowSelection,
            columnOrder,
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true,
        manualFiltering: true,
        manualSorting: serverSideSort,
        enableRowSelection: enableMultiSelect,
        getRowId: (row) => getRowId(row),
        onRowSelectionChange: (updater) => {
            const oldState = rowSelection;
            const newState = typeof updater === "function" ? updater(oldState) : updater;

            setRowSelection(newState);

            // 更新selectedRowIds和selectedRowsData
            const currentPageRows = table.getRowModel().rows;
            const newSelectedIds = new Set(selectedRowIds);

            currentPageRows.forEach((row) => {
                const rowId = getRowId(row.original);
                if (newState[row.id]) {
                    newSelectedIds.add(rowId);
                    selectedRowsData.set(rowId, row.original);
                } else {
                    newSelectedIds.delete(rowId);
                    selectedRowsData.delete(rowId);
                }
            });

            setSelectedRowIds(newSelectedIds);
        },
        onColumnOrderChange: setColumnOrder,
        getSortedRowModel: serverSideSort ? undefined : getSortedRowModel(),
    });

    // 拖拽处理函数
    const handleDragEnd = useMemoizedFn((result: DragResult) => {
        if (!result.destination) return;

        const items = Array.from(table.getAllLeafColumns().map((d) => d.id));
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);

        table.setColumnOrder(items);
    });

    // 处理数据获取
    const fetchData = useMemoizedFn(async () => {
        try {
            await onFetch({
                page: table.getState().pagination.pageIndex + 1,
                pageSize: table.getState().pagination.pageSize,
                sorting,
                columnFilters,
                globalFilters: debouncedGlobalFilters,
            });
        } catch (error) {
            console.error("获取数据失败: ", error);
            noty.error("获取数据失败: " + error.message);
        }
    });

    const { pageIndex, pageSize } = table.getState().pagination;

    // 监听筛选条件变化
    useEffect(() => {
        if (isFirstRender) {
            return;
        }
        // 在使用服务器端排序时，排序变化才触发 fetchData todo fix
        if (!serverSideSort) {
            return;
        }
        table.resetPageIndex(); // 重置页码到第一页
        setRowSelection({});
        console.log("排序变化");
        fetchData();
    }, [sorting]);

    useEffect(() => {
        if (isFirstRender) {
            return;
        }
        table.resetPageIndex(); // 重置页码到第一页
        setRowSelection({});
        console.log("筛选变化");
        fetchData();
    }, [columnFilters, debouncedGlobalFilters]);

    // 单独处理分页变化
    useEffect(() => {
        if (isFirstRender) {
            return;
        }
        console.log("分页变化");
        fetchData();
    }, [pageIndex, pageSize]);

    // 初始化获取数据
    useEffect(() => {
        fetchData();
        setIsFirstRender(false);
    }, []);

    // 处理全屏切换
    const toggleFullscreen = () => {
        setIsFullscreen(!isFullscreen);
    };

    return (
        <Stack
            gap="md"
            className="tw-relative"
            style={{
                position: isFullscreen ? "fixed" : "relative",
                top: isFullscreen ? "0" : "auto",
                left: isFullscreen ? "0" : "auto",
                right: isFullscreen ? "0" : "auto",
                bottom: isFullscreen ? "0" : "auto",
                zIndex: isFullscreen ? 100 : "auto",
                background: "white",
                padding: isFullscreen ? "1rem" : "initial",
                height: isFullscreen ? "100%" : "auto",
                overflow: "auto",
                transition: "all 0.3s ease",
            }}
        >
            {/* 顶部全局搜索 */}
            {globalFilterFields.length > 0 && (
                <GlobalFilters
                    globalFilterFields={globalFilterFields}
                    localGlobalFilters={localGlobalFilters}
                    setLocalGlobalFilters={setLocalGlobalFilters}
                />
            )}

            <ActionBar
                table={table}
                onExport={onExport}
                isFullscreen={isFullscreen}
                toggleFullscreen={toggleFullscreen}
                density={density}
                setDensity={setDensity}
            />

            {/* 选择操作组 */}
            {enableMultiSelect && selectedRowIds.size > 0 && (
                <Group>
                    <Text size="sm">已选择 {selectedRowIds.size} 项</Text>
                    {selectionActions}
                </Group>
            )}

            {/* 表格主体 */}
            <ScrollArea className="tw-border tw-flex-1">
                <LoadingOverlay visible={loading} />
                <Table
                    striped
                    highlightOnHover
                    withTableBorder
                    verticalSpacing={density}
                    stickyHeader
                >
                    <TableHeader
                        table={table}
                        enableMultiSelect={enableMultiSelect}
                        filterTypes={filterTypes}
                        filterOptions={filterOptions}
                        localColumnFilters={localColumnFilters}
                        setLocalColumnFilters={setLocalColumnFilters}
                        handleDragEnd={handleDragEnd}
                    />

                    <TableBody
                        table={table}
                        enableMultiSelect={enableMultiSelect}
                    />

                    {/* 合计行 */}
                    <Table.Tfoot>
                        <Table.Tr>
                            {enableMultiSelect && <Table.Th />}
                            {table.getAllLeafColumns().map((column) => (
                                <Table.Th key={column.id}>
                                    {column.columnDef.footer
                                        ? flexRender(
                                              column.columnDef.footer,
                                              table
                                                  .getHeaderGroups()[0]
                                                  .headers.find((h) => h.id === column.id)
                                                  ?.getContext() || {}
                                          )
                                        : null}
                                </Table.Th>
                            ))}
                        </Table.Tr>
                    </Table.Tfoot>
                </Table>
            </ScrollArea>

            {/* 分页控件 */}
            <Group justify="space-between">
                <Group>
                    <Text size="sm">总共 {totalCount} 条记录</Text>
                    <Select
                        size="sm"
                        w={100}
                        data={PAGE_SIZE_OPTIONS}
                        value={pageSize.toString()}
                        onChange={(value) => {
                            const newSize = parseInt(value || "10");
                            table.setPageSize(newSize);
                        }}
                    />
                </Group>
                <Pagination
                    total={Math.ceil(totalCount / pageSize)}
                    value={pageIndex + 1}
                    onChange={(page) => table.setPageIndex(page - 1)}
                />
            </Group>
        </Stack>
    );
});
