import { useEffect } from "react";
import api from "@/apis";
import useModalStore from "@/store/modal";
import { Group, Modal, Stack, Text, SimpleGrid, Grid, Switch, Select } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import useSettingStore from "@code.8cent/store/setting";
import useDataStore from "@code.8cent/store/data";
import { t } from "@code.8cent/i18n";
import { Check, X } from "@phosphor-icons/react";
import { useShallow } from "zustand/react/shallow";
import ModalFooter from "@/components/common/ModalFooter";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, Controller } from "react-hook-form";
import dayjs from "dayjs";
import noty from "@code.8cent/react/noty";

const userSettingSchema = z.object({
    settingLanguage: z.string({
        required_error: "请输入系统语言",
        invalid_type_error: "请输入系统语言",
    }),
    settingTimezone: z.string({
        required_error: "请输入时区",
        invalid_type_error: "请输入时区",
    }),
    settingDateFormat: z.string({
        required_error: "请输入日期格式",
        invalid_type_error: "请输入日期格式",
    }),
    settingTimeFormat: z.string({
        required_error: "请输入时间格式",
        invalid_type_error: "请输入时间格式",
    }),
    settingCurrency: z.string({
        required_error: "请输入货币",
        invalid_type_error: "请输入货币",
    }),
    settingNotifyType: z.string({
        required_error: "请选择通知方式",
        invalid_type_error: "请选择通知方式",
    }),
    settingNotifyEmergency: z.number(),
    settingNotifySuspiciousOperation: z.number(),
    settingNotifySafeUpdated: z.number(),
    settingNotifyRecPrivateMsg: z.number(),
    settingNotifyImportanceUpdate: z.number(),
    settingNotifySystemUpdate: z.number(),
    settingNotifyJoinInvestigate: z.number(),
    settingNotifyBill: z.number(),
});

type UserProfileResponse = z.infer<typeof userSettingSchema> & {
    profileID: number;
};

type TPersonalSettingModalProps = {
    onUpdateSettingSuccess: () => {};
};

const PersonalSetting = ({ onUpdateSettingSuccess }: TPersonalSettingModalProps) => {
    const lang = useSettingStore.use.lang();
    const openConfirm = useModalStore.use.openConfirm();
    const { timeFormats, timezones, languages, dateFormats, currencies } = useDataStore();

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.associatePersonalSettingModal,
            close: state.close,
        }))
    );

    // 获取传入的参数
    const profileParams = useModalStore((state) => state.modalParams.associatePersonalSettingModal);
    const profile = profileParams?.profile;

    const {
        handleSubmit,
        formState: { errors },
        control,
        reset,
    } = useForm<UserProfileResponse>({
        defaultValues: {
            ...profile,
            settingNotifyType: String(profile?.settingNotifyType || 0),
        },
        resolver: zodResolver(userSettingSchema),
    });

    useEffect(() => {
        if (profile) {
            reset({
                settingLanguage: profile.settingLanguage,
                settingTimezone: profile.settingTimezone,
                settingDateFormat: profile.settingDateFormat,
                settingTimeFormat: profile.settingTimeFormat,
                settingCurrency: profile.settingCurrency,
                settingNotifyType: String(profile.settingNotifyType || 0),
                settingNotifyEmergency: profile.settingNotifyEmergency,
                settingNotifySuspiciousOperation: profile.settingNotifySuspiciousOperation,
                settingNotifySafeUpdated: profile.settingNotifySafeUpdated,
                settingNotifyRecPrivateMsg: profile.settingNotifyRecPrivateMsg,
                settingNotifyImportanceUpdate: profile.settingNotifyImportanceUpdate,
                settingNotifySystemUpdate: profile.settingNotifySystemUpdate,
                settingNotifyJoinInvestigate: profile.settingNotifyJoinInvestigate,
                settingNotifyBill: parseInt(profile.settingNotifyBill),
            });
        } else {
            reset({
                settingLanguage: "",
                settingTimezone: "",
                settingDateFormat: "",
                settingTimeFormat: "",
                settingCurrency: "",
                settingNotifyType: "0",
                settingNotifyEmergency: 0,
                settingNotifySuspiciousOperation: 0,
                settingNotifySafeUpdated: 0,
                settingNotifyRecPrivateMsg: 0,
                settingNotifyImportanceUpdate: 0,
                settingNotifySystemUpdate: 0,
                settingNotifyJoinInvestigate: 0,
                settingNotifyBill: 0,
            });
        }
    }, [profile, reset]);

    useEffect(() => {
        if (isVisible === false) {
            reset();
        }
    }, [isVisible]);

    const closeModal = useMemoizedFn(() => {
        close("associatePersonalSettingModal");
    });

    const submitForm = async (data: UserProfileResponse) => {
        openConfirm({
            title: "保存",
            message: "您确定要保存合伙人的个人设置信息吗？",
            onConfirm: async () => {
                try {
                    const res = await api.associate.updateSetting(
                        {
                            ...data,
                            notifyType: Number(data.settingNotifyType),
                            notifyEmergency: data.settingNotifyEmergency,
                            notifySuspiciousOperation: data.settingNotifySuspiciousOperation,
                            notifySafeUpdated: data.settingNotifySafeUpdated,
                            notifyRecPrivateMsg: data.settingNotifyRecPrivateMsg,
                            notifyImportanceUpdate: data.settingNotifyImportanceUpdate,
                            notifySystemUpdate: data.settingNotifySystemUpdate,
                            notifyJoinInvestigate: data.settingNotifyJoinInvestigate,
                            notifyBill: data.settingNotifyBill,
                        },
                        profile.profileID
                    );

                    if (res) {
                        noty.success("保存成功");
                        close("associatePersonalSettingModal");
                        onUpdateSettingSuccess();
                    }
                } catch (error) {
                    console.log(error);
                }
            },
        });
    };

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title="个人设置"
            size="xl"
        >
            <form
                onSubmit={handleSubmit(submitForm, (errors) => {
                    console.log(errors);
                })}
            >
                <Stack gap="md">
                    <Text>语言与地区</Text>
                    <SimpleGrid cols={2}>
                        <Stack>
                            <Controller
                                name="settingLanguage"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        label={t("setting.lang_area.label.system_language", lang)}
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        data={languages.map((lang) => ({
                                            value: String(lang.languageCode),
                                            label: String(lang.languageType),
                                        }))}
                                        error={errors.settingLanguage?.message}
                                    />
                                )}
                            />
                            <Controller
                                name="settingDateFormat"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        label={t("setting.lang_area.label.date_format", lang)}
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        data={dateFormats.map((format) => ({
                                            value: String(format.dateFormat),
                                            label: `${format.dateFormat} ${dayjs().format(
                                                format.dateFormat
                                            )}`,
                                        }))}
                                        error={errors.settingDateFormat?.message}
                                    />
                                )}
                            />
                        </Stack>
                        <Stack>
                            <Controller
                                name="settingTimezone"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        searchable
                                        label={t("setting.lang_area.label.timezone", lang)}
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        data={timezones.map((timezone) => ({
                                            value: String(timezone.timeZone),
                                            label: `${timezone.timeZone.replace("_", " ")} ${
                                                timezone.timeZoneGMT
                                            }`,
                                        }))}
                                        error={errors.settingTimezone?.message}
                                    />
                                )}
                            />
                            <Controller
                                name="settingTimeFormat"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        label={t("setting.lang_area.label.time_format", lang)}
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        data={timeFormats.map((format) => ({
                                            value: String(format.timeFormat),
                                            label: `${format.timeFormat} ${dayjs().format(
                                                format.timeFormat
                                            )}`,
                                        }))}
                                        error={errors.settingTimeFormat?.message}
                                    />
                                )}
                            />
                        </Stack>
                    </SimpleGrid>
                    <Controller
                        name="settingCurrency"
                        control={control}
                        render={({ field }) => (
                            <Select
                                {...field}
                                searchable
                                label={t("setting.lang_area.label.currency", lang)}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                data={currencies.map((currency) => ({
                                    value: String(currency.currencyCode),
                                    label: `${currency.currencyName} - ${currency.currencyCode}`,
                                }))}
                                error={errors.settingCurrency?.message}
                            />
                        )}
                    />

                    <Text>信息与通知</Text>
                    <Controller
                        name="settingNotifyType"
                        control={control}
                        render={({ field }) => (
                            <Select
                                {...field}
                                label="通知方式"
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                data={[
                                    {
                                        value: "1",
                                        label: "电子邮件",
                                    },
                                    {
                                        value: "2",
                                        label: "短信",
                                        disabled: true,
                                    },
                                ]}
                                error={errors.settingNotifyType?.message}
                            />
                        )}
                    />

                    <Text
                        size="sm"
                        className="profile-form-label"
                    >
                        通知设置
                    </Text>
                    <Stack>
                        <Grid>
                            <Grid.Col span={{ base: 12, md: 6 }}>
                                <Group justify="space-between">
                                    <Text>安全设置被更改</Text>
                                    <Controller
                                        name="settingNotifySafeUpdated"
                                        control={control}
                                        render={({ field }) => (
                                            <Switch
                                                size="sm"
                                                checked={Boolean(field.value)}
                                                onChange={(e) =>
                                                    field.onChange(e.currentTarget.checked ? 1 : 0)
                                                }
                                                error={errors.settingNotifySafeUpdated?.message}
                                            />
                                        )}
                                    />
                                </Group>
                                <Group justify="space-between">
                                    <Text>可疑操作</Text>
                                    <Controller
                                        name="settingNotifySuspiciousOperation"
                                        control={control}
                                        render={({ field }) => (
                                            <Switch
                                                size="sm"
                                                checked={Boolean(field.value)}
                                                onChange={(e) =>
                                                    field.onChange(e.currentTarget.checked ? 1 : 0)
                                                }
                                                error={
                                                    errors.settingNotifySuspiciousOperation?.message
                                                }
                                            />
                                        )}
                                    />
                                </Group>
                                <Group justify="space-between">
                                    <Text>重要更新</Text>
                                    <Controller
                                        name="settingNotifyImportanceUpdate"
                                        control={control}
                                        render={({ field }) => (
                                            <Switch
                                                size="sm"
                                                checked={Boolean(field.value)}
                                                onChange={(e) =>
                                                    field.onChange(e.currentTarget.checked ? 1 : 0)
                                                }
                                                error={
                                                    errors.settingNotifyImportanceUpdate?.message
                                                }
                                            />
                                        )}
                                    />
                                </Group>
                                <Group justify="space-between">
                                    <Text>系统更新</Text>
                                    <Controller
                                        name="settingNotifySystemUpdate"
                                        control={control}
                                        render={({ field }) => (
                                            <Switch
                                                size="sm"
                                                checked={Boolean(field.value)}
                                                onChange={(e) =>
                                                    field.onChange(e.currentTarget.checked ? 1 : 0)
                                                }
                                                error={errors.settingNotifySystemUpdate?.message}
                                            />
                                        )}
                                    />
                                </Group>
                            </Grid.Col>
                            <Grid.Col span={{ base: 12, md: 6 }}>
                                <Group justify="space-between">
                                    <Text>紧急通知</Text>
                                    <Controller
                                        name="settingNotifyEmergency"
                                        control={control}
                                        render={({ field }) => (
                                            <Switch
                                                size="sm"
                                                checked={Boolean(field.value)}
                                                onChange={(e) =>
                                                    field.onChange(e.currentTarget.checked ? 1 : 0)
                                                }
                                                error={errors.settingNotifyEmergency?.message}
                                            />
                                        )}
                                    />
                                </Group>
                                <Group justify="space-between">
                                    <Text>收到私信</Text>
                                    <Controller
                                        name="settingNotifyRecPrivateMsg"
                                        control={control}
                                        render={({ field }) => (
                                            <Switch
                                                size="sm"
                                                checked={Boolean(field.value)}
                                                onChange={(e) =>
                                                    field.onChange(e.currentTarget.checked ? 1 : 0)
                                                }
                                                error={errors.settingNotifyRecPrivateMsg?.message}
                                            />
                                        )}
                                    />
                                </Group>
                                <Group justify="space-between">
                                    <Text>参与调查</Text>
                                    <Controller
                                        name="settingNotifyJoinInvestigate"
                                        control={control}
                                        render={({ field }) => (
                                            <Switch
                                                size="sm"
                                                checked={Boolean(field.value)}
                                                onChange={(e) =>
                                                    field.onChange(e.currentTarget.checked ? 1 : 0)
                                                }
                                                error={errors.settingNotifyJoinInvestigate?.message}
                                            />
                                        )}
                                    />
                                </Group>
                                <Group justify="space-between">
                                    <Text>账单通知</Text>
                                    <Controller
                                        name="settingNotifyBill"
                                        control={control}
                                        render={({ field }) => (
                                            <Switch
                                                size="sm"
                                                checked={Boolean(field.value)}
                                                onChange={(e) =>
                                                    field.onChange(e.currentTarget.checked ? 1 : 0)
                                                }
                                                error={errors.settingNotifyBill?.message}
                                            />
                                        )}
                                    />
                                </Group>
                            </Grid.Col>
                        </Grid>
                    </Stack>

                    <ModalFooter
                        // timelineContent="最近修改: Amos Wu (2024-11-18 12:00:00)"
                        buttons={[
                            {
                                key: "save",
                                label: "保存",
                                leftSection: <Check size={16} />,
                                type: "submit",
                            },
                            {
                                key: "close",
                                label: "关闭",
                                style: "outline",
                                leftSection: <X size={16} />,
                                onClick: closeModal,
                            },
                        ]}
                    />
                </Stack>
            </form>
        </Modal>
    );
};

export default PersonalSetting;
