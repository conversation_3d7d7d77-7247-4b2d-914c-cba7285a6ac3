import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Stack, Title, Group, Text } from "@mantine/core";
import { ArrowCircleRight } from "@phosphor-icons/react";
import { useMemoizedFn, useSetState, useTitle } from "ahooks";
import SettingPageContext, { initialState, SettingModalState } from "./context";
import SettingLoginModal from "./modals/SettingLoginModal";
import SettingActivityModal from "./modals/SettingActivityModal";
import SettingNotificationModal from "./modals/SettingNotificationModal";
import SettingSupportModal from "./modals/SettingSupportModal";
import SettingRegionModal from "./modals/SettingRegionModal";
import SettingPasswordModal from "./modals/SettingPasswordModal";
import { CnaButton, PageHeader } from "@code.8cent/react/components";
import { useNavigate } from "react-router-dom";

const SettingPage = () => {
    const lang = useSettingStore.use.lang();
    const navigate = useNavigate();

    const [modalState, setModalState] = useSetState<SettingModalState>(initialState);

    useTitle(`${t("setting.title", lang)} | ${window.app_title}`, {
        restoreOnUnmount: true,
    });

    const open = useMemoizedFn((modal: keyof SettingModalState) => {
        setModalState((state) => ({ ...state, [modal]: true }));
    });

    const close = useMemoizedFn((modal: keyof SettingModalState) => {
        setModalState((state) => ({ ...state, [modal]: false }));
    });

    const settings: { label: string; modal: keyof SettingModalState }[] = [
        {
            label: t("setting.lang_area", lang),
            modal: "SettingRegionModal",
        },
        {
            label: t("setting.info_notification", lang),
            modal: "SettingNotificationModal",
        },
        {
            label: t("setting.active_log", lang),
            modal: "SettingActivityModal",
        },
        {
            label: t("setting.login_history", lang),
            modal: "SettingLoginModal",
        },
        {
            label: t("setting.help", lang),
            modal: "SettingSupportModal",
        },
        {
            label: "修改登录密码",
            modal: "SettingPasswordModal",
        },
    ];

    return (
        <SettingPageContext.Provider value={{ ...modalState, open, close }}>
            <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
                <PageHeader
                    title={t("setting.title", lang)}
                    desc={t("navigation.setting.system.quote", lang)}
                />
                <Stack className="tw-px-0">
                    {settings.map((setting, index) => (
                        <Group
                            key={index}
                            className="tw-justify-between tw-px-5 tw-py-5 tw-border-b tw-cursor-pointer"
                            onClick={() => {
                                open(setting.modal);
                            }}
                        >
                            <Text>{setting.label}</Text>
                            <ArrowCircleRight size={28} />
                        </Group>
                    ))}
                    {/* todo remove */}
                    {/* <CnaButton
                        className="tw-w-full tw-bg-primary tw-text-white"
                        onClick={() => navigate("/admin/web/structures")}
                    >
                        设置网页多语言
                    </CnaButton> */}
                </Stack>
            </Stack>
            <SettingRegionModal />
            <SettingNotificationModal />
            <SettingActivityModal />
            <SettingLoginModal />
            <SettingSupportModal />
            <SettingPasswordModal />
        </SettingPageContext.Provider>
    );
};

export default SettingPage;
