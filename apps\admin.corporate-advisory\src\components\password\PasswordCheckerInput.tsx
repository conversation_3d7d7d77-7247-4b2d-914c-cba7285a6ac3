import { Group, PasswordInput, Stack, Text } from "@mantine/core";
import { Check, X } from "@phosphor-icons/react";
import { useState } from "react";
import { Control, Path, useController, UseFormTrigger } from "react-hook-form";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

type PasswordCheckerInputProps<T = any> = {
    label: string;
    name: Path<T>;
    control: Control<T>;
    trigger?: UseFormTrigger<T>;
    inputProps?: React.ComponentProps<typeof PasswordInput>;
    rulerProps?: React.ComponentProps<typeof Stack>;
};

// 密码强度规则
const strengthRules = [
    {
        text: "password.requirement.letter",
        validate: (password: string) => password.length >= 8,
    },
    {
        text: "password.requirement.capital.letter",
        validate: (password: string) => /[A-Z]/.test(password),
    },
    {
        text: "password.requirement.small.letter",
        validate: (password: string) => /[a-z]/.test(password),
    },
    {
        text: "password.requirement.number",
        validate: (password: string) => /\d/.test(password),
    },
] as const;

const PasswordCheckerInput = <T,>({
    label,
    name,
    control,
    trigger,
    inputProps,
    rulerProps
}: PasswordCheckerInputProps<T>) => {
    const lang = useSettingStore.use.lang();

    const [isFocused, setIsFocused] = useState(false); // 控制 checklist 显示状态

    // 使用 useController 控制字段状态和数据
    const {
        field: { value: password, onChange, onBlur },
        fieldState: { error },
    } = useController({
        name, // 传入表单字段名
        control,
    });

    const handleFocus = () => setIsFocused(true);
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        onBlur(); // 保留 react-hook-form 的 onBlur 行为
    };

    return (
        <Stack gap={0} className="tw-mb-3">
            <PasswordInput
                error={error ? true : false}
                {...inputProps}
                label={label}
                value={String(password) || ""}
                onChange={onChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
            />

            {isFocused === true && (
                <Stack gap={1} {...rulerProps}>
                    {strengthRules.map((rule, index) => {
                        const isValid = rule.validate(String(password) || "");
                        return (
                            <Group key={index} gap={2}>
                                {isValid ? (
                                    <Check
                                        weight="bold"
                                        size={12}
                                        className="tw-text-green-600"
                                    />
                                ) : (
                                    <X
                                        weight="bold"
                                        size={12}
                                        className="tw-text-[var(--mantine-color-error)]"
                                    />
                                )}
                                <Text
                                    size="xs"
                                    className={
                                        isValid
                                            ? "tw-text-green-600"
                                            : "tw-text-[var(--mantine-color-error)]"
                                    }
                                >
                                    {t(rule.text, lang)}
                                </Text>
                            </Group>
                        );
                    })}
                </Stack>
            )}
        </Stack>
    );
};

export default PasswordCheckerInput;
