import AuthenticationLayout from "@code.8cent/react/layouts/AuthenticationLayout";
import AuthFormCard from "@code.8cent/react/components/AuthFormCard";
import RegisterEmailPhoneForm from "@/components/register/EmailPhoneForm";
import RegisterInformationForm from "@/components/register/InformationForm";
import RegisterPay from "@/components/register/Pay";
import PayResult from "@/components/register/PayResult";
import { useEventBus } from "@/utils/eventBus";
import { useMount, useTitle, useUnmount } from "ahooks";
import { useState } from "react";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Box } from "@mantine/core";

const AccountRegisterPage = () => {
    const lang = useSettingStore.use.lang();

    useTitle(`${t("register.sign.up.title", lang)} | ${window.app_title}`);

    const [step, setStep] = useState<number>(1);

    const bus = useEventBus();

    useMount(() => {
        bus.on("account.register.step", setStep);
    });

    useUnmount(() => {
        bus.off("account.register.step", setStep);
    });

    return (
        <AuthenticationLayout>
            {step === 1 && (
                <AuthFormCard
                    className="tw-w-[600px]"
                    title={t("register.title", lang)}
                >
                    <RegisterEmailPhoneForm />
                </AuthFormCard>
            )}
            {step === 2 && (
                <AuthFormCard className="tw-w-[1200px]">
                    <RegisterInformationForm />
                </AuthFormCard>
            )}
            {step === 3 && (
                <AuthFormCard className="tw-w-[600px]">
                    <RegisterPay />
                </AuthFormCard>
            )}
            {step === 4 && (
                <AuthFormCard className="tw-w-[600px]">
                    <PayResult />
                </AuthFormCard>
            )}
        </AuthenticationLayout>
    );
};

export default AccountRegisterPage;
