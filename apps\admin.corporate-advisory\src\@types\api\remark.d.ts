declare global {
    type TRemarkSearchParams = {
        objID: number;
        type: number; // 类别: 1合伙人;2行政;;3律师;4客户
        keyword?: string | null;
    } & TPageQueryParams;

    // todo
    type TRemark = {
        remarkID: number;
        remarkType: number;
        objID: number;
        remarkContent: string;
        editUser: number;
        editRole: number;
        created_at: string;
        updated_at: string;
        edit_user: {
            profileID: number;
            profileName: string;
        } | null;
    }

    type TStoreRemarkParams = {
        remarkType: number;
        objID: number;
        remarkContent: string;
    };

    type TUpdateRemarkParams = {
        remarkContent: string;
    };

    type TRemarksResponse = {
        items: TRemark[];
        paginate: BasePaginateResponse;
    };
}

export {};
