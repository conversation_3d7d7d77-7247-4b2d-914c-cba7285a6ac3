import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const team = {
    list: async (params: TTeamSearchParams) => {
        const { error, result } = await cnaRequest<TTeamsResponse>(
            "/api/v1/admin/teamProfile/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (params: TTeamStoreParams) => {
        const { error } = await cnaRequest(
            `/api/v1/admin/teamProfile/create`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (id: number, params: TTeamStoreParams) => {
        const { error } = await cnaRequest(
            `/api/v1/admin/teamProfile/edit/${id}`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    getRankOptions: async () => {
        const { error, result } = await cnaRequest<TRankOption[]>(
            `/api/v1/admin/teamProfile/rank-options`,
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default team;
