import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, PasswordInput, Space, Text, Title } from "@mantine/core";
import { useEffect, useState } from "react";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import PasswordCheckerInput from "../password/PasswordCheckerInput";
import PasswordMatcherInput from "../password/PasswordMatcherInput";
import { z } from "zod";
import { useMount, useRequest, useUnmount } from "ahooks";
import api from "@/apis";
import { useEventBus } from "@/utils/eventBus";
import { notifications } from "@mantine/notifications";
import useWizardStore from "@code.8cent/store/wizard";
import { SHA256 } from "crypto-js";

type ResetPasswordFormInput = {
    currentPassword: string;
    newPassword: string;
    confirmNewPassword: string;
};

const resetPasswordSchema = z
    .object({
        currentPassword: z.string().min(1, "password.current.empty"),
        newPassword: z.string().min(1, "password.new.empty"),
        confirmNewPassword: z.string().min(1, "password.enter.empty"),
    })
    .refine((data) => data.newPassword === data.confirmNewPassword, {
        message: "password.enter.not.match",
        path: ["confirmNewPassword"],
    });

const initalValues = {
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
};

const SecuritySetup = () => {
    const { lang } = useSettingStore();

    const { setState: setWizardState } = useWizardStore();

    const bus = useEventBus();

    const {
        register,
        handleSubmit,
        formState: { errors },
        control,
        trigger,
        reset,
    } = useForm<ResetPasswordFormInput>({
        defaultValues: initalValues,
        resolver: zodResolver(resetPasswordSchema),
        mode: "all",
    });

    const { run: submit, loading } = useRequest(
        async (data: ResetPasswordFormInput) => {
            let res = await api.user.setSecurity({
                currentPassword: SHA256(data.currentPassword).toString(),
                newPassword: SHA256(data.newPassword).toString(),
                passwordConfirmation: SHA256(data.confirmNewPassword).toString(),
            });

            if(res){
                setWizardState(res.state);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        bus.emit("wizard.submitting", loading);
    }, [loading])

    useMount(() => {
        bus.on("wizard.submit.click", handleSubmit(submit));
    });

    useUnmount(() => {
        bus.emit("wizard.submitting", false);
        bus.off("wizard.submit.click");
    })

    return (
        <Box>
            <Title order={6}>更改密码</Title>
            <Text size="xs">建议你使用高强度密码，不要与其他地方重复。</Text>
            <Space h={32} />
            <PasswordInput
                className="tw-mb-6"
                label={t("password.current", lang)}
                placeholder={t("password.current.details", lang)}
                {...register("currentPassword")}
                error={errors.currentPassword ? true : false}
                required
            />

            <PasswordCheckerInput<ResetPasswordFormInput>
                label={t("password.new", lang)}
                name="newPassword"
                control={control}
                trigger={trigger}
                inputProps={{
                    className: "tw-mb-6",
                    placeholder: t("password.new.details", lang),
                    required: true,
                }}
            />

            <PasswordMatcherInput<ResetPasswordFormInput>
                label={t("password.confirm", lang)}
                name="confirmNewPassword"
                control={control}
                trigger={trigger}
                inputProps={{
                    className: "tw-mb-6",
                    placeholder: t("password.reconfirm", lang),
                    required: true,
                }}
            />
        </Box>
    );
};

export default SecuritySetup;
