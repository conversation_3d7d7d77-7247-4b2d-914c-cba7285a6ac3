import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Box, Modal, Table, Switch, LoadingOverlay } from "@mantine/core";
import { useEffect, useState, useCallback, useMemo } from "react";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";

interface PermissionItem {
    powerActionID?: number;
    powerID?: number;
    structureCode?: string;
    powerActionCode?: string;
    code: string;
    check: boolean;
}

interface Permission {
    label: string;
    name: string;
    items: PermissionItem[];
}

const Permission = () => {
    const { lang } = useSettingStore();
    const openConfirm = useModalStore.use.openConfirm();

    const modalParams = useModalStore((state) => state.modalParams.accountPermissionModal);
    const profileID = modalParams?.profileID;

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.accountPermissionModal,
            close: state.close,
        }))
    );

    // 添加加载状态
    const [loading, setLoading] = useState(true);

    // 初始化权限状态
    const DEFAULT_PERMISSION_ITEMS = useMemo(
        () => [
            { structureCode: "view", powerActionCode: "view", code: "view", check: false },
            { structureCode: "edit", powerActionCode: "edit", code: "edit", check: false },
            { structureCode: "delete", powerActionCode: "delete", code: "delete", check: false },
            { structureCode: "setting", powerActionCode: "setting", code: "setting", check: false },
            { structureCode: "export", powerActionCode: "export", code: "export", check: false },
        ],
        []
    );

    const [permissions, setPermissions] = useState<Permission[]>([
        {
            label: "all",
            name: "全部选择",
            items: DEFAULT_PERMISSION_ITEMS,
        },
    ]);

    const closeModal = useMemoizedFn(() => {
        setPermissions([
            {
                label: "all",
                name: "全部选择",
                items: DEFAULT_PERMISSION_ITEMS,
            },
        ]);
        close("accountPermissionModal");
    });

    const fetchPermissions = useMemoizedFn(async () => {
        try {
            setLoading(true);
            const permissions = await api.account.permissions(profileID);
            const updatedPermissions = permissions.map((permission) => ({
                label: permission.structureCode,
                name: permission.structureCode,
                items: permission.power_actions.map((item) => ({
                    ...item,
                    code: item.structureCode.split(".").pop() || "",
                })),
            }));

            setPermissions((prev) => {
                const allRow = prev[0]; // "全部"行
                const updatedAllRow = {
                    ...allRow,
                    items: allRow.items.map((item) => {
                        // 检查其他所有权限行中当前列是否全部选中
                        const allCheckedForField = updatedPermissions.every(
                            (permission) =>
                                permission.items.find((i) => i.code === item.code)?.check ?? false
                        );
                        return {
                            ...item,
                            check: allCheckedForField,
                        };
                    }),
                };

                return [updatedAllRow, ...updatedPermissions];
            });
        } catch (error) {
            console.error("Error fetching permissions:", error);
            noty.error("获取权限失败");
        } finally {
            setLoading(false);
        }
    });

    useEffect(() => {
        if (isVisible) {
            fetchPermissions();
        }
    }, [isVisible, fetchPermissions]);

    const handleToggle = useMemoizedFn((label, field) => {
        setPermissions((prev) => {
            const updatedPermissions = prev.map((permission) => {
                const isCurrent = permission.label === label;
                const updatedItems = permission.items.map((item) => ({
                    ...item,
                    check: isCurrent && item.code === field ? !item.check : item.check,
                }));

                return {
                    ...permission,
                    items: updatedItems,
                };
            });

            // 计算除"all"之外的所有权限中，指定field的选中状态
            const allOtherPermissions = updatedPermissions.filter((p) => p.label !== "all");
            const allCheckedForField = allOtherPermissions.every(
                (permission) => permission.items.find((item) => item.code === field)?.check
            );

            // 更新"all"行对应field的状态
            return updatedPermissions.map((permission) => {
                if (permission.label === "all") {
                    return {
                        ...permission,
                        items: permission.items.map((item) => ({
                            ...item,
                            check: item.code === field ? allCheckedForField : item.check,
                        })),
                    };
                }
                return permission;
            });
        });
    });

    const handleAllToggle = useMemoizedFn((code) => {
        setPermissions((prev) => {
            // 更新 "全部" 列的状态
            const newCheckState = !prev[0].items.find((item) => item.code === code).check;

            return prev.map((permission) => ({
                ...permission,
                items: permission.items.map((item) => ({
                    ...item,
                    check: item.code === code ? newCheckState : item.check,
                })),
            }));
        });
    });

    // 获取选中权限的逻辑
    const getSelectedPermissions = useCallback(() => {
        return permissions.flatMap((permission) =>
            permission.items
                .filter((item) => item.check && item.powerID !== undefined)
                .map((item) => ({
                    powerActionCode: item.powerActionCode,
                    powerID: item.powerID,
                }))
        );
    }, [permissions]);

    const handleConfirm = useCallback(async () => {
        try {
            const selectedPermissions = getSelectedPermissions();

            const res = await api.account.updatePermissions({
                user_id: profileID,
                data: JSON.stringify(selectedPermissions),
            });

            if (res) {
                noty.success("更新权限成功");
                closeModal();
            }
        } catch (error) {
            console.error("Error confirming review:", error);
            noty.error("更新权限失败");
        }
    }, [profileID, getSelectedPermissions, closeModal]);

    const handlePass = useCallback(() => {
        openConfirm({
            title: "提示",
            message: "您确定通过该申请吗？",
            onConfirm: handleConfirm,
        });
    }, [openConfirm, handleConfirm]);

    const tableBody = useMemo(
        () =>
            permissions.map((permission) => (
                <Table.Tr key={permission.label}>
                    <Table.Td>{t(`${permission.name}`, lang)}</Table.Td>
                    {permission.items.map((item) => (
                        <Table.Td
                            key={item.code}
                            className="tw-text-center"
                        >
                            <Switch
                                checked={item.check}
                                onChange={() => {
                                    if (permission.label === "all") {
                                        handleAllToggle(item.code);
                                    } else {
                                        handleToggle(permission.label, item.code);
                                    }
                                }}
                                size="md"
                            />
                        </Table.Td>
                    ))}
                </Table.Tr>
            )),
        [permissions, handleToggle]
    );

    // 表头
    const TABLE_HEADERS = useMemo(
        () => [
            { key: "name", label: "#" },
            { key: "view", label: "查看" },
            { key: "edit", label: "编辑" },
            { key: "delete", label: "删除" },
            { key: "setting", label: "设置" },
            { key: "export", label: "导出" },
        ],
        []
    );

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title="权限设置"
            size="xl"
            centered
        >
            <LoadingOverlay visible={loading} />

            <Table
                striped
                withTableBorder
                withColumnBorders
            >
                <Table.Thead>
                    <Table.Tr>
                        {TABLE_HEADERS.map((header) => (
                            <Table.Th
                                key={header.key}
                                className={header.key !== "name" ? "tw-text-center" : ""}
                            >
                                {header.label}
                            </Table.Th>
                        ))}
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody>{tableBody}</Table.Tbody>
            </Table>

            <ModalFooter
                buttons={[
                    {
                        key: "save",
                        label: "保存",
                        leftSection: <Check size={16} />,
                        onClick: handlePass,
                    },
                    {
                        key: "close",
                        label: "关闭",
                        style: "outline",
                        leftSection: <X size={16} />,
                        onClick: closeModal,
                    },
                ]}
            />
        </Modal>
    );
};

export default Permission;
