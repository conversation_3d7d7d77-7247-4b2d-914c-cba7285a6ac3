import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn, useMount } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { GearSix, Plus } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import dayjs from "dayjs";
import Info from "@/components/modals/notice/Info";
import { getNoticeStatus } from "@/utils/constants";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";

const columnHelper = createColumnHelper<TNotice>();

const AdminNoticesPage = () => {
    const lang = useSettingStore.use.lang();

    const openModal = useModalStore.use.open();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const [notice, setNotice] = useState<TNotice | null>(null);

    // 定义状态值常量
    const STATUS_MAP = getNoticeStatus(lang);

    useMount(() => {});

    const pageButtons = [
        {
            key: "add",
            label: "创建",
            leftSection: <Plus size={14} />,
            onClick: () => openModal("noticeInfoModal"),
        },
        {
            key: "setting",
            label: "设置",
            leftSection: <GearSix size={14} />,
            onClick: () => {},
        },
    ];

    const rowActions = (row) => [
        {
            key: "profile",
            label: "简介信息",
            onClick: () => {
                setNotice(row);
                openModal("noticeInfoModal");
            },
        },
    ];

    const refreshTable = useMemoizedFn(() => {
        if (tableRef.current) {
            tableRef.current.refresh();
        }
    });

    const handleUpdateSuccess = useMemoizedFn(() => {
        setNotice(null);
        refreshTable();
    });

    const handleClose = useMemoizedFn(() => {
        setNotice(null);
    });

    const tableColumns = [
        columnHelper.accessor(`notificationTitle${lang}`, {
            header: "标题",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor(`notificationState`, {
            header: "状态",
            enableSorting: false,
            cell: (info) => t(`notice.state.${info.getValue()}`, lang),
        }),
        columnHelper.accessor("notificationValidity", {
            header: "有效期",
            enableSorting: false,
            cell: (info) =>
                (info.getValue() &&
                    dayjs(info.getValue() as string).format("YYYY-MM-DD HH:mm:ss")) ||
                "永久",
        }),
        columnHelper.accessor("notificationDepartment", {
            header: "针对群体",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("createTime", {
            header: "发布日期",
            enableSorting: false,
            cell: (info) =>
                info.getValue() && dayjs(info.getValue() as string).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("created_at", {
            header: "创建日期",
            enableSorting: false,
            cell: (info) =>
                info.getValue() && dayjs(info.getValue() as string).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        }),
    ];

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.notice.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="通知"
                desc="查询通知"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
            />

            <Info
                notice={notice}
                onUpdateSuccess={handleUpdateSuccess}
                onClose={handleClose}
            />
        </Stack>
    );
};

export default AdminNoticesPage;
