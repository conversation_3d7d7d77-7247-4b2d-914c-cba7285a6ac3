import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import { PageHeader, DataTable, DataTableRef, CnaButton } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import Report from "@/components/modals/visitor/Report";
import { exportToExcel } from "@/utils/xlsx";
import { exportToPDF } from "@/utils/pdf";

const columnHelper = createColumnHelper<TVisitorReport>();

const TYPE_MAP = [
    { label: "来访登记", value: "1" },
    { label: "贵宾登记", value: "2" },
];

const AdminVisitorReportsPage = () => {
    const { lang } = useSettingStore();
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState<TVisitorReport[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "序号",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("report_author", {
            header: "报告作者",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("visitor_date", {
            header: "到访日期",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("visitor_time", {
            header: "来访时间",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("type", {
            header: "报告类型",
            enableSorting: false,
            cell: (info) =>
                TYPE_MAP.find((item) => item.value === info.getValue().toString())?.label,
        }),
        columnHelper.accessor("created_at", {
            header: "创建日期",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => (
                <CnaButton
                    variant="outline"
                    size="xs"
                    onClick={() => {
                        viewReportDetail(info.row.original);
                    }}
                >
                    详情
                </CnaButton>
            ),
        }),
    ];

    // 报告详情
    const viewReportDetail = useMemoizedFn(async (report: TVisitorReport) => {
        const reportRes = await api.visitor.reportDetail(report.id);
        if (reportRes) {
            openModal("visitorReportModal", { report: reportRes });
        }
    });

    const handleFetch = useMemoizedFn(async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                start_date: globalFilters?.dateRange?.[0] || "",
                end_date: globalFilters?.dateRange?.[1] || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.visitor.reportList(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    });

    // 导出数据
    const handleExport = useMemoizedFn(async () => {
        const { globalFilters } = tableRef.current?.getState();

        const requestParams = {
            keyword: globalFilters?.keyword || "",
            start_date: globalFilters?.dateRange?.[0] || "",
            end_date: globalFilters?.dateRange?.[1] || "",
            page: 1,
            pageSize: totalCount,
        };

        const { items } = await api.visitor.reportList(requestParams);

        exportToExcel(
            items,
            [
                { key: "id", title: "序号" },
                { key: "type", title: "报告类型", format: (value) => TYPE_MAP[value] },
                { key: "report_author", title: "报告作者" },
                { key: "visitor_date", title: "到访日期" },
                { key: "visitor_time", title: "来访时间" },
                { key: "visitor_purpose", title: "来访目的" },
                { key: "expect", title: "期望" },
                { key: "content", title: "沟通/会议内容概述" },
                { key: "developments", title: "详细进展" },
                { key: "result", title: "讨论结果及评估" },
                { key: "follow_content", title: "后续跟进计划内容" },
                { key: "remark", title: "备注" },
                {
                    key: "created_at",
                    title: "创建日期",
                    format: (value) => dayjs(value).format("YYYY-MM-DD HH:mm:ss"),
                },
            ],
            `C&A贵宾登记记录_${dayjs().format("YYYYMMDD")}.xlsx`
        );
    });

    const confirmExport = useMemoizedFn(async () => {
        openConfirm({
            title: "提示",
            message: "确定要导出数据吗？",
            onConfirm: handleExport,
        });
    });

    // 导出PDF
    const handleExportPDF = useMemoizedFn(async () => {
        const selectedRows = tableRef.current?.getSelectedRows();
        if (!selectedRows?.length) {
            openConfirm({
                title: "提示",
                message: "请先选择要导出的数据",
                onConfirm: () => {},
            });
            return;
        }

        await exportToPDF({
            title: 'C&A会谈结果报告',
            data: selectedRows,
            fileName: 'C&A会谈结果报告',
            fields: [
                { key: 'type', label: '报告类型', format: (value) => TYPE_MAP.find((item) => item.value === value.toString())?.label || '' },
                { key: 'report_author', label: '报告作者' },
                { key: 'visitor_date', label: '到访日期' },
                { key: 'visitor_time', label: '来访时间' },
                { key: 'visitor_purpose', label: '来访目的' },
                { key: 'expect', label: '期望' },
                { key: 'content', label: '沟通/会议内容概述' },
                { key: 'developments', label: '详细进展' },
                { key: 'result', label: '讨论结果及评估' },
                { key: 'follow_content', label: '后续跟进计划内容' },
                { key: 'remark', label: '备注' },
                { key: 'created_at', label: '创建日期', format: (value) => value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '' },
            ],
        });
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="C&A会谈结果报告"
                desc="查询C&A会谈结果报告"
            />

            <DataTable
                ref={tableRef}
                columns={tableColumns as any}
                data={data}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={confirmExport}
                enableMultiSelect
                selectionActions={
                    <CnaButton
                        size="xs"
                        onClick={handleExportPDF}
                    >
                        导出PDF
                    </CnaButton>
                }
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                    {
                        field: "dateRange",
                        label: "到访日期",
                        type: "dateRange",
                    },
                ]}
            />

            <Report />
        </Stack>
    );
};

export default AdminVisitorReportsPage;
