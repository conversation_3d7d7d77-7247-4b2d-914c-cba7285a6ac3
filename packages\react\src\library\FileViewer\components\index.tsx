import { useAsyncEffect, useMount, useRequest, useSetState } from "ahooks";
import React, { useEffect, useState } from "react";
import { filesize } from "filesize";
import { getFileByUrl } from "../utils";
import { Box, Group, Image, LoadingOverlay, Progress, Stack, Text } from "@mantine/core";
import PDFViewer from "./viewers/PDFViewer";
import <PERSON><PERSON><PERSON>Vie<PERSON> from "./viewers/JsonViewer";
import DocxViewer from "./viewers/DocxViewer";
import FileLoadingProgress from "./common/FileLoadingProgress";
import PDFViewerV2 from "./viewers/PDFViewerV2";
import PDFViewerV3 from "./viewers/PDFViewerV3";
import ImageViewer from "./viewers/ImageViewer";

type FileViewerProps = {
    file: File | string;
};

const FileViewer: React.FC<FileViewerProps> = ({ file }) => {
    const [viewFile, setViewFile] = useState<File>();

    const [inited, setInited] = useState<boolean>(false);

    const [downloadState, setDownloadState] = useSetState({
        rate: 0,
        progress: 0,
        total: 0,
        loaded: 0,
    });

    const { loading: fetching, run: getFile } = useRequest(
        async (url: string) => {
            let __file = await getFileByUrl(url, (progress) => {
                setDownloadState({
                    rate: progress.rate ?? 0,
                    progress: progress.progress ?? 0,
                    total: progress.total ?? 0,
                    loaded: progress.loaded ?? 0,
                });
            });

            if (__file) {
                setViewFile(__file);
            }
        },
        {
            manual: true,
            ready: inited,
            cacheKey: `file-view`,
        }
    );

    const RenderFileTypeViewer = () => {
        switch (viewFile?.type) {
            case "application/pdf": {
                return <PDFViewerV3 file={viewFile} />;
            }
            case "application/json": {
                return <JSONViewer file={viewFile} />;
            }
            case "image/png":
            case "image/jpeg": {
                // return (
                //     <Stack
                //         justify="center"
                //         className="tw-h-full tw-w-full"
                //     >
                //         <Image
                //             fit="contain"
                //             src={URL.createObjectURL(viewFile)}
                //         />
                //     </Stack>
                // );
                return <ImageViewer file={viewFile} />;
            }
            case "application/vnd.openxmlformats-officedocument.wordprocessingml.document": {
                return <DocxViewer file={viewFile} />;
            }

            default: {
                return (
                    <Stack
                        justify="center"
                        className="tw-h-full tw-w-full"
                    >
                        <Text>暂不支持{viewFile?.type}文件类型浏览</Text>
                    </Stack>
                );
            }
        }
    };

    useEffect(() => {
        if (file instanceof File === true) {
            setViewFile(file);
        } else {
            getFile(file);
        }
    }, [file, inited]);

    useMount(() => {
        setInited(true);
    });

    return (
        <Box className="tw-h-full tw-relative">
            <FileLoadingProgress
                visible={fetching}
                loaded={downloadState.loaded}
                total={downloadState.total}
                rate={downloadState.rate}
            />
            {viewFile && <RenderFileTypeViewer />}
        </Box>
    );
};

export default FileViewer;
