import api from "@/apis";
import { Group, Stack } from "@mantine/core";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import dayjs from "dayjs";
import CnaAdminButton from "@/components/common/CnaAdminButton";
import { useMemoizedFn } from "ahooks";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import Remark from "@/components/modals/Remark";
import { UploadModal } from "@/components/common/UploadModal";

const columnHelper = createColumnHelper<TDigitalHuman>();

const STATUS_MAP = {
    1: "未审核",
    2: "审核通过",
    3: "审核失败",
};

const AdminDigitalHumanPage = () => {
    const lang = useSettingStore.use.lang();

    const openModal = useModalStore.use.open();
    const openUpload = useModalStore.use.openUpload();
    const openConfirm = useModalStore.use.openConfirm();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    // 审核不通过的 id
    const [rejectFilesId, setRejectFilesId] = useState<number | null>(null);

    // 处理下载打包的 zip 文件
    const handleDownloadZip = useMemoizedFn(async (row) => {
        setLoading(true);
        // 获取服务端的 zip 文件路径
        const { result } = await cnaRequest("/api/v1/admin/digital-humans/zip", "GET", {
            id: row.id,
        });
        const fileBasename = result.data.split("/").pop();

        // 下载 zip 文件
        try {
            const response = await cnaRequest(
                "/api/v1/admin/digital-humans/download-file",
                "POST",
                { file_path: fileBasename, type: "local" },
                // 指定响应类型为 Blob
                { responseType: "blob" }
            );

            // 获取文件内容（Blob）
            const blob = new Blob([response.result?.data]);

            // 创建一个临时链接
            const url = window.URL.createObjectURL(blob);

            // 创建一个 <a> 标签并触发下载
            const a = document.createElement("a");
            a.href = url;
            // 使用文件路径的最后一部分作为文件名
            a.download = fileBasename || "download";
            document.body.appendChild(a);
            a.click();

            // 清理临时链接
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error("下载失败:", error);
            noty.error("下载失败，请稍后重试");
        } finally {
            setLoading(false);
        }
    });

    const confirmDownload = useMemoizedFn((row) => {
        openConfirm({
            title: "确定下载此文件吗？",
            onConfirm: () => {
                handleDownloadZip(row);
            },
        });
    });

    // 审核上传文件的不通过 和 上传成品
    const rowActions = (row) => [
        {
            key: "review",
            label: "审核不通过",
            onClick: () => {
                setRejectFilesId(row.id);
                openModal("remarkModal", { objID: row.id, type: 1 });
            },
        },
        {
            key: "upload",
            label: "上传成品",
            onClick: () => {
                openUpload({
                    title: "上传数字人成品",
                    accept: ["video/mp4"],
                    required: true,
                    maxSize: 5 * 1024 * 1024,
                    onSuccess: async (file) => {
                        // 上传成品
                        try {
                            setLoading(true);

                            const formData = new FormData();
                            formData.append("id", row.id);
                            formData.append("status", "2");
                            formData.append("finished_files", file);

                            const { error } = await cnaRequest(
                                "/api/v1/admin/digital-humans/review",
                                "POST",
                                formData
                            );

                            if (!error) {
                                noty.success("上传成功");
                            }
                        } catch (error) {
                            console.log(error);
                        } finally {
                            setLoading(false);
                        }
                    },
                    onError: (error) => {
                        console.log(error);
                    },
                });
            },
        },
    ];

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profile.profileName", {
            header: "合伙人姓名",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profile.profilePartnerCode", {
            header: "合伙人编码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("status", {
            header: "状态",
            enableSorting: false,
            cell: (info) => STATUS_MAP[info.getValue()],
        }),
        columnHelper.accessor("created_at", {
            header: "创建时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("updated_at", {
            header: "更新时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                return (
                    <Group>
                        <CnaAdminButton
                            size="xs"
                            variant="outline"
                            onClick={() => confirmDownload(info.row.original)}
                        >
                            下载文件
                        </CnaAdminButton>
                        <TableRowDropActionMenu items={rowActions(info.row.original)} />
                    </Group>
                );
            },
        }),
    ];

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                status: globalFilters?.status || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.digitalHuman.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    // 审核不通过
    const rejectFiles = useMemoizedFn(async () => {
        setLoading(true);
        try {
            const { error } = await cnaRequest("/api/v1/admin/digital-humans/review", "POST", {
                id: rejectFilesId,
                status: 3,
            });

            if (!error) {
                noty.success("审核成功");
                setRejectFilesId(null);
            }
        } catch (error) {
            noty.error("审核失败");
        } finally {
            setLoading(false);
        }
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="数字人"
                desc="查询数字人"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                    {
                        field: "status",
                        label: "状态",
                        type: "select",
                        options: Object.entries(STATUS_MAP).map(([key, value]) => ({
                            label: value,
                            value: key,
                        })),
                    },
                ]}
            />

            <Remark onSaveSuccess={rejectFiles} />
            <UploadModal />
        </Stack>
    );
};

export default AdminDigitalHumanPage;
