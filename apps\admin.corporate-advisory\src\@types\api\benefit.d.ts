declare global {
    type TBenefitSearchParams = {
        keyword?: string | null;
    } & TPageQueryParams;

    type TBenefit = {
        benefitID: number;
        benefitTitleEN: string;
        benefitTitleZH: string;
        benefitTitleZT: string;
        benefitTitleMS: string;
        benefitDescriptionEN: string;
        benefitDescriptionZH: string;
        benefitDescriptionZT: string;
        benefitDescriptionMS: string;
        createUser: number;
        createTime: string;
        editUser: number;
        editTime: string;
        create_user_info?: {
            profileID: number;
            profileName: string;
        } | null;
        edit_user_info?: {
            profileID: number;
            profileName: string;
        } | null;
    }

    type TBenefitsResponse = {
        items: TBenefit[];
        paginate: BasePaginateResponse;
    };

    type TBenefitApplication = {
        benefitXID: number;
        benefitID: number;
        userID: number;
        adminID: number;
        benefitStatus: string;
        benefitRemark: string;
        createUser: number;
        createTime: string;
        editUser: number;
        editTime: string;
    }

    type TBenefitApplicationSearchParams = {
        profileID?: number;
        keyword?: string | null;
    } & TPageQueryParams;

    type TBenefitApplicationReviewParams = {
        id: number;
        status: string;
        benefitRemark: string;
    }

    type TBenefitApplicationsResponse = {
        items: TBenefit[];
        paginate: BasePaginateResponse;
    };
}

export {};
