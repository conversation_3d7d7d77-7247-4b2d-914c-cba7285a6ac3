import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import {
    Divider,
    Modal,
    Select,
    SimpleGrid,
    Stack,
    Text,
    Textarea,
    Group,
    ActionIcon,
    Tooltip,
} from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Checks, Download, Eye, X } from "@phosphor-icons/react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { t } from "@code.8cent/i18n";
import api from "@/apis";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { Dropzone } from "@mantine/dropzone";
import { downloadFile } from "@/utils/files";
import { useState } from "react";

const createSchema = (type?: string) =>
    z
        .object({
            status:
                type === "reCheck" ? z.string().optional() : z.string().min(1, "请选择审核结果"),
            form_reject_reason: z.string().optional(),
            report_reject_reason: z.string().optional(),
            contract_reject_reason: z.string().optional(),
            report_ca_review: z.string().optional(),
            file: z.instanceof(File).optional(),
            file_cn: z.instanceof(File).optional(),
            file_en: z.instanceof(File).optional(),
            form_reject_type: z.string().optional(),
        })
        .partial()
        .refine(
            (data) =>
                data.status !== "3" ||
                (!!data.form_reject_reason && data.form_reject_reason.trim() !== ""),
            {
                message: "驳回时请输入审核意见",
                path: ["form_reject_reason"],
            }
        )
        .refine(
            (data) =>
                data.status !== "3" ||
                (!!data.form_reject_type && data.form_reject_type.trim() !== ""),
            {
                message: "驳回时请选择驳回类型",
                path: ["form_reject_type"],
            }
        )
        .refine(
            (data) =>
                data.status !== "6" ||
                (!!data.contract_reject_reason && data.contract_reject_reason.trim() !== ""),
            {
                message: "驳回时请输入审核意见",
                path: ["contract_reject_reason"],
            }
        )
        .refine(
            (data) =>
                data.status !== "11" ||
                (!!data.report_reject_reason && data.report_reject_reason.trim() !== ""),
            {
                message: "驳回时请输入审核意见",
                path: ["report_reject_reason"],
            }
        )
        .refine((data) => data.status !== "10" || !!data.file, {
            message: "请上传企业信用文件",
            path: ["file"],
        })
        .refine((data) => data.status !== "13" || !!data.file_cn, {
            message: "请上传修改后合成文件(中文版)",
            path: ["file_cn"],
        })
        .refine((data) => data.status !== "13" || !!data.file_en, {
            message: "请上传修改后合成文件(英文版)",
            path: ["file_en"],
        });

const GspReview = ({ onSubmitSuccess }: { onSubmitSuccess: () => void }) => {
    const { lang } = useSettingStore();
    const { openFileView } = useFileViewer();
    const openConfirm = useModalStore.use.openConfirm();
    const [loading, setLoading] = useState(false);

    const gspReviewParams = useModalStore((state) => state.modalParams.gspReviewModal);
    const gsp = gspReviewParams?.gspApplication;
    const type = gspReviewParams?.type; // form or report or contract

    const schema = createSchema(type);
    type FormValues = z.infer<typeof schema>;

    // 文件操作配置
    const fileOperations = [
        { label: "尽调报告中文版", type: "cn" },
        { label: "尽调报告英文版", type: "en" },
        { label: "尽调报告(工作人员查看)中文版", type: "admin" },
        { label: "尽调报告(工作人员查看)英文版", type: "en_admin" },
        { label: "尽调报告(最后修改)中文版", type: "ca" },
        { label: "尽调报告(最后修改)英文版", type: "en_ca" },
    ];

    // 文件操作组件
    const FileOperationItem = ({ label, type }: { label: string; type: string }) => (
        <Group justify="space-between">
            <Text>{label}:</Text>
            <Group>
                <Tooltip label="查看">
                    <ActionIcon onClick={() => previewFile(gsp.report_url, type)}>
                        <Eye />
                    </ActionIcon>
                </Tooltip>
                <Tooltip label="下载">
                    <ActionIcon
                        onClick={() => confirmDownload(gsp.report_url, type, "local")}
                        variant="outline"
                    >
                        <Download />
                    </ActionIcon>
                </Tooltip>
            </Group>
        </Group>
    );

    // 文件上传组件
    const FileUploadItem = ({
        label,
        fieldName,
        error,
    }: {
        label: string;
        fieldName: "file" | "file_cn" | "file_en";
        error?: string;
    }) => (
        <Stack gap={2}>
            <Text size="sm">{label}</Text>
            <Dropzone
                onDrop={(files) => {
                    setValue(fieldName, files[0]);
                }}
                onReject={(files) => {
                    setValue(fieldName, undefined);
                }}
                maxSize={30 * 1024 * 1024}
                accept={["application/pdf", "application/docx", "application/doc"]}
                multiple={false}
                maxFiles={1}
                {...register(fieldName)}
            >
                {watch(fieldName) ? (
                    <Text
                        c="dimmed"
                        size="sm"
                        className="tw-text-center tw-m-4"
                    >
                        {watch(fieldName).name}
                    </Text>
                ) : (
                    <Text
                        c="dimmed"
                        size="sm"
                        className="tw-text-center tw-m-4"
                    >
                        拖拽文件到此处或点击上传
                    </Text>
                )}
            </Dropzone>
            {error && (
                <Text
                    c="red"
                    size="sm"
                >
                    {error}
                </Text>
            )}
        </Stack>
    );

    const statusOptions =
        type === "form"
            ? [
                  { label: "通过预审", value: "2" },
                  { label: "驳回预审", value: "3" },
              ]
            : type === "contract"
            ? [
                  { label: "通过合同", value: "5" },
                  { label: "驳回合同", value: "6" },
              ]
            : type === "report"
            ? [
                  { label: "通过尽调资料", value: "10" },
                  { label: "驳回尽调资料", value: "11" },
              ]
            : [
                  { label: "我已查看报告审核无误", value: "12" },
                  { label: "审核报告发现有误", value: "13" },
              ];

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.gspReviewModal,
            close: state.close,
        }))
    );

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
        watch,
    } = useForm<FormValues>({
        defaultValues: {
            status: gsp?.status === 14 ? "12" : "",
            form_reject_reason: "",
            report_reject_reason: "",
            contract_reject_reason: "",
            report_ca_review: "",
            form_reject_type: "",
        },
        resolver: zodResolver(schema),
    });

    const closeModal = useMemoizedFn(() => {
        reset();
        close("gspReviewModal");
    });

    const onSubmit: SubmitHandler<FormValues> = async (data) => {
        setLoading(true);
        try {
            // 如果 type = report, 并且 status = 10, 用 formData 上传文件
            if (type === "report" && data.status === "10") {
                const formData = new FormData();
                formData.append("id", gsp?.id);
                formData.append("status", data.status);
                formData.append("file", data.file);
                formData.append("report_reject_reason", data.report_reject_reason || "");

                const res = await api.gsp.reviewApplication(formData, type);
                if (res) {
                    closeModal();
                    onSubmitSuccess();
                }
                return;
            }

            if (type === "reCheck") {
                const formData = new FormData();
                formData.append("id", gsp?.id);
                // formData.append("status", data.status);
                if (data.file_cn) {
                    formData.append("file_cn", data.file_cn);
                }
                if (data.file_en) {
                    formData.append("file_en", data.file_en);
                }

                const res = await api.gsp.reviewApplication(formData, type);
                if (res) {
                    closeModal();
                    onSubmitSuccess();
                }
                return;
            }

            const reviewParams = {
                id: gsp?.id,
                status: Number(data.status),
                form_reject_reason: data.form_reject_reason,
                report_reject_reason: data.report_reject_reason,
                contract_reject_reason: data.contract_reject_reason,
                report_ca_review: data.report_ca_review,
                form_reject_type: data.form_reject_type,
            };

            const res = await api.gsp.reviewApplication(reviewParams, type);

            if (res) {
                closeModal();
                onSubmitSuccess();
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const modalFooterButtons = [
        {
            key: "submit",
            label: "提交",
            leftSection: <Checks />,
            onClick: handleSubmit(onSubmit, (errors) => console.log(errors)),
            loading,
        },
        {
            key: "close",
            label: "关闭",
            leftSection: <X />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    const previewFile = useMemoizedFn((filePath, type) => {
        let formatFilePath = filePath;
        // 去除 filePath 的扩展后缀，然后拼上 _admin.pdf or _en_admin.pdf
        if (type !== "cn") {
            formatFilePath = formatFilePath.replace(".pdf", `_${type}.pdf`);
        }

        openFileView(`/api/v1/admin/file/resource?path=${formatFilePath}&type=local`, {
            title: `查看${type === "cn" ? "报告" : "英文报告"}`,
        });
    });

    const handleDownload = useMemoizedFn(
        (path: string, file_type: string, type: "local" | "remote") => {
            let formatFileName = path;
            if (file_type !== "cn") {
                formatFileName = formatFileName.replace(".pdf", `_${file_type}.pdf`);
            }
            downloadFile(formatFileName, type);
        }
    );

    const confirmDownload = useMemoizedFn(
        (path: string, file_type: string, type: "local" | "remote") => {
            openConfirm({
                title: "确定下载此文件吗？",
                onConfirm: () => {
                    handleDownload(path, file_type, type);
                },
            });
        }
    );

    return (
        <Modal
            title="绿智地球申请审核"
            opened={show}
            onClose={closeModal}
            size="xl"
        >
            <Stack>
                {type && type === "reCheck" && (
                    <>
                        {gsp.report_url != "" && (
                            <SimpleGrid cols={2}>
                                {fileOperations.map((operation) => (
                                    <FileOperationItem
                                        key={operation.type}
                                        label={operation.label}
                                        type={operation.type}
                                    />
                                ))}
                            </SimpleGrid>
                        )}
                        <Select
                            label="合伙人审核状态"
                            data={[
                                { label: "未审核", value: "0" },
                                { label: "已审核（正确）", value: "1" },
                                { label: "已审核（有误）", value: "2" },
                            ]}
                            defaultValue={String(gsp.is_report_partner_post)}
                            readOnly
                        />
                        <Textarea
                            label="合伙人审核备注"
                            placeholder="合伙人审核备注"
                            defaultValue={gsp.report_partner_suggest}
                            autosize
                            minRows={4}
                            readOnly
                        />
                        <Divider size="lg" />
                    </>
                )}

                {type && type !== "reCheck" && (
                    <Select
                        label="审核状态"
                        data={statusOptions}
                        error={errors.status?.message}
                        {...register("status")}
                        onChange={(value) => setValue("status", value || "")}
                        readOnly={gsp?.status === 14}
                    />
                )}

                {type && type === "form" && watch("status") === "3" && (
                    <Select
                        label="驳回类型"
                        data={[
                            { label: "驳回合伙人修改", value: "1" },
                            { label: "不符合入驻要求", value: "2" },
                        ]}
                        {...register("form_reject_type")}
                        onChange={(value) => setValue("form_reject_type", value || "")}
                        error={errors.form_reject_type?.message}
                    />
                )}

                {/* 复审报告不需要填写审核意见 */}
                {type && type !== "reCheck" && (
                    <Textarea
                        label="审核意见"
                        placeholder="请输入审核意见"
                        autosize
                        minRows={4}
                        error={
                            type === "form"
                                ? errors.form_reject_reason?.message
                                : type === "contract"
                                ? errors.contract_reject_reason?.message
                                : type === "report"
                                ? errors.report_reject_reason?.message
                                : errors.report_ca_review?.message
                        }
                        {...register(
                            type === "form"
                                ? "form_reject_reason"
                                : type === "contract"
                                ? "contract_reject_reason"
                                : type === "report"
                                ? "report_reject_reason"
                                : "report_ca_review"
                        )}
                    />
                )}

                {type && type === "report" && (
                    <FileUploadItem
                        label="上传企业信用文件"
                        fieldName="file"
                        error={errors.file?.message}
                    />
                )}

                {/* 复审报告, 上传修改后合成文件 */}
                {type && type === "reCheck" && (
                    <Stack gap={4}>
                        <FileUploadItem
                            label="修改后合成文件(中文版)"
                            fieldName="file_cn"
                            error={errors.file_cn?.message}
                        />
                        <FileUploadItem
                            label="修改后合成文件(英文版)"
                            fieldName="file_en"
                            error={errors.file_en?.message}
                        />
                    </Stack>
                )}
            </Stack>

            <div className="tw-mt-10">
                <ModalFooter buttons={modalFooterButtons} />
            </div>
        </Modal>
    );
};

export default GspReview;
