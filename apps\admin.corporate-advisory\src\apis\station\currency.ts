import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

// 货币管理
const currency = {
    list: async (params: TCurrencySearchParams) => {
        const { error, result } = await cnaRequest<TCurrencyResponse>(
            `/api/v1/admin/currency`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    detail: async (id: string) => {
        const { error, result } = await cnaRequest<TCurrency>(
            `/api/v1/admin/currency/${id}`,
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (params: TCurrencyStoreParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/currency/save`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (id: string, params: TCurrencyUpdateParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/currency/save/${id}`,
            "PUT",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default currency;
