import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import { Modal, Select, Stack, Textarea } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Checks, X } from "@phosphor-icons/react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { t } from "@code.8cent/i18n";
import refund from "@/apis/station/refund";
import noty from "@code.8cent/react/noty";

const schema = z.object({
    status: z.number().min(2, "请选择审核结果").max(3, "请选择审核结果"),
    review_comments: z.string().min(1, "请输入审核备注"),
});

type FormValues = z.infer<typeof schema>;

const RefundExamine = ({ onSubmitSuccess }: { onSubmitSuccess: () => void }) => {
    const { lang } = useSettingStore();

    const refundExamineParams = useModalStore((state) => state.modalParams.refundExamineModal);
    const refundData = refundExamineParams?.refundData;

    const statusOptions = [
        { label: "审核通过", value: "2" },
        { label: "审核不通过", value: "3" },
    ];

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.refundExamineModal,
            close: state.close,
        }))
    );

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
    } = useForm<FormValues>({
        defaultValues: {
            status: 2,
            review_comments: "",
        },
        resolver: zodResolver(schema),
    });

    const closeModal = useMemoizedFn(() => {
        reset();
        close("refundExamineModal");
    });

    const onSubmit: SubmitHandler<FormValues> = async (data) => {
        try {
            const examineParams: TRefundExamineParams = {
                id: refundData?.id,
                status: data.status as 2 | 3,
                review_comments: data.review_comments,
            };

            const res = await refund.examine(examineParams);

            if (res) {
                noty.success("审核成功");
                closeModal();
                onSubmitSuccess();
            }
        } catch (error) {
            console.error("审核失败:", error);
        }
    };

    const modalFooterButtons = [
        {
            key: "submit",
            label: "提交",
            leftSection: <Checks />,
            onClick: handleSubmit(onSubmit),
        },
        {
            key: "close",
            label: "关闭",
            leftSection: <X />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            title="退款审核"
            size="md"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-mt-4">
                <div className="tw-mb-4">
                    <div className="tw-text-sm tw-text-gray-600 tw-mb-2">退款信息</div>
                    <div className="tw-bg-gray-50 tw-p-3 tw-rounded">
                        <div className="tw-text-sm">
                            <span className="tw-text-gray-500">合伙人：</span>
                            <span>{refundData?.profile?.profileName}</span>
                        </div>
                        <div className="tw-text-sm tw-mt-1">
                            <span className="tw-text-gray-500">退款金额：</span>
                            <span className="tw-text-red-500">¥{refundData?.refund_price}</span>
                        </div>
                        <div className="tw-text-sm tw-mt-1">
                            <span className="tw-text-gray-500">申请备注：</span>
                            <span>{refundData?.remark || "无"}</span>
                        </div>
                    </div>
                </div>

                <Select
                    label="审核结果"
                    placeholder="请选择审核结果"
                    data={statusOptions}
                    error={errors.status?.message}
                    {...register("status", { valueAsNumber: true })}
                    onChange={(value) => setValue("status", Number(value))}
                />

                <Textarea
                    label="审核备注"
                    placeholder="请输入审核备注"
                    minRows={3}
                    error={errors.review_comments?.message}
                    {...register("review_comments")}
                />
            </Stack>

            <ModalFooter buttons={modalFooterButtons} />
        </Modal>
    );
};

export default RefundExamine;
