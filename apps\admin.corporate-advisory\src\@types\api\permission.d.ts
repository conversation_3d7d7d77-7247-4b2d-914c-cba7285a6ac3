declare global {
    type TAction = {
        powerActionID: number;
        powerID: number;
        structureCode: string;
        powerActionCode: string;
        check?: boolean;
        created_at?: string;
        updated_at?: string;
        structureEN?: string;
        structureMS?: string;
        structureZH?: string;
        structureZT?: string;
    };

    type TPermission = {
        powerID: number;
        structureCode: string;
        powerParentID: number;
        powerOrder: number;
        powerRoute: string;
        isDisplay: number;
        created_at?: string;
        updated_at?: string;
        structureEN?: string;
        structureMS?: string;
        structureZH?: string;
        structureZT?: string;
        power_actions: TAction[];
    };

    type TStorePermissionParams = {
        user_id: number;
        data: string;
    };

    type TPermissionsResponse = {
        items: TPermission[];
        paginate: BasePaginateResponse;
    };
}

export {};
