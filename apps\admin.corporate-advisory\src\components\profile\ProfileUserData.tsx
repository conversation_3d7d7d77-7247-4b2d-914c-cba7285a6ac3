import { t } from "@code.8cent/i18n";
import useProfileStore from "@/store/profile";
import useSettingStore from "@code.8cent/store/setting";
import { Grid, Input, Select, TextInput } from "@mantine/core";
import { useMemoizedFn, useMount } from "ahooks";
import React, { useState } from "react";

type ProfileUserDataProps = {
    dataKey: "userProfessional" | "userSkill";
    readOnly?: boolean;
    data: {};
    selectOptions: [];
};

type TDataKeyMap = {
    [key in ProfileUserDataProps["dataKey"]]: {
        label: string;
        IDKey: `${string}ID`;
        descriptionKey: `${string}Description`;
        key: string;
        titleKey: string;
    };
};

const dataKeyMap = {
    userProfessional: {
        label: "introduction.label.professional",
        key: "professionals",
        IDKey: "professionalID",
        descriptionKey: "professionalDescription",
        titleKey: "professionalTitleZH",
        additionalKey: "introduction.label.select",
    },
    userSkill: {
        label: "introduction.label.skill",
        key: "skills",
        IDKey: "skillID",
        descriptionKey: "skillDescription",
        titleKey: "skillTitleZH",
        additionalKey: "introduction.label.select",
    },
} as const;

const ProfileUserData: React.FC<ProfileUserDataProps> = ({
    dataKey,
    readOnly,
    data,
    selectOptions,
}) => {
    const lang = useSettingStore.use.lang();

    return (
        <Input.Wrapper
            label={t(dataKeyMap[dataKey].label, lang)}
            labelProps={{ className: "profile-form-label" }}
        >
            {Array.from({ length: 3 }).map((_, index) => (
                <Grid gutter={3} key={`${dataKey}_${index}`}>
                    <Grid.Col span={{ base: 12, md: 6 }} className="tw-mb-3">
                        <Select
                            placeholder={`${t(
                                dataKeyMap[dataKey].additionalKey,
                                lang
                            )} ${t(dataKeyMap[dataKey].label, lang)}`}
                            searchable
                            allowDeselect={false}
                            value={String(
                                data[index]?.[dataKeyMap[dataKey].IDKey] ?? ""
                            )}
                            data={selectOptions.map((data, i) => ({
                                value: String(data[dataKeyMap[dataKey].IDKey]),
                                label: data[dataKeyMap[dataKey].titleKey],
                            }))}
                            onChange={(e) => {}}
                            readOnly={readOnly}
                        />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, md: 6 }} className="tw-mb-3">
                        <TextInput
                            value={
                                data[index]?.["pivot"][
                                    dataKeyMap[dataKey].descriptionKey
                                ] ?? ""
                            }
                            onChange={(e) => {}}
                            readOnly={readOnly}
                        />
                    </Grid.Col>
                </Grid>
            ))}
        </Input.Wrapper>
    );
};

export default ProfileUserData;
