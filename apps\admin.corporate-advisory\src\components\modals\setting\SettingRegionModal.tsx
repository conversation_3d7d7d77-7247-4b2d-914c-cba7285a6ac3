import api from "@/apis";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import useModalStore from "@/store/modal";
import useProfileStore from "@/store/profile";
import useSettingStore from "@code.8cent/store/setting";
import { Group, Modal, Select, Stack, Button } from "@mantine/core";
import { Check, X } from "@phosphor-icons/react";
import { useMemoizedFn, useRequest } from "ahooks";
import dayjs from "dayjs";
import { useState } from "react";
import { useShallow } from "zustand/react/shallow";
const SettingRegionModal = () => {
    const { timeFormats, timezones, languages, dateFormats, currencies } =
        useDataStore();
    const { setLang, lang } = useSettingStore();
    const {
        settingCurrency,
        settingTimeFormat,
        settingTimezone,
        settingDateFormat,
        settingLanguage,
        setProfileValue,
    } = useProfileStore();
    const {
        show,
        close,
        openAlert: alert,
    } = useModalStore(
        useShallow((state) => ({
            show: state.languageRegion,
            close: state.close,
            openAlert: state.openAlert,
        }))
    );
    const { run: updateUserSetting, loading: saving } = useRequest(
        async () => {
            let update_res = await api.user.updateLanguageRegionSetting({
                settingCurrency,
                settingTimeFormat,
                settingTimezone,
                settingDateFormat,
                settingLanguage,
            });
            return update_res;
        },
        {
            manual: true,
            onFinally(params, updateResult) {
                if (updateResult === true) {
                    alert(t("notification.user.settings", lang), t("notification.settings.saved.success", lang), "success");
                    setLang(settingLanguage as LangCode);
                    // close("languageRegion");
                } else {
                    alert(t("notification.user.settings", lang), t("notification.settings.saved.fail", lang), "danger");
                }
            },
        }
    );

    return (
        <Modal
            opened={show}
            onClose={() => close("languageRegion")}
            title={t("setting.lang_area", lang)}
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-6">
                <Select
                    data={languages.map((lang) => ({
                        value: String(lang.languageCode),
                        label: String(lang.languageType),
                    }))}
                    value={settingLanguage}
                    label={t("setting.lang_area.label.system_language", lang)}
                    onChange={(e) => {
                        setProfileValue("settingLanguage", e);
                    }}
                />
                <Select
                    searchable
                    data={timezones.map((timezone) => ({
                        value: String(timezone.timeZone),
                        label: `${timezone.timeZone.replace("_", " ")} ${
                            timezone.timeZoneGMT
                        }`,
                    }))}
                    value={settingTimezone}
                    label={t("setting.lang_area.label.timezone", lang)}
                    onChange={(e) => {
                        setProfileValue("settingTimezone", e);
                    }}
                />
                <Select
                    data={dateFormats.map((format) => ({
                        value: String(format.dateFormat),
                        label: `${format.dateFormat} ${dayjs().format(
                            format.dateFormat
                        )}`,
                    }))}
                    value={settingDateFormat}
                    label={t("setting.lang_area.label.date_format", lang)}
                    onChange={(e) => {
                        setProfileValue("settingDateFormat", e);
                    }}
                />
                <Select
                    data={timeFormats.map((format) => ({
                        value: String(format.timeFormat),
                        label: `${format.timeFormat} ${dayjs().format(
                            format.timeFormat
                        )}`,
                    }))}
                    value={settingTimeFormat}
                    label={t("setting.lang_area.label.time_format", lang)}
                    onChange={(e) => {
                        setProfileValue("settingTimeFormat", e);
                    }}
                />
                <Select
                    searchable
                    data={currencies.map((currency) => ({
                        value: String(currency.currencyCode),
                        label: `${currency.currencyName} - ${currency.currencyCode}`,
                    }))}
                    value={settingCurrency}
                    label={t("setting.lang_area.label.currency", lang)}
                    onChange={(e) => {
                        setProfileValue("settingCurrency", e);
                    }}
                />
            </Stack>
            <Group justify="end" className="tw-border-t tw-pt-4 tw-mt-4">
                <CnaButton
                    onClick={saving === true ? null : updateUserSetting}
                    loading={saving}
                    color="cna"
                    leftSection={<Check weight="bold" />}
                >
                    {t("common.save", lang)}
                </CnaButton>
                <CnaButton
                    color="cna"
                    variant="outline"
                    onClick={() => close("languageRegion")}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default SettingRegionModal;
