import useDataStore from "@code.8cent/store/data";

import { find as _find } from 'es-toolkit/compat';

export function t(code: string, lang: string = "ZH"):string {
    const structures = useDataStore.getState().languageStructures;

    let structure = _find(structures, {structureCode: code});

    if(structure){
        return structure[`structure${lang}`] || structure[`structureZH`];
    }else{
        return code;
    }
}
