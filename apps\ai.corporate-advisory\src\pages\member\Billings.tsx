import api from "@/apis";
import { Badge, Stack, Title, Table, Group, TextInput } from "@mantine/core";
import {
    useDebounce,
    useGetState,
    useMemoizedFn,
    useMount,
    useRequest,
    useUnmount,
    useUpdateEffect,
    useVirtualList,
} from "ahooks";
import { useRef, useState } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import ScrollArea from "@code.8cent/react/components/ScrollArea";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { DownloadSimple, Eye, MagnifyingGlass } from "@phosphor-icons/react";
import { PageHeader } from "@code.8cent/react/components";

const MemberBillingsPage = () => {
    const lang = useSettingStore.use.lang();

    const containerRef = useRef<HTMLDivElement>(null);

    const wrapperRef = useRef(null);

    const [bills, setBills] = useState<BillItem[]>([]);

    const [inited, setInited] = useState(false);

    const [hasMore, setHasMore] = useState<boolean>(false);

    const [page, setPage, getPage] = useGetState<number>(1);

    const [keyword, setKeyword] = useState<string>("");

    const keywordValue = useDebounce(keyword, { wait: 500 });

    const { run: getBillList, loading } = useRequest(
        async (page_param: number = 1, keyword_param: string = "") => {
            try {
                if (page !== 1) {
                    return;
                }

                const pageSize = 5;

                const bills = await api.user.getBillList({
                    keyword: keyword_param,
                    page: page_param,
                    pageSize,
                });

                if (bills.length < pageSize) {
                    setHasMore(false);
                } else {
                    setHasMore(true);
                }

                console.log("fetch page: ", page_param);

                if (page_param === 1) {
                    setBills(bills);
                } else {
                    setBills((prev) => [...prev, ...bills]);
                }
            } catch (error) {
                console.error("Failed to fetch bills:", error);
            }
        },
        {
            ready: inited,
        }
    );

    const getMoreBills = useMemoizedFn(async () => {
        if (hasMore) {
            const new_page = page + 1;

            getBillList(new_page);

            setPage(new_page);
        }
    });

    const handleFile = useMemoizedFn(
        async (fileId: number, type: "view" | "download") => {
            // let file_url = await api.user.getDocumentRealLink(fileId);
            // if (!file_url) {
            //     return;
            // }
            // let file_full_url = `${window.api_base_url}/${file_url}`;
            // if (type === "view") {
            //     let file_view_url = file_full_url.replace(
            //         "/bill/",
            //         "/bill/view/"
            //     );
            //     open(file_view_url, "_blank");
            // } else if (type === "download") {
            //     let a = document.createElement("a");
            //     a.setAttribute("download", "");
            //     a.setAttribute("href", file_full_url);
            //     a.setAttribute("target", "_blank");
            //     a.style.display = "none";
            //     document.body.appendChild(a);
            //     a.click();
            //     document.body.removeChild(a);
            // }
        }
    );

    useMount(() => {
        setInited(true);
    });

    useUnmount(() => {
        console.log("unmounted");
        setInited(false);
    });

    useUpdateEffect(() => {
        setPage(1);
        getBillList(1, keywordValue);
    }, [keywordValue]);

    // UseVirtualList hook
    const [virtualBillList] = useVirtualList(bills, {
        containerTarget: containerRef,
        wrapperTarget: wrapperRef,
        itemHeight: 63 + 8, // Define your row height here
        overscan: 5, // Number of extra items rendered above and below the visible range
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title={t("billing.title", lang)}
                desc={
                    t("navigation.billing.system.quote", lang)
                }
            />

            <TextInput
                value={keyword}
                onChange={(e) => setKeyword(e.target.value)}
                className="tw-mb-5"
                leftSection={<MagnifyingGlass weight="bold" size={20} />}
            />

            <ScrollArea
                onScrollBottom={getMoreBills}
                className="tw-border tw-flex-1"
                ref={containerRef}
                viewportRef={wrapperRef}
            >
                <Table verticalSpacing="md">
                    <Table.Thead className="tw-sticky tw-bg-white tw-shadow-sm">
                        <Table.Tr className="tw-w-full">
                            <Table.Th>#</Table.Th>
                            <Table.Th>
                                {t("billing.table.th.company", lang)}
                            </Table.Th>
                            <Table.Th>
                                {t("billing.table.th.detail", lang)}
                            </Table.Th>
                            <Table.Th>
                                {t("billing.table.th.amount", lang)}
                            </Table.Th>
                            <Table.Th>
                                {t("billing.table.th.status", lang)}
                            </Table.Th>
                            {/* <Table.Th>
                                {t("billing.table.th.action", lang)}
                            </Table.Th> */}
                        </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody className="tw-flex-1">
                        {!virtualBillList.length && (
                            <Table.Tr>
                                <Table.Td colSpan={6}>
                                    <Group
                                        justify="center"
                                        className="tw-mt-20"
                                    >
                                        {t("billings.empty", lang)}
                                    </Group>
                                </Table.Td>
                            </Table.Tr>
                        )}

                        {virtualBillList.map(
                            ({ data: bill, index: b_index }) => {
                                return (
                                    <Table.Tr
                                        key={b_index}
                                        style={{ height: 63 }}
                                        className="last:!tw-border-inherit last:!tw-border-solid"
                                    >
                                        <Table.Td>{bill.billID}</Table.Td>
                                        <Table.Td>
                                            {bill.billCompanyName}
                                        </Table.Td>
                                        <Table.Td>
                                            {bill.billDescription}
                                        </Table.Td>
                                        <Table.Td>{bill.billAmount}</Table.Td>
                                        <Table.Td>
                                            <Badge
                                                color={
                                                    bill.billState === 1
                                                        ? "green"
                                                        : "red"
                                                }
                                                variant="outline"
                                            >
                                                {bill.billState === 1
                                                    ? t(
                                                          "common.completed",
                                                          lang
                                                      )
                                                    : t(
                                                          "common.not.completed",
                                                          lang
                                                      )}
                                            </Badge>
                                        </Table.Td>
                                        {/* <Table.Td>
                                            <Group>
                                                <CnaButton
                                                    size="xs"
                                                    color="dark.3"
                                                    variant="outline"
                                                    onClick={() =>
                                                        handleFile(
                                                            bill.billID,
                                                            "view"
                                                        )
                                                    }
                                                >
                                                    <Eye size={16} />
                                                </CnaButton>
                                                <CnaButton
                                                    size="xs"
                                                    color="dark.3"
                                                    variant="outline"
                                                    onClick={() =>
                                                        handleFile(
                                                            bill.billID,
                                                            "download"
                                                        )
                                                    }
                                                >
                                                    <DownloadSimple size={16} />
                                                </CnaButton>
                                            </Group>
                                        </Table.Td> */}
                                    </Table.Tr>
                                );
                            }
                        )}
                    </Table.Tbody>
                </Table>
            </ScrollArea>
        </Stack>
    );
};

export default MemberBillingsPage;
