import api from "@/apis";
import { <PERSON><PERSON>, Stack } from "@mantine/core";
import { useMemoizedFn, useMount } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { GearSix } from "@phosphor-icons/react";
import { useLocation } from "react-router-dom";
import PageActionButtons from "@/components/common/PageActionButtons";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import Profile from "@/components/modals/client/Profile";
import useModalStore from "@/store/modal";
import Remark from "@/components/modals/Remark";
import TableModal from "@/components/modals/TableModal";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";

const columnHelper = createColumnHelper<TClient>();

const AdminClientsPage = () => {
    const lang = useSettingStore.use.lang();
    const location = useLocation();

    const openModal = useModalStore.use.open();

    const { profileID } = location.state || {};

    const [clientProfile, setClientProfile] = useState<TClient | null>(null);

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    // 定义状态值常量
    const STATUS_MAP = [
        { value: "0", color: "yellow", label: "待审核" },
        { value: "1", color: "green", label: "已通过" },
    ];

    const tableColumns = [
        columnHelper.accessor("companyID", {
            header: "客户编码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("companyState", {
            header: "状态",
            enableSorting: false,
            cell: (info) => {
                const matchedStatus = STATUS_MAP.find((item) => item.value == info.getValue());
                return <Badge color={matchedStatus?.color}>{matchedStatus?.label}</Badge>;
            },
        }),
        columnHelper.accessor("companyEmail", {
            header: "尽调报告",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("companyProjectID", {
            header: "项目名字",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("companyName", {
            header: "企业名字",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("companyRegisterCode", {
            header: "企业注册号码",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("companyCategoriesID", {
            header: "企业类别",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("companyMobile", {
            header: "手机号码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                return <TableRowDropActionMenu items={rowActions(info.row.original)} />;
            },
        }),
    ];

    useMount(() => {
        if (profileID) {
            // todo 筛选特定合伙人公司
            console.log("profileID", profileID);
        }
    });

    const pageButtons = [
        {
            key: "setting",
            label: "设置",
            leftSection: <GearSix size={14} />,
            onClick: () => {},
        },
    ];

    const rowActions = (row) => [
        {
            key: "profile",
            label: "简介信息",
            onClick: () => {
                setClientProfile(row);
                openModal("clientProfileModal");
            },
        },
        {
            key: "remark",
            label: "备注信息",
            onClick: () => openModal("remarkModal", { objID: row.companyID, type: 4 }),
        },
        {
            key: "billing",
            label: "付款记录",
            onClick: () =>
                openModal("tableModal", { tableKey: "companyBilling", profileID: row.companyID }),
        },
        {
            key: "report",
            label: "尽调报告",
            onClick: () => {},
        },
    ];

    const refreshTable = useMemoizedFn(() => {
        if (tableRef.current) {
            tableRef.current.refresh();
        }
    });

    const handleUpdateSuccess = useMemoizedFn(() => {
        setClientProfile(null);
        refreshTable();
    });

    const handleCloseSuccess = useMemoizedFn(() => {
        setClientProfile(null);
    });

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                status: globalFilters?.status || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.client.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    const handleExport = async () => {
        console.log("export");
    };

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="客户"
                desc="查询客户"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={handleExport}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                    {
                        field: "status",
                        label: "状态",
                        type: "select",
                        options: STATUS_MAP,
                    },
                ]}
            />

            <Profile
                profile={clientProfile}
                onUpdateSuccess={handleUpdateSuccess}
                onCloseSuccess={handleCloseSuccess}
            />
            {/* 渲染备注信息弹窗 */}
            <Remark />
            <TableModal />
        </Stack>
    );
};

export default AdminClientsPage;
