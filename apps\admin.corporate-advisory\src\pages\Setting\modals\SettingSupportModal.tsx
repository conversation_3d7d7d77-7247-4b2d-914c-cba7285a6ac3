import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Group, Modal, Stack, Text, Button } from "@mantine/core";
import { ArrowCircleRight, X } from "@phosphor-icons/react";
import { useContext } from "react";
import SettingPageContext from "../context";
import { useRequest } from "ahooks";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";

const supportOptions = [
    { label: "setting.help.privacy_policy", value: "privacyPolicy", id: 1 },
    { label: "setting.help.service_term", value: "termsOfService", id: 2 },
    { label: "setting.help.cookie_term", value: "cookiesPolicy", id: 3 },
];

const SettingSupportModal = () => {
    const lang = useSettingStore.use.lang();

    const { SettingSupportModal: show, close } = useContext(SettingPageContext);

    const { openFileView } = useFileViewer();

    const { run: openSupportFile } = useRequest(
        async (id: number, label: string) => {
            let response = await cnaRequest<string>(
                "/api/v1/setting/help/generateDocToken",
                "POST",
                {
                    id,
                }
            );

            if (!response.error) {
                const fileToken = response.result.data;
                openFileView(
                    `${window.api_base_url}/api/v1/setting/help/previewDoc/${fileToken}`,
                    {
                        title: t(label, lang),
                    }
                );
            } else {
                noty.error(response.error.message);
            }
        },
        {
            manual: true,
        }
    );

    return (
        <Modal
            opened={show}
            onClose={() => close("SettingSupportModal")}
            title={t("setting.help", lang)}
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-0">
                {supportOptions.map((item) => (
                    <Group
                        key={item.value}
                        className="tw-justify-between tw-px-5 tw-pb-5 tw-border-b tw-cursor-pointer"
                        onClick={() => {
                            openSupportFile(item.id, item.label);
                        }}
                    >
                        <Text>{t(item.label, lang)}</Text>
                        <ArrowCircleRight size={28} />
                    </Group>
                ))}
            </Stack>
            <Group justify="end" className="tw-border-t tw-pt-4">
                <CnaButton
                    color="basic"
                    variant="outline"
                    leftSection={<X weight="bold" />}
                    onClick={() => close("SettingSupportModal")}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default SettingSupportModal;
