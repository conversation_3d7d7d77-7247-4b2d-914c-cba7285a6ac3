import useProfileStore from "@/store/profile";
import { CnaButton } from "@code.8cent/react/components";
import { ButtonProps } from "@mantine/core";

interface CnaAdminButtonProps extends ButtonProps {
    onClick?: React.MouseEventHandler<HTMLButtonElement>;
    needPermission?: string; // 所需权限
    noPermissionHidden?: boolean; // 没有权限后是否隐藏
}

const CnaAdminButton: React.FC<CnaAdminButtonProps> = ({
    needPermission,
    noPermissionHidden,
    ...props
}) => {
    const { permissions } = useProfileStore();

    // 如果不需要权限检查，直接返回按钮
    if (!needPermission) {
        return <CnaButton {...props}>{props.children}</CnaButton>;
    }

    // 没有所需权限且需要隐藏时，返回 null
    if (!permissions.includes(needPermission) && noPermissionHidden) {
        return null;
    }

    // 返回按钮，当没有权限时设置为禁用状态
    return (
        <CnaButton
            {...props}
            disabled={!permissions.includes(needPermission)}
        >
            {props.children}
        </CnaButton>
    );
};

export default CnaAdminButton;
