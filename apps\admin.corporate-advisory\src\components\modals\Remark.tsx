import useModalStore from "@/store/modal";
import React, { useState, useEffect, useCallback } from "react";
import { Box, Group, Modal, Stack, Text, Grid, Textarea, ScrollArea } from "@mantine/core";
import { useMemoizedFn, useMount } from "ahooks";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Check, NotePencil, Trash, X } from "@phosphor-icons/react";
import { useShallow } from "zustand/react/shallow";
import ModalFooter from "@/components/common/ModalFooter";
import { CnaButton } from "@code.8cent/react/components";
import dayjs from "dayjs";
import useProfileStore from "@/store/profile";
import api from "@/apis";
import noty from "@code.8cent/react/noty";

const Remark = ({ onSaveSuccess }: { onSaveSuccess?: () => void }) => {
    const lang = useSettingStore.use.lang();
    const openConfirm = useModalStore.use.openConfirm();

    // 获取传入的参数
    const remarkParams = useModalStore((state) => state.modalParams.remarkModal);
    const objID = remarkParams?.objID;
    const type = remarkParams?.type;

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.remarkModal,
            close: state.close,
        }))
    );

    const logedName = useProfileStore.use.profileName();
    const [newRemark, setNewRemark] = useState("");
    const [page, setPage] = useState(1);
    const [remarks, setRemarks] = useState<TRemarksResponse["items"]>([]);
    const [hasMore, setHasMore] = useState(true);
    const [nowTime, setNowTime] = useState(dayjs().format("YYYY-MM-DD HH:mm:ss"));

    const [oldRemark, setOldRemark] = useState("");
    const [editingRemarkID, setEditingRemarkID] = useState<number | null>(null);

    const fetchRemarks = async (currentPage: number) => {
        try {
            const response = await api.remark.list({
                objID,
                type,
                page: currentPage,
                page_size: 10,
            });

            const remarkItems =
                response?.items?.map((item) => ({
                    ...item,
                    isEditing: false,
                })) || [];

            setRemarks((prev) => [...prev, ...remarkItems]);
            setHasMore(currentPage < (response?.paginate?.total_page || 0));
        } catch (error) {
            console.error("Error fetching remarks:", error);
        }
    };

    const closeModal = useMemoizedFn(() => {
        close("remarkModal");
    });

    useEffect(() => {
        if (show) {
            setPage(1);
            setRemarks([]);
            fetchRemarks(1);
        }
    }, [show]);

    const handleReachBottom = () => {
        if (hasMore) {
            setPage(page + 1);
            fetchRemarks(page + 1);
        }
    };

    useMount(() => {
        const timer = setInterval(() => {
            setNowTime(dayjs().format("YYYY-MM-DD HH:mm:ss"));
        }, 1000);
        return () => clearInterval(timer);
    });

    const handleEdit = (remarkID: number) => {
        setEditingRemarkID(remarkID);

        setOldRemark(remarks.find((remark) => remark.remarkID === remarkID)?.remarkContent || "");
    };

    const handleUpdate = (remarkID: number) => {
        const remarkContent = remarks.find((remark) => remark.remarkID === remarkID)?.remarkContent;
        if (remarkContent !== oldRemark) {
            openConfirm({
                title: "更新备注",
                message: "确认更新该备注吗？",
                onConfirm: async () => {
                    try {
                        await api.remark.update({ remarkContent }, remarkID);
                        noty.success("更新成功");
                        setEditingRemarkID(null);
                    } catch (error) {
                        console.error("更新失败", error);
                    }
                },
            });
        } else {
            setEditingRemarkID(null);
        }
    };

    const handleCancelUpdate = (remarkID: number) => {
        setEditingRemarkID(null);

        setRemarks((prev) =>
            prev.map((item) =>
                item.remarkID === remarkID
                    ? {
                          ...item,
                          isEditing: false,
                          remarkContent: oldRemark,
                      }
                    : item
            )
        );
    };

    const handleRemarkSave = async (remarkID: number | null, content: string) => {
        try {
            if (remarkID) {
                await api.remark.update({ remarkContent: content }, remarkID);
                noty.success("更新成功");
            } else {
                await api.remark.store({
                    objID,
                    remarkType: type,
                    remarkContent: content,
                });
                noty.success("添加成功");
            }
        } catch (error) {
            console.error("保存备注失败:", error);
        } finally {
            setEditingRemarkID(null);
            setNewRemark("");
        }
    };

    const handleDelete = (remarkID: number) => {
        openConfirm({
            title: "删除备注",
            message: "确认删除该备注吗？",
            onConfirm: () => {
                const res = api.remark.delete(remarkID);
                if (res) {
                    setRemarks((prev) => prev.filter((item) => item.remarkID !== remarkID));
                    noty.success("删除成功");
                }
            },
        });
    };

    const handleContentChange = (remarkID: number, newContent: string) => {
        setRemarks((prev) =>
            prev.map((item) =>
                item.remarkID === remarkID ? { ...item, remarkContent: newContent } : item
            )
        );
    };

    const handleNewRemarkChange = useMemoizedFn((event: React.ChangeEvent<HTMLTextAreaElement>) => {
        setNewRemark(event.target.value);
    });

    const handleSave = async () => {
        try {
            await handleRemarkSave(null, newRemark);
            onSaveSuccess?.();
            closeModal();
        } catch (error) {
            noty.error("保存失败");
        } finally {
            setNewRemark("");
        }
    };

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            title="备注信息"
            size="xl"
        >
            <Stack gap="md">
                {/* 备注信息 */}
                {remarks.length > 0 && (
                    <ScrollArea
                        h={300}
                        onBottomReached={handleReachBottom}
                    >
                        {remarks.map((remark) => (
                            <Box
                                key={remark.remarkID}
                                className="tw-border tw-border-gray-300 tw-rounded tw-p-2 tw-my-2"
                            >
                                <Group
                                    justify="space-between"
                                    mb="md"
                                >
                                    <Text>{remark.edit_user.profileName}</Text>
                                    <Text>
                                        {dayjs(remark.created_at).format("YYYY-MM-DD HH:mm:ss")}
                                    </Text>
                                </Group>
                                <Grid>
                                    <Grid.Col span={9}>
                                        {remark.remarkID === editingRemarkID ? (
                                            <Textarea
                                                value={remark.remarkContent}
                                                onChange={(e) =>
                                                    handleContentChange(
                                                        remark.remarkID,
                                                        e.target.value
                                                    )
                                                }
                                            />
                                        ) : (
                                            <Text>{remark.remarkContent}</Text>
                                        )}
                                    </Grid.Col>
                                    {remark.edit_user.profileName === logedName && (
                                        <Grid.Col span={3}>
                                            <Group
                                                justify="end"
                                                gap="xs"
                                            >
                                                {remark.remarkID === editingRemarkID ? (
                                                    <>
                                                        <CnaButton
                                                            size="xs"
                                                            leftSection={<Check size={16} />}
                                                            onClick={() =>
                                                                handleUpdate(remark.remarkID)
                                                            }
                                                        >
                                                            保存
                                                        </CnaButton>
                                                        <CnaButton
                                                            size="xs"
                                                            variant="outline"
                                                            leftSection={<X size={16} />}
                                                            onClick={() =>
                                                                handleCancelUpdate(remark.remarkID)
                                                            }
                                                        >
                                                            取消
                                                        </CnaButton>
                                                    </>
                                                ) : (
                                                    <>
                                                        <CnaButton
                                                            size="xs"
                                                            variant="outline"
                                                            leftSection={<NotePencil size={16} />}
                                                            onClick={() =>
                                                                handleEdit(remark.remarkID)
                                                            }
                                                        >
                                                            修改
                                                        </CnaButton>
                                                        <CnaButton
                                                            size="xs"
                                                            variant="outline"
                                                            leftSection={<Trash size={16} />}
                                                            onClick={() =>
                                                                handleDelete(remark.remarkID)
                                                            }
                                                        >
                                                            删除
                                                        </CnaButton>
                                                    </>
                                                )}
                                            </Group>
                                        </Grid.Col>
                                    )}
                                </Grid>
                            </Box>
                        ))}
                    </ScrollArea>
                )}

                {/* 输入框 */}
                <Group justify="space-between">
                    <Text>{logedName}</Text>
                    <Text>{nowTime}</Text>
                </Group>
                <Textarea
                    placeholder="请输入备注......"
                    value={newRemark}
                    onChange={handleNewRemarkChange}
                />

                <ModalFooter
                    buttons={[
                        {
                            key: "save",
                            label: "保存",
                            leftSection: <Check size={16} />,
                            onClick: handleSave,
                        },
                        {
                            key: "close",
                            label: "关闭",
                            style: "outline",
                            leftSection: <X size={16} />,
                            onClick: closeModal,
                        },
                    ]}
                />
            </Stack>
        </Modal>
    );
};

export default Remark;
