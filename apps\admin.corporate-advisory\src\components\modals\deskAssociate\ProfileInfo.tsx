import { t } from "@code.8cent/i18n";
import {
    AddressSelect,
    CnaButton,
    CountrySelect,
    PhoneInput,
    ProfileAvatar,
} from "@code.8cent/react/components";
import useDataStore from "@code.8cent/store/data";
import useSettingStore from "@code.8cent/store/setting";
import {
    Grid,
    Group,
    Modal,
    Stack,
    TextInput,
    Text,
    Select,
    Card,
    ActionIcon,
    SimpleGrid,
    Textarea,
    Tooltip,
    Table,
    ScrollArea,
    Radio,
} from "@mantine/core";
import ProfileReview from "./ProfileReview";
import { useState, useEffect } from "react";
import { SubmitHand<PERSON>, useForm, Controller } from "react-hook-form";
import deskApi from "@/apis/desk";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { useMemoizedFn, useRequest } from "ahooks";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { getProfileStatus } from "@/utils/constants";
import { useShallow } from "zustand/react/shallow";
import { Download, Eye } from "@phosphor-icons/react";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import Popconfirm from "@/components/common/PopConfirm";
import { downloadFile } from "@/utils/files";
import React from "react";

const userProfileSchema = z.object({
    status: z.string().min(1, "请选择状态"),
    profilePartnerCode: z.string().min(1, "请输入合伙人代码"),
    profileName: z.string().min(1, "请输入名字"),
    profileNRIC: z.string().min(1, "请输入身份证号"),
    profileBirthDate: z.string().min(1, "请输入出生日期"),
    profileContact: z.string().min(1, "请输入联系电话"),
    profileEmail: z.string().email("请输入有效的邮箱地址"),
});

// 定义卡片数据类型
interface CardItem {
    id: string;
    experience?: {
        id: number;
        title_zh: string;
    };
    country?: {
        countryID: number;
        countryZH: string;
    };
    industry?: {
        id: number;
        title_zh: string;
    };
    desc?: string;
    experience_name: string;
    time_range?: string;
    file?: string;
}

const InterviewStatusMap: Record<number, string> = {
    "-2": "缺席",
    "-1": "面试未通过",
    "0": "已预约",
    "1": "面试通过",
    "2": "等待审核",
};

// 卡片组件
const ProfileCard = ({
    item,
    type,
}: {
    item: CardItem;
    type: "certificate" | "skill" | "client";
}) => {
    const getCardContent = () => {
        const { openFileView } = useFileViewer();

        const previewFile = useMemoizedFn((filePath) => {
            openFileView(`/api/v1/admin/file/resource?path=${filePath}&type=local`, {
                title: "资格证书",
            });
        });

        switch (type) {
            case "certificate":
                return (
                    <>
                        <Group justify="space-between">
                            <Text size="xs">资格证书：{item.experience?.title_zh}</Text>
                            {item.file && (
                                <ActionIcon
                                    variant="outline"
                                    onClick={() => previewFile(item.file)}
                                >
                                    <Eye />
                                </ActionIcon>
                            )}
                        </Group>
                        <Text size="xs">时间范围：{item.time_range}</Text>
                        <Text size="xs">所在区域：{item.country?.countryZH}</Text>
                        <Text size="xs">行业领域：{item.industry?.title_zh}</Text>
                    </>
                );
            case "skill":
                return (
                    <>
                        <Text size="xs">专业技能：{item.experience?.title_zh}</Text>
                        <Text size="xs">时间范围：{item.time_range}</Text>
                        <Text size="xs">所在区域：{item.country?.countryZH}</Text>
                        <Text size="xs">行业领域：{item.industry?.title_zh}</Text>
                        <Text size="xs">描述：{item.desc}</Text>
                    </>
                );
            case "client":
                return (
                    <>
                        <Text size="xs">公司名字：{item.experience_name}</Text>
                        <Text size="xs">时间范围：{item.time_range}</Text>
                        <Text size="xs">所在区域：{item.country?.countryZH}</Text>
                        <Text size="xs">行业领域：{item.industry?.title_zh}</Text>
                        <Text size="xs">描述：{item.desc}</Text>
                    </>
                );
            default:
                return null;
        }
    };

    return (
        <Card
            shadow="sm"
            radius="md"
            withBorder
        >
            {getCardContent()}
        </Card>
    );
};

// 卡片区域
const ProfileCardSection = ({
    title,
    items,
    type,
}: {
    title: string;
    items: CardItem[];
    type: "certificate" | "skill" | "client";
}) => {
    const [showAll, setShowAll] = useState(false);
    const displayItems = showAll ? items : items.slice(0, 2);

    return (
        <Stack gap="xs">
            <Group
                justify="space-between"
                className="tw-pl-2 tw-pr-2 tw-py-1 tw-bg-[#060d3d]"
            >
                <Text
                    size="sm"
                    className="tw-text-white"
                >
                    {title}
                </Text>
                {items.length > 2 && (
                    <Text
                        size="sm"
                        className="tw-text-white tw-cursor-pointer hover:tw-opacity-80"
                        onClick={() => setShowAll(!showAll)}
                    >
                        {showAll ? "收起" : "查看更多"}
                    </Text>
                )}
            </Group>
            {items.length > 0 && (
                <SimpleGrid cols={2}>
                    {displayItems.map((item) => (
                        <ProfileCard
                            key={item.id}
                            item={item}
                            type={type}
                        />
                    ))}
                </SimpleGrid>
            )}
            {items.length === 0 && (
                <Text
                    size="sm"
                    c="dimmed"
                    className="tw-text-center tw-py-2"
                >
                    暂无数据
                </Text>
            )}
        </Stack>
    );
};

// 审核按钮组组件
const ReviewButtonGroup = ({
    profile,
    loading,
    onPass,
    onReject,
    onSave,
    onCancel,
    onInfoReview,
    onPaymentReview,
}: {
    profile: any;
    loading: boolean;
    onPass: () => void;
    onReject: () => void;
    onSave: () => void;
    onCancel: () => void;
    onInfoReview: () => void;
    onPaymentReview: () => void;
}) => {
    const { info_status, register_payment_status } = profile?.admin_review_status || {};
    const isInfoReviewed = info_status === 1;
    const isPaymentReviewed = register_payment_status === 1;
    const isPendingReview = profile.status === 2;
    const isAllReviewed = isInfoReviewed && isPaymentReviewed;

    // 显示信息核查按钮
    if (!isAllReviewed) {
        return (
            <>
                {!isInfoReviewed && (
                    <Grid.Col span={{ base: 12, md: 3 }}>
                        <Popconfirm
                            title="提示"
                            content="您确定已核查合伙人信息（身份证、银行卡和合同等）吗？"
                            onConfirm={onInfoReview}
                            confirmText="确认"
                            cancelText="取消"
                        >
                            <CnaButton className="tw-w-full">已核查合伙人信息</CnaButton>
                        </Popconfirm>
                    </Grid.Col>
                )}
                {!isPaymentReviewed && (
                    <Grid.Col span={{ base: 12, md: 3 }}>
                        <Popconfirm
                            title="提示"
                            content="您确定已核查入驻费信息（微信商家后台或银行流水）吗？"
                            onConfirm={onPaymentReview}
                            confirmText="确认"
                            cancelText="取消"
                        >
                            <CnaButton
                                className="tw-w-full"
                                variant="outline"
                            >
                                已核查入驻费信息
                            </CnaButton>
                        </Popconfirm>
                    </Grid.Col>
                )}
            </>
        );
    }

    // 显示保存/取消按钮
    if (!isPendingReview) {
        return (
            <>
                <Grid.Col span={{ base: 12, md: 3 }}>
                    <CnaButton
                        className="tw-w-full"
                        variant="outline"
                        type="submit"
                        onClick={onSave}
                    >
                        保存
                    </CnaButton>
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                    <CnaButton
                        className="tw-w-full"
                        color="basic"
                        onClick={onCancel}
                    >
                        取消
                    </CnaButton>
                </Grid.Col>
            </>
        );
    }

    // 显示审核通过/驳回按钮
    return (
        <>
            <Grid.Col span={{ base: 12, md: 3 }}>
                <CnaButton
                    className="tw-w-full"
                    variant="outline"
                    onClick={onPass}
                    loading={loading}
                >
                    审核通过
                </CnaButton>
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 3 }}>
                <CnaButton
                    className="tw-w-full"
                    color="basic"
                    onClick={onReject}
                >
                    审核驳回
                </CnaButton>
            </Grid.Col>
        </>
    );
};

const ProfileInfo = ({
    onClose = () => {},
    onReviewSuccess = () => {},
    onRejectSuccess = () => {},
}: {
    onClose?: () => void;
    onReviewSuccess?: () => void;
    onRejectSuccess?: () => void;
}) => {
    const { lang } = useSettingStore();
    // 展开的面试结果 id
    const [expandedInterviewId, setExpandedInterviewId] = useState<number | null>(null);

    const { openFileView } = useFileViewer();
    // 预览文件
    const previewFile = useMemoizedFn((filePath) => {
        openFileView(`/api/v1/admin/file/resource?path=${filePath}&type=local`, {
            title: "资格证书",
        });
    });

    const modalParams = useModalStore((state) => state.modalParams.associateProfileModal) || {};
    const { profile = {}, readOnly = false } = modalParams;

    // 通过接口获取 profile 详情
    const { data: profileDetail, loading: detailLoading } = useRequest(
        () => deskApi.associate.info(profile.profileID),
        {
            ready: !!profile.profileID,
        }
    );

    const [stationInterviewComment, setStationInterviewComment] = useState<Record<string, string>>({
        personality: "",
        logic: "",
        insight: "",
        innovation: "",
        efficiency: "",
        status: "",
    });

    // 重置面试评价状态
    const resetStationInterviewComment = useMemoizedFn(() => {
        setStationInterviewComment({
            personality: "",
            logic: "",
            insight: "",
            innovation: "",
            efficiency: "",
            status: "",
        });
    });

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.associateProfileModal,
            close: state.close,
        }))
    );

    const { countryDatas } = useDataStore();

    const [loading, setLoading] = useState(false);

    const [certificateItems, setCertificateItems] = useState<CardItem[]>([]);
    const [skillItems, setSkillItems] = useState<CardItem[]>([]);
    const [clientItems, setClientItems] = useState<CardItem[]>([]);

    // 处理资格证书等
    useEffect(() => {
        if (!profileDetail) return;

        const experiences = profileDetail?.new_experiences || [];

        setCertificateItems(experiences.filter((item) => item.type === 1));
        setSkillItems(experiences.filter((item) => item.type === 2));
        setClientItems(experiences.filter((item) => item.type === 3));
    }, [profileDetail?.new_experiences]);

    const openConfirm = useModalStore.use.openConfirm();
    const openModal = useModalStore.use.open();

    const handlePass = useMemoizedFn(() => {
        openConfirm({
            title: "提示",
            message: "您确定通过该申请吗？",
            onConfirm: async () => {
                handleConfirm();
            },
        });
    });

    const closeModal = useMemoizedFn(() => {
        // 重置面试评价状态
        resetStationInterviewComment();
        close("associateProfileModal");
    });

    const handleConfirm = async () => {
        try {
            const res = await deskApi.associate.review({
                id: profile.profileID,
                status: 1,
            });
            if (res) {
                noty.success("审核通过");
                // 重置面试评价状态
                resetStationInterviewComment();
                close("associateProfileModal");
                onReviewSuccess();
            }
        } catch (error) {
            console.error(error);
        }
    };

    const STATUS_MAP = getProfileStatus(lang);

    const submitForm: SubmitHandler<UserProfileResponse> = async (data) => {
        console.log(data);
    };

    const handleRejectSuccess = () => {
        // 重置面试评价状态
        resetStationInterviewComment();
        close("associateProfileModal");
        onRejectSuccess();
    };

    const {
        register,
        handleSubmit,
        formState: { errors },
        control,
        setValue,
        reset,
    } = useForm<UserProfileResponse>({
        defaultValues: {
            ...profile,
            status: String(profile.status || 0),
        },
        resolver: zodResolver(userProfileSchema),
    });

    useEffect(() => {
        if (!profileDetail) return;

        setValue("status", String(profileDetail.status || 0));
        setValue("profileName", profileDetail.profileName || "");
        setValue("profileNationalityID", profileDetail.profileNationalityID || 0);
        setValue("profilePartnerCode", profileDetail.profilePartnerCode || "");
        setValue("profileNRIC", profileDetail.profileNRIC || "");
        setValue("profileBirthDate", profileDetail.profileBirthDate || "");
        setValue("profileEmail", profileDetail.profileEmail || "");
        setValue("profileContact", profileDetail.profileContact || "");

        if (profileDetail.ai_station_interviews?.[0]?.comment) {
            setStationInterviewComment(JSON.parse(profileDetail.ai_station_interviews[0].comment));
        }
    }, [profileDetail]);

    const handleAdminReviewInfo = useMemoizedFn(
        async (type: "info_status" | "register_payment_status") => {
            setLoading(true);
            try {
                const res = await deskApi.associate.markReviewed(profile.profileID, type);

                if (res) {
                    // todo 更新弹窗、刷新列表等
                    noty.success("标注状态成功");
                    onClose();
                }
            } catch (error) {
                console.error("Error confirming review:", error);
            } finally {
                setLoading(false);
            }
        }
    );

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title={`审核 ${profileDetail?.profileName || ""} 简介`}
            size="xl"
            zIndex={350}
        >
            {detailLoading ? (
                <div className="tw-flex tw-justify-center tw-items-center tw-py-8">
                    <Text>加载中...</Text>
                </div>
            ) : (
                <form
                    onSubmit={handleSubmit(submitForm, (errors) => {
                        console.log(errors);
                    })}
                >
                    <Stack>
                        <Group justify="center">
                            {profileDetail?.profileAvatar && profileDetail.profileAvatar !== "" && (
                                <ProfileAvatar
                                    style={{
                                        width: 120,
                                    }}
                                    src={`${window.api_base_url}${profileDetail.profileAvatar}`}
                                />
                            )}
                        </Group>
                        <Grid>
                            <Grid.Col span={{ base: 12, md: 6 }}>
                                <Stack>
                                    <Controller
                                        name="status"
                                        control={control}
                                        render={({ field }) => (
                                            <Select
                                                label={t("project.table.th.status", lang)}
                                                labelProps={{
                                                    className: "profile-form-label",
                                                }}
                                                data={STATUS_MAP}
                                                {...field}
                                                readOnly={readOnly}
                                            />
                                        )}
                                    />
                                    <Controller
                                        name="profileName"
                                        control={control}
                                        render={({ field }) => (
                                            <TextInput
                                                {...field}
                                                label={t("introduction.label.last_name", lang)}
                                                labelProps={{
                                                    className: "profile-form-label",
                                                }}
                                                readOnly={readOnly}
                                            />
                                        )}
                                    />
                                    <Controller
                                        name="profileNationalityID"
                                        control={control}
                                        render={({ field }) => (
                                            <CountrySelect
                                                {...field}
                                                label={t("introduction.nationality", lang)}
                                                labelProps={{
                                                    className: "profile-form-label",
                                                }}
                                                value={String(profileDetail.profileNationalityID)}
                                                {...{
                                                    data: countryDatas,
                                                    flagKey: "countryISOCode2",
                                                    labelKey:
                                                        `country${lang}` as keyof CountryDataItem,
                                                    valueKey: "countryID",
                                                }}
                                                readOnly={readOnly}
                                            />
                                        )}
                                    />
                                </Stack>
                            </Grid.Col>
                            <Grid.Col span={{ base: 12, md: 6 }}>
                                <Stack>
                                    <Controller
                                        name="profilePartnerCode"
                                        control={control}
                                        render={({ field }) => (
                                            <TextInput
                                                {...field}
                                                label={t("introduction.partner_code", lang)}
                                                labelProps={{
                                                    className: "profile-form-label",
                                                }}
                                                error={errors.profilePartnerCode?.message}
                                                readOnly={readOnly}
                                            />
                                        )}
                                    />
                                    <Controller
                                        name="profileNRIC"
                                        control={control}
                                        render={({ field }) => (
                                            <TextInput
                                                {...field}
                                                label="身份证号码"
                                                labelProps={{
                                                    className: "profile-form-label",
                                                }}
                                                error={errors.profileNRIC?.message}
                                                readOnly={readOnly}
                                            />
                                        )}
                                    />
                                    <Controller
                                        name="profileBirthDate"
                                        control={control}
                                        render={({ field }) => (
                                            <TextInput
                                                {...field}
                                                label={t("form.birth.date", lang)}
                                                labelProps={{
                                                    className: "profile-form-label",
                                                }}
                                                error={errors.profileBirthDate?.message}
                                                readOnly={readOnly}
                                            />
                                        )}
                                    />
                                </Stack>
                            </Grid.Col>
                            <Grid.Col span={12}>
                                <PhoneInput<CountryDataItem>
                                    data={countryDatas}
                                    label={t("introduction.label.phone", lang)}
                                    prefixFlagKey="countryISOCode2"
                                    prefixValueKey="countryID"
                                    prefixLabelKey="countryCode"
                                    wrapperProps={{
                                        labelProps: {
                                            className: "profile-form-label",
                                        },
                                    }}
                                    inputProps={{
                                        value: profileDetail?.profileContact ?? "",
                                    }}
                                    prefixProps={{
                                        w: 120,
                                        value: String(profileDetail?.mobilePrefixID ?? ""),
                                    }}
                                    readOnly
                                />
                            </Grid.Col>
                            <Grid.Col span={12}>
                                <Controller
                                    name="profileEmail"
                                    control={control}
                                    render={({ field }) => (
                                        <TextInput
                                            {...field}
                                            label={t("introduction.label.email", lang)}
                                            labelProps={{
                                                className: "profile-form-label",
                                            }}
                                            error={errors.profileEmail?.message}
                                            readOnly={readOnly}
                                        />
                                    )}
                                />
                            </Grid.Col>
                            <Grid.Col span={12}>
                                <AddressSelect<typeof profile, CountryDataItem>
                                    label={t("introduction.label.address", lang)}
                                    wrapperProps={{
                                        className: "tw-mb-2",
                                        labelProps: {
                                            className: "profile-form-label",
                                        },
                                    }}
                                    addressFieldMap={{
                                        unit: {
                                            key: "profileAddressUnit",
                                            placeholder: t("introduction.label.unit", lang),
                                        },
                                        street: {
                                            key: "profileAddressStreet",
                                            placeholder: t("introduction.label.street", lang),
                                        },
                                        district: {
                                            key: "profileAddressDistrictId",
                                            placeholder: t("introduction.label.district", lang),
                                        },
                                        city: {
                                            key: "profileAddressCityId",
                                            placeholder: t("introduction.label.city", lang),
                                        },
                                        state: {
                                            key: "profileAddressStateId",
                                            placeholder: t("introduction.label.state", lang),
                                        },
                                        postcode: {
                                            key: "profileAddressPostcode",
                                            placeholder: t("introduction.label.postcode", lang),
                                        },
                                        country: {
                                            key: "profileAddressCountry",
                                            placeholder: t("introduction.label.country", lang),
                                        },
                                    }}
                                    countrySelectProps={{
                                        data: countryDatas,
                                        flagKey: "countryISOCode2",
                                        labelKey: `country${lang}` as keyof CountryDataItem,
                                        valueKey: "countryID",
                                    }}
                                    addressData={profileDetail}
                                    readOnly={readOnly}
                                    onAddressDataChange={(addressData) => {
                                        console.log(addressData);
                                    }}
                                    hidePostcodeAndStreet
                                />
                            </Grid.Col>
                        </Grid>

                        {/* 资格证书 专业技能 客户资源 */}
                        <ProfileCardSection
                            title="资格证书"
                            items={certificateItems}
                            type="certificate"
                        />

                        <ProfileCardSection
                            title="专业技能"
                            items={skillItems}
                            type="skill"
                        />

                        <ProfileCardSection
                            title="客户资源"
                            items={clientItems}
                            type="client"
                        />

                        {/* 最高学历与资格证书 */}
                        <Text
                            size="sm"
                            className="tw-text-white tw-bg-[#060d3d] tw-py-1 tw-px-2"
                        >
                            最高学历与资格证书
                        </Text>

                        <Stack gap="xs">
                            {profileDetail?.education && (
                                <Group
                                    justify="space-between"
                                    className="tw-py-1 tw-px-2 tw-bg-gray-100"
                                >
                                    <Text>最高学历</Text>
                                    <Group>
                                        <Tooltip label="查看">
                                            <ActionIcon
                                                onClick={() => previewFile(profileDetail.education)}
                                            >
                                                <Eye />
                                            </ActionIcon>
                                        </Tooltip>
                                        <Tooltip label="下载">
                                            <ActionIcon
                                                onClick={() =>
                                                    downloadFile(profileDetail.education, "remote")
                                                }
                                            >
                                                <Download />
                                            </ActionIcon>
                                        </Tooltip>
                                    </Group>
                                </Group>
                            )}

                            {profileDetail?.certificate && (
                                <Group
                                    justify="space-between"
                                    className="tw-py-1 tw-px-2 tw-bg-gray-100"
                                >
                                    <Text>资格证书</Text>
                                    <Group>
                                        <Tooltip label="查看">
                                            <ActionIcon
                                                onClick={() =>
                                                    previewFile(profileDetail.certificate)
                                                }
                                            >
                                                <Eye />
                                            </ActionIcon>
                                        </Tooltip>
                                        <Tooltip label="下载">
                                            <ActionIcon
                                                onClick={() =>
                                                    downloadFile(
                                                        profileDetail.certificate,
                                                        "remote"
                                                    )
                                                }
                                            >
                                                <Download />
                                            </ActionIcon>
                                        </Tooltip>
                                    </Group>
                                </Group>
                            )}
                        </Stack>

                        {/* 业务案例 */}
                        <Text
                            size="sm"
                            className="tw-text-white tw-bg-[#060d3d] tw-py-1 tw-px-2"
                        >
                            业务案例
                        </Text>
                        {profileDetail?.businessCase && profileDetail.businessCase.length > 0 ? (
                            profileDetail.businessCase.map((caseItem, index) => (
                                <Card
                                    key={index}
                                    className="tw-mb-4"
                                    withBorder
                                >
                                    <Stack gap="xs">
                                        <Text
                                            size="sm"
                                            fw={500}
                                            className="tw-text-gray-700"
                                        >
                                            案例 {index + 1}
                                        </Text>
                                        <TextInput
                                            label="案例名称"
                                            value={caseItem.name || ""}
                                            readOnly
                                        />
                                        <Textarea
                                            label="服务内容"
                                            value={caseItem.content || ""}
                                            readOnly
                                        />
                                        <Textarea
                                            label="成果简述"
                                            value={caseItem.result || ""}
                                            readOnly
                                        />
                                        {(caseItem.address || caseItem.date) && (
                                            <Group gap="md">
                                                {caseItem.address && (
                                                    <TextInput
                                                        label="项目地址"
                                                        value={caseItem.address}
                                                        readOnly
                                                    />
                                                )}
                                                {caseItem.date && (
                                                    <TextInput
                                                        label="项目日期"
                                                        value={caseItem.date}
                                                        readOnly
                                                    />
                                                )}
                                            </Group>
                                        )}
                                    </Stack>
                                </Card>
                            ))
                        ) : (
                            <Text
                                size="sm"
                                className="tw-text-gray-500 tw-py-2"
                            >
                                暂无业务案例
                            </Text>
                        )}

                        {/* AI案例 */}
                        <Text
                            size="sm"
                            className="tw-text-white tw-bg-[#060d3d] tw-py-1 tw-px-2"
                        >
                            AI工具使用经验
                        </Text>
                        {profileDetail?.aiCases && profileDetail.aiCases.length > 0 ? (
                            profileDetail.aiCases.map((aiCase, index) => (
                                <Card
                                    key={index}
                                    className="tw-mb-4"
                                    withBorder
                                >
                                    <Stack gap="xs">
                                        <Text
                                            size="sm"
                                            fw={500}
                                            className="tw-text-gray-700"
                                        >
                                            AI案例 {index + 1}
                                        </Text>
                                        <TextInput
                                            label="AI使用经验"
                                            value={aiCase.name || ""}
                                            readOnly
                                        />
                                        <Textarea
                                            label="经验描述"
                                            value={aiCase.content || ""}
                                            readOnly
                                        />
                                    </Stack>
                                </Card>
                            ))
                        ) : (
                            <Text
                                size="sm"
                                className="tw-text-gray-500 tw-py-2"
                            >
                                暂无AI工具使用经验
                            </Text>
                        )}

                        {/* todo 面试结果和问题，接口返回数据待调整 */}
                        <Text
                            size="sm"
                            className="tw-text-white tw-bg-[#060d3d] tw-py-1 tw-px-2"
                        >
                            面试结果及评价
                        </Text>
                        <Stack gap="0">
                            <Textarea
                                label="个人气质与态度评价："
                                value={stationInterviewComment.personality || ""}
                                readOnly
                            />
                            <Textarea
                                label="逻辑思维能力评价："
                                value={stationInterviewComment.logic || ""}
                                readOnly
                            />
                            <Textarea
                                label="长远眼光与洞察力评价："
                                value={stationInterviewComment.insight || ""}
                                readOnly
                            />
                            <Textarea
                                label="学习与创新能力评价："
                                value={stationInterviewComment.innovation || ""}
                                readOnly
                            />
                            <Textarea
                                label="行动与效率评价："
                                value={stationInterviewComment.efficiency || ""}
                                readOnly
                            />
                            <Group>
                                <Text>面试结果：</Text>
                                <Radio.Group
                                    value={String(stationInterviewComment.status || 0)}
                                    readOnly
                                >
                                    <Group>
                                        <Radio
                                            value="1"
                                            label="通过"
                                        />
                                        <Radio
                                            value="2"
                                            label="不通过"
                                        />
                                    </Group>
                                </Radio.Group>
                            </Group>
                        </Stack>

                        <Grid>
                            <Grid.Col
                                span={6}
                                className="tw-flex tw-items-center"
                            ></Grid.Col>

                            <ReviewButtonGroup
                                profile={profileDetail}
                                loading={loading}
                                onPass={handlePass}
                                onReject={() =>
                                    openModal("associateReviewModal", {
                                        profileId: profileDetail?.profileID,
                                    })
                                }
                                onSave={handleSubmit(submitForm)}
                                onCancel={closeModal}
                                onInfoReview={() => handleAdminReviewInfo("info_status")}
                                onPaymentReview={() =>
                                    handleAdminReviewInfo("register_payment_status")
                                }
                            />
                        </Grid>
                    </Stack>
                </form>
            )}
            <ProfileReview onSubmitSuccess={handleRejectSuccess} />
        </Modal>
    );
};

export default ProfileInfo;
