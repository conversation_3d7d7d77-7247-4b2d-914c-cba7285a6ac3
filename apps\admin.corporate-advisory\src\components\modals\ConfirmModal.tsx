import useModalStore from "@/store/modal";
import { useEffect, useState } from "react";
import { Button, Group, Modal, Stack, Text } from "@mantine/core";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

const ConfirmModal = () => {
    const { show, title, message, onConfirm } = useModalStore.use.confirm();

    const close = useModalStore.use.closeConfirm();

    const [key, setKey] = useState(0);

    const lang = useSettingStore.use.lang();

    useEffect(() => {
        if (show) {
            setKey(new Date().getTime());
        }
    }, [show]);

    return (
        <Modal
            opened={show}
            onClose={close}
            zIndex={1112}
            title={title}
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-pt-5">
                {show === true && (
                    <img
                        src={`/images/icons/icon-alert-warning.svg?k=${key}`}
                        className="tw-w-[100px] tw-mx-auto"
                    />
                )}
                <Text className="tw-text-center tw-text-sm">{message}</Text>
            </Stack>
            <Group justify="end" className="tw-border-t tw-pt-4 tw-mt-4">
                <Button variant="outline" color="basic" onClick={close}>
                    {t("common.close", lang)}
                </Button>

                <Button color="basic" onClick={() => {
                    onConfirm();
                    close();
                }}>
                    {t("common.confirm", lang)}
                </Button>
            </Group>
        </Modal>
    );
};

export default ConfirmModal;
