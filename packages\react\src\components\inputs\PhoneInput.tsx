import { Input, Group, TextInput, Text, Select } from "@mantine/core";
import { useMemoizedFn, useMount } from "ahooks";
import React, { useEffect, useRef, useState } from "react";
import FlagComponent from "../common/FlagComponent";

type PhoneInputProps<T = any> = {
    prefixValueKey: keyof T;
    prefixLabelKey: keyof T;
    prefixFlagKey: keyof T;
    label?: string;
    disabled?: boolean;
    readOnly?: boolean;
    data: T[];
    wrapperProps?: React.ComponentProps<typeof Input.Wrapper>;
    prefixProps?: React.ComponentProps<typeof Select>;
    inputProps?: React.ComponentProps<typeof TextInput>;
};

const PhoneInput = <T,>({
    label,
    data,
    prefixValueKey,
    prefixLabelKey,
    prefixFlagKey,
    prefixProps,
    inputProps,
    wrapperProps,
    disabled,
    readOnly,
}: PhoneInputProps<T>) => {
    const [flagName, setFlagName] = useState<string>("");

    const selectRef = useRef<HTMLInputElement>(null);

    const setFlagItem = useMemoizedFn(() => {
        if (prefixProps?.value?.length > 0) {
            const prefixValue = String(prefixProps?.value);

            const flag_item = data.find((item) => {
                return `${item[prefixValueKey]}` === prefixValue;
            });

            if (flag_item) {
                setFlagName(flag_item[prefixFlagKey] as string);
            }
        }
    });

    useEffect(() => {
        setFlagItem();
    }, [prefixProps?.value, data]);

    useMount(() => {
        setFlagItem();
    });

    return (
        <Input.Wrapper label={label} {...wrapperProps}>
            <Group gap={0} align="stretch">
                <Select
                    {...prefixProps}
                    ref={selectRef}
                    data={data.map((item, _idx) => ({
                        ...item,
                        label: `+${item[prefixLabelKey]}`,
                        value: String(item[prefixValueKey]),
                    }))}
                    comboboxProps={{
                        position: "bottom-start",
                    }}
                    classNames={{
                        ...prefixProps?.classNames,
                        option: `tw-p-0 ${
                            (prefixProps?.classNames as any)?.option ?? ""
                        }`,
                        input: `tw-rounded-e-none ${
                            (prefixProps?.classNames as any)?.input ?? ""
                        }`,
                        dropdown: `tw-min-w-[150px] ${
                            (prefixProps?.classNames as any)?.dropdown ?? ""
                        }`,
                    }}
                    renderOption={({
                        option,
                        checked,
                    }: {
                        option: T & { label: string; value: string };
                        checked: boolean;
                    }) => {
                        return (
                            <Group
                                className={`tw-w-full tw-px-2 tw-py-2 tw-gap-3 ${
                                    checked && "tw-bg-neutral-100"
                                }`}
                            >
                                <FlagComponent
                                    countryCode={`${option[prefixFlagKey]}`}
                                />
                                <Text>{option[prefixFlagKey] as string}</Text>
                                <Text>{option.label}</Text>
                            </Group>
                        );
                    }}
                    leftSection={
                        flagName.length > 0 && (
                            <FlagComponent countryCode={flagName} />
                        )
                    }
                    disabled={disabled}
                    readOnly={prefixProps.readOnly || readOnly}
                />
                <TextInput
                    classNames={{
                        input: "tw-rounded-s-none tw-border-l-transparent focus:tw-border-l-[var(--input-bd-focus)]",
                    }}
                    className="tw-flex-1"
                    {...inputProps}
                    disabled={disabled}
                    readOnly={readOnly}
                />
            </Group>
        </Input.Wrapper>
    );
};

export default PhoneInput;
