import { useEffect, useState } from "react";
import { Grid, GridColProps, Input, TextInput, Select } from "@mantine/core";
import CountrySelect from "./CountrySelect";
import { useDebounce, useDebounceFn } from "ahooks";
import { cnaRequest } from "@code.8cent/utils";

type AddressFieldKeys = "unit" | "street" | "city" | "state" | "postcode" | "country" | "district";

interface AreaData {
    value: string;
    label: string;
    children?: AreaData[];
}

interface ApiAreaData {
    id: number;
    name: string;
    children: ApiAreaData[];
}

type AddressInputProps<TAddressData = any, TCountryData = any> = {
    readOnly?: boolean;
    addressData: TAddressData;
    addressFieldMap: {
        [key in AddressFieldKeys]: {
            placeholder?: string;
            key?: keyof TAddressData;
        };
    };
    errors?: {
        [key in AddressFieldKeys]: boolean;
    };
    label?: string;
    wrapperProps?: Omit<React.ComponentProps<typeof Input.Wrapper>, "children" | "label">;
    countrySelectProps: Omit<
        React.ComponentProps<typeof CountrySelect<TCountryData>>,
        "value" | "onChange" | "searchable" | "placeholder"
    >;
    onAddressDataChange?: (addressData: TAddressData) => void;
    showCountryAndPostcode?: boolean;
    hidePostcodeAndStreet?: boolean;
};

const AddressSelect = <TAddressData = any, TCountryData = any>({
    label,
    addressFieldMap,
    wrapperProps,
    countrySelectProps,
    addressData,
    onAddressDataChange,
    errors,
    readOnly,
    showCountryAndPostcode = true,
    hidePostcodeAndStreet = false,
}: AddressInputProps<TAddressData, TCountryData>) => {
    // 使用本地state来管理输入值
    const [localValues, setLocalValues] = useState<{
        [key in keyof TAddressData]: string;
    }>({} as { [key in keyof TAddressData]: string });

    const debouncedLocalValues = useDebounce(localValues, { wait: 200 });

    useEffect(() => {
        typeof onAddressDataChange === "function" &&
            onAddressDataChange(debouncedLocalValues as TAddressData);
    }, [debouncedLocalValues]);

    // 保存完整的地区数据树
    const [areaData, setAreaData] = useState<AreaData[]>([]);

    // 根据当前选择的省市获取对应的选项列表
    const [cityOptions, setCityOptions] = useState<AreaData[]>([]);
    const [districtOptions, setDistrictOptions] = useState<AreaData[]>([]);

    // 加载地区数据
    const loadAreaData = async () => {
        try {
            const res = await cnaRequest<ApiAreaData[]>("/api/v1/config/divisionCnList", "GET");
            // 格式化数据
            const data = res.result.data.map((item) => ({
                value: item.id.toString(),
                label: item.name,
                children: item.children.map((child) => ({
                    value: child.id.toString(),
                    label: child.name,
                    children: child.children.map((grandChild) => ({
                        value: grandChild.id.toString(),
                        label: grandChild.name,
                    })),
                })),
            }));
            setAreaData(data);
        } catch (error) {
            console.error("加载地区数据失败:", error);
        }
    };

    // 处理省份变化
    const handleProvinceChange = (value: string) => {
        setLocalValues((prev) => ({
            ...prev,
            [addressFieldMap.state.key]: value,
            [addressFieldMap.city.key]: "",
            [addressFieldMap.district.key]: "",
        }));

        // 查找对应省份的城市列表
        const province = areaData.find((p) => p.value === value);
        setCityOptions(province?.children || []);
        setDistrictOptions([]);
    };

    // 处理城市变化
    const handleCityChange = (value: string) => {
        setLocalValues((prev) => ({
            ...prev,
            [addressFieldMap.city.key]: value,
            [addressFieldMap.district.key]: "",
        }));

        // 查找对应城市的区县列表
        const province = areaData.find((p) => p.value === localValues[addressFieldMap.state.key]);
        const city = province?.children?.find((c) => c.value === value);
        setDistrictOptions(city?.children || []);
    };

    // 初始化加载数据
    useEffect(() => {
        loadAreaData();
    }, []);

    // 处理初始值
    useEffect(() => {
        if (areaData.length && addressData) {
            const stateValue = addressData[addressFieldMap.state.key];
            const cityValue = addressData[addressFieldMap.city.key];

            if (stateValue) {
                const province = areaData.find((p) => p.value == stateValue);
                setCityOptions(province?.children || []);

                if (cityValue && province) {
                    const city = province.children?.find((c) => c.value == cityValue);
                    setDistrictOptions(city?.children || []);
                }
            }
        }
    }, [areaData, addressData]);

    const [addressFields] = useState<
        {
            id: AddressFieldKeys;
            placeholder: string;
            key: keyof TAddressData;
            span: GridColProps["span"];
        }[]
    >(() => {
        const fields = [];
        if (showCountryAndPostcode) {
            fields.push(
                {
                    id: "country",
                    placeholder: addressFieldMap.country.placeholder,
                    key: addressFieldMap.country.key,
                    span: { base: 12, md: 6 },
                },
                {
                    id: "postcode",
                    placeholder: addressFieldMap.postcode.placeholder,
                    key: addressFieldMap.postcode.key,
                    span: { base: 12, md: 6 },
                },
            );
        }

        fields.push(
            {
                id: "state",
                // placeholder: addressFieldMap.state.placeholder,
                placeholder: "省份",
                key: addressFieldMap.state.key,
                span: { base: 12, md: 4 },
            },
            {
                id: "city",
                placeholder: addressFieldMap.city.placeholder,
                key: addressFieldMap.city.key,
                span: { base: 12, md: 4 },
            },
            {
                id: "district",
                // placeholder: addressFieldMap.district.placeholder,
                placeholder: "地区",
                key: addressFieldMap.district.key,
                span: { base: 12, md: 4 },
            },
            {
                id: "street",
                placeholder: addressFieldMap.street.placeholder,
                key: addressFieldMap.street.key,
                span: { base: 12 },
            },
            {
                id: "unit",
                placeholder: addressFieldMap.unit.placeholder,
                key: addressFieldMap.unit.key,
                span: { base: 12 },
            }
        )

        if (hidePostcodeAndStreet) {
            // country 的 span 改成 { base: 12}
            fields.find(field => field.id === "country")!.span = { base: 12 };
            // 删除 postcode 和 street
            return fields.filter(field => field.id !== "postcode" && field.id !== "street");
        }

        return fields;
    });

    return (
        <Input.Wrapper
            label={label}
            {...wrapperProps}
        >
            <Grid gutter={3}>
                {addressFields.map((field, index) => (
                    <Grid.Col
                        span={field.span}
                        key={field.id}
                        className="tw-mb-3"
                    >
                        {field.id === "country" ? (
                            <CountrySelect<TCountryData>
                                {...countrySelectProps}
                                placeholder={field.placeholder}
                                searchable={true}
                                value={
                                    (localValues[field.key] as string) ??
                                    (addressData[field.key] as string) ??
                                    ""
                                }
                                onChange={(value) => {
                                    setLocalValues((prev) => ({
                                        ...prev,
                                        [field.key]: value,
                                    }));
                                }}
                                error={errors?.[field.id] ?? false}
                                readOnly={countrySelectProps.readOnly ?? readOnly}
                            />
                        ) : // state city district 使用 select
                        field.id === "state" ? (
                            <Select
                                searchable
                                data={areaData}
                                placeholder={field.placeholder}
                                value={String(localValues[field.key] || addressData[field.key] || '')}
                                onChange={handleProvinceChange}
                                error={errors?.[field.id] ?? false}
                                readOnly={readOnly}
                            />
                        ) : field.id === "city" ? (
                            <Select
                                key={`city-${localValues[addressFieldMap.state.key]}`}
                                searchable
                                data={cityOptions}
                                placeholder={field.placeholder}
                                value={String(localValues[field.key] || addressData[field.key] || '')}
                                onChange={handleCityChange}
                                disabled={!localValues[addressFieldMap.state.key] && !addressData[addressFieldMap.state.key]}
                                error={errors?.[field.id] ?? false}
                                readOnly={readOnly}
                            />
                        ) : field.id === "district" ? (
                            <Select
                                key={`district-${localValues[addressFieldMap.district.key]}`}
                                searchable
                                data={districtOptions}
                                placeholder={field.placeholder}
                                value={String(localValues[field.key] || addressData[field.key] || '')}
                                onChange={(value) => {
                                    setLocalValues((prev) => ({
                                        ...prev,
                                        [field.key]: value,
                                    }));
                                }}
                                disabled={!localValues[addressFieldMap.city.key] && !addressData[addressFieldMap.city.key]}
                                error={errors?.[field.id] ?? false}
                                readOnly={readOnly}
                            />
                        ) : (
                            <TextInput
                                placeholder={field.placeholder}
                                value={
                                    (localValues[field.key] as string) ??
                                    (addressData[field.key] as string) ??
                                    ""
                                }
                                onChange={(e) => {
                                    // 立即更新本地状态
                                    setLocalValues((prev) => ({
                                        ...prev,
                                        [field.key]: e.target.value,
                                    }));
                                }}
                                error={errors?.[field.id] ?? false}
                                readOnly={readOnly}
                            />
                        )}
                    </Grid.Col>
                ))}
            </Grid>
        </Input.Wrapper>
    );
};

export default AddressSelect;
