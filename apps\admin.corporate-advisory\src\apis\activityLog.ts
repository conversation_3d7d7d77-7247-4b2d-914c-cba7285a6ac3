import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const activityLog = {
    list: async (params: TActivityLogSearchParams) => {
        const { error, result } = await cnaRequest<TActivityLogsResponse>(
            "/api/v1/admin/activeLog/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default activityLog;
