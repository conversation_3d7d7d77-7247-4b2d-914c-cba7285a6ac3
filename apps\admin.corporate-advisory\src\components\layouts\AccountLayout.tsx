import { Center, Grid, Image, Stack } from "@mantine/core";
import React, { useEffect } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";

const AccountLayout: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    useEffect(() => {
        if (location.pathname === "/account") {
            navigate("/account/login");
        }
    }, [location.pathname, navigate]);
    return (
        <main className="tw-h-[100vh] tw-w-[100vw] tw-py-10 tw-overflow-y-auto">
            <Outlet />
        </main>
    );
};
export default AccountLayout;
