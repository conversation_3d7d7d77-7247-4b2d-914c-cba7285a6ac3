import ValidateStatusSection from "@/components/register/ValidateStatusSection";
import { t } from "@code.8cent/i18n";
import { PhoneInput } from "@code.8cent/react/components";
import { TextInput, Text, Grid } from "@mantine/core";
import {
    Control,
    Controller,
    UseFormClearErrors,
    UseFormGetValues,
    UseFormTrigger,
} from "react-hook-form";
import { RegisterInput, ValidateStatus } from "../types";
import useSettingStore from "@code.8cent/store/setting";
import useDataStore from "@code.8cent/store/data";
import { useVerifyModal } from "@code.8cent/react/VerifyModal";
import noty from "@code.8cent/react/noty";
import { cnaRequest } from "@code.8cent/utils";
import { modals } from "@mantine/modals";
import { useMemoizedFn } from "ahooks";
import { useNavigate } from "react-router-dom";
import useRegisterStore from "@/store/register";
import { useState } from "react";

type PhoneEmailSectionProps = {
    control: Control<RegisterInput, any>;
    getValues: UseFormGetValues<RegisterInput>;
    trigger: UseFormTrigger<RegisterInput>;
    clearErrors: UseFormClearErrors<RegisterInput>;
    validate: ValidateStatus;
    setValidate: React.Dispatch<React.SetStateAction<ValidateStatus>>;
};

const PhoneEmailSection = ({
    control,
    getValues,
    trigger,
    clearErrors,
    validate,
    setValidate,
}: PhoneEmailSectionProps) => {
    const { lang } = useSettingStore();

    const { countryDatas } = useDataStore();

    const navigate = useNavigate();

    const { setRegisterInfoValue } = useRegisterStore();
    const [phoneError, setPhoneError] = useState(true);
    const [emailError, setEmailError] = useState(true);

    const { open: openEmailValidate } = useVerifyModal({
        id: "registerVerifyEmail",
        isUpdate: false,
        type: "email",
        getCodeUrl: "/api/v1/register/sendEmail",
        verifyUrl: "/api/v1/register/verifyEmail",
        onVerifySuccess(data) {
            setValidate((prev) => ({
                ...prev,
                email: true,
            }));

            clearErrors("email");
        },
    });

    const { open: openPhoneValidate } = useVerifyModal({
        id: "registerVerifyPhone",
        isUpdate: false,
        type: "phone",
        getCodeUrl: "/api/v1/register/sendSms",
        verifyUrl: "/api/v1/register/verifySms",
        onVerifySuccess(data) {
            setValidate((prev) => ({
                ...prev,
                phone: true,
            }));

            clearErrors("phone");
        },
    });

    const registerCheck = useMemoizedFn(
        async (params: { email: string } | { phone: string; prefixID: string }) => {
            let { result, error } = await cnaRequest<{ token?: string }>(
                "/api/v1/register/check",
                "POST",
                {
                    ...params,
                }
            );

            console.log({ result, error });

            if (error) {
                // noty.error("验证失败", error.message);

                modals.openConfirmModal({
                    id: "registerBackToLogin",
                    title: "",
                    children: <Text>{error.message}</Text>,
                    labels: { confirm: "开始设置您的 AI 办公室", cancel: "取消" },
                    confirmProps: { color: "basic" },
                    closeOnConfirm: true,
                    onConfirm: () => {
                        navigate("/account/login");
                    },
                });

                return false;
            } else {
                if (result.data?.token) {
                    modals.openConfirmModal({
                        id: "registerPaymentConfirm",
                        title: "账户未付款",
                        children: <Text>您有注册的账户尚未付款,是否前往付款?</Text>,
                        labels: { confirm: "前往付款", cancel: "取消" },
                        confirmProps: { color: "basic" },
                        closeOnConfirm: true,
                        onConfirm: () => {
                            setRegisterInfoValue("token", result.data.token);
                            navigate("/account/register-payment", { replace: true });
                        },
                    });

                    return false;
                }
            }

            return true;
        }
    );

    return (
        <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
                <Controller
                    name="phone"
                    control={control}
                    render={({ field, fieldState }) => (
                        <PhoneInput<CountryDataItem>
                            // label={t("project.company_form.label.phone", lang)}
                            label={"手机号码"}
                            data={countryDatas}
                            prefixFlagKey="countryISOCode2"
                            prefixLabelKey="countryCode"
                            prefixValueKey="countryID"
                            prefixProps={{
                                w: 78,
                                rightSectionWidth: 0,
                                value: getValues("prefixID"),
                                readOnly: true,
                                classNames: {
                                    input: "tw-border-r-0",
                                },
                            }}
                            disabled={validate.phone}
                            inputProps={{
                                rightSectionWidth: 120,
                                classNames: {
                                    error: "tw-absolute",
                                    input: "tw-rounded-l-none ",
                                },

                                rightSection: !phoneError && (
                                    <ValidateStatusSection
                                        status={validate.phone}
                                        openValidate={async () => {
                                            let phoneOk = await trigger("phone");

                                            if (!phoneOk) {
                                                console.log(fieldState.error);
                                                if (fieldState.error?.type !== "custom") {
                                                    return;
                                                }
                                            }

                                            clearErrors("phone");

                                            let checkPhoneRes = await registerCheck({
                                                phone: getValues("phone"),
                                                prefixID: getValues("prefixID"),
                                            });

                                            //18675699456

                                            if (checkPhoneRes !== true) return;

                                            openPhoneValidate({
                                                initialPhone: getValues("phone"),
                                                initialPrefixID: getValues("prefixID"),
                                            });
                                        }}
                                    />
                                ),
                                ...field,
                                error: fieldState.error?.message
                                    ? t(fieldState.error.message, lang)
                                    : null,
                                onChange: (e) => {
                                    if (e.target.value.length == 11) {
                                        setPhoneError(false);
                                    } else {
                                        setPhoneError(true);
                                    }

                                    field.onChange(e);
                                },
                                placeholder: "输入输入手机号码",
                            }}
                        />
                    )}
                />
            </Grid.Col>
            <Grid.Col span={{ base: 12, md: 6 }}>
                <Controller
                    name="email"
                    control={control}
                    render={({ field, fieldState }) => (
                        <TextInput
                            label={"邮箱地址"}
                            // label={t("form.email.address", lang)}
                            rightSectionWidth={120}
                            classNames={{
                                error: "tw-absolute",
                            }}
                            disabled={validate.email}
                            rightSection={
                                !emailError && (
                                    <ValidateStatusSection
                                        status={validate.email}
                                        openValidate={async () => {
                                            let emailOk = await trigger("email");

                                            if (!emailOk) {
                                                console.log(fieldState.error);
                                                if (fieldState.error?.type !== "custom") {
                                                    return;
                                                }
                                            }
                                            clearErrors("email");

                                            let checkEmailRes = await registerCheck({
                                                email: getValues("email"),
                                            });

                                            //<EMAIL>

                                            if (checkEmailRes !== true) return;

                                            openEmailValidate({
                                                initialEmail: getValues("email"),
                                            });
                                        }}
                                    />
                                )
                            }
                            placeholder="请输入邮箱地址"
                            {...field}
                            error={
                                fieldState.error?.message ? t(fieldState.error.message, lang) : null
                            }
                            onChange={(e) => {
                                if (
                                    e.target.value.includes("@") &&
                                    e.target.value.split("@")[1]?.includes(".") &&
                                    e.target.value.split("@")[1]?.split(".")[1]?.length >= 2
                                ) {
                                    setEmailError(false);
                                } else {
                                    setEmailError(true);
                                }

                                field.onChange(e);
                            }}
                        />
                    )}
                />
            </Grid.Col>
        </Grid>
    );
};
export default PhoneEmailSection;
