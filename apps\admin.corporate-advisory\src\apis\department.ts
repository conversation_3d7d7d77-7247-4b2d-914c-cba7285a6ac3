import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const department = {
    all: async () => {
        const { error, result } = await cnaRequest<BaseApiResponse<TDepartment[]>>(
            "/api/v1/admin/account/department",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default department;
