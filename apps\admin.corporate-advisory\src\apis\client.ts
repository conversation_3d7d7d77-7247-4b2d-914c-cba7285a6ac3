import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const client = {
    list: async (params: TClientSearchParams) => {
        const { error, result } = await cnaRequest<TClientsResponse>(
            "/api/v1/admin/companys/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    update: async (params: TClientUpdateParams, id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/companys/edit/${id}`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 合伙人企业客户
    associateCustomer: async (params) => {
        const { error, result } = await cnaRequest<any>(
            "/api/v1/admin/partner/customer",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default client;
