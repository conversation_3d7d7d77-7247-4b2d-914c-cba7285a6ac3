import { useEffect, useState } from "react";
import { CnaButton } from "@code.8cent/react/components";
import { Group, Modal, Tree, useTree, LoadingOverlay } from "@mantine/core";
import { CaretDown, CaretUp } from "@phosphor-icons/react";
import { useShallow } from "zustand/react/shallow";
import useModalStore from "@/store/modal";
import { useRequest } from "ahooks";
import { cnaRequest } from "@code.8cent/utils";
import api from "@/apis";

const OrganizationModal = () => {
    const openModal = useModalStore.use.open();
    const [loading, setLoading] = useState(false);

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.associateOrganizationModal,
            close: state.close,
        }))
    );

    const [organization, setOrganization] = useState<any[]>([]);
    const [isExpanded, setIsExpanded] = useState(false);

    const organizationParams = useModalStore(
        (state) => state.modalParams.associateOrganizationModal
    );
    const profileId = organizationParams?.profileID;

    // 递归处理团队树结构
    const processTeamTree = (items) => {
        if (!items || !Array.isArray(items)) return [];

        return items.map((item) => {
            const { profileID, profileName, profilePartnerCode, team_rank, descendants } = item;

            // 构建标签附加信息
            const partnerCode = profilePartnerCode || "";
            const teamRankText = team_rank ? ` ${team_rank.job}` : "";
            const labelSuffix = [partnerCode, teamRankText].filter(Boolean).join("");
            const label = profileName + (labelSuffix ? `（${labelSuffix}）` : "");

            return {
                label,
                value: profileID,
                children: descendants ? processTeamTree(descendants) : [],
            };
        });
    };

    // 获取团队架构数据
    const { run: getOrganization } = useRequest(
        async (profileId) => {
            try {
                const res = await cnaRequest("/api/v1/admin/partner/team", "GET", {
                    id: profileId,
                });

                // 格式化团队架构数据
                if (!res || !res.result) {
                    setOrganization([]);
                    return;
                }

                const { data: teamData = [] } = res.result;
                if (!teamData || teamData.length === 0) {
                    setOrganization([]);
                    return;
                }

                const teamArray = Array.isArray(teamData) ? teamData : [teamData];
                const teamTree = processTeamTree(teamArray);

                setOrganization(teamTree);
            } catch (error) {
                console.error("获取团队架构失败:", error);
                setOrganization([]);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (profileId) {
            getOrganization(profileId);
        }
    }, [profileId]);

    const dataTree = useTree();

    const toggleExpand = () => {
        if (isExpanded) {
            dataTree.collapseAllNodes();
        } else {
            dataTree.expandAllNodes();
        }
        setIsExpanded(!isExpanded);
    };

    const closeModal = () => {
        close("associateOrganizationModal");
    };

    // 处理节点点击事件
    const handleNodeClick = async (node) => {
        // 获取合伙人数据
        try {
            setLoading(true);
            const res = await api.associate.info(node.value);
            if (res) {
                // 打开合伙人详情弹窗
                openModal("associateProfileModal", {
                    profile: res,
                    readOnly: true,
                });
            }
        } catch (error) {
            console.error("获取合伙人数据失败:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            size="lg"
            title="团队架构"
        >
            <LoadingOverlay visible={loading} />

            {organization.length > 0 ? (
                <>
                    <Tree
                        data={organization}
                        tree={dataTree}
                        levelOffset={23}
                        renderNode={({ node, expanded, hasChildren, elementProps }) => (
                            <Group
                                gap={5}
                                {...elementProps}
                            >
                                {hasChildren && (
                                    <CaretDown
                                        size={18}
                                        style={{
                                            transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
                                            transition: "transform 0.2s ease",
                                        }}
                                    />
                                )}
                                <span
                                    style={{
                                        cursor: "pointer",
                                        color: "#0066cc",
                                    }}
                                    onClick={(e) => {
                                        e.stopPropagation(); // 防止触发树节点的展开/折叠
                                        handleNodeClick(node);
                                    }}
                                    onMouseOver={(e) => {
                                        e.currentTarget.style.textDecoration = "underline";
                                    }}
                                    onMouseOut={(e) => {
                                        e.currentTarget.style.textDecoration = "none";
                                    }}
                                >
                                    {node.label}
                                </span>
                            </Group>
                        )}
                    />
                    <Group
                        justify="flex-end"
                        mt="md"
                    >
                        <CnaButton
                            variant="outline"
                            leftSection={
                                isExpanded ? <CaretUp size={18} /> : <CaretDown size={18} />
                            }
                            onClick={toggleExpand}
                        >
                            {isExpanded ? "全部折叠" : "全部展开"}
                        </CnaButton>
                    </Group>
                </>
            ) : (
                <div className="tw-text-center tw-py-4">暂无团队架构数据</div>
            )}
        </Modal>
    );
};

export default OrganizationModal;
