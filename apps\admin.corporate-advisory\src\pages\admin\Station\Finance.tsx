import { useState, useCallback, useRef } from "react";
import { Stack, Group, Card, Text, Badge, Grid, Divider } from "@mantine/core";
import { TrendUp, TrendDown, CurrencyDollar, Receipt, HandCoins } from "@phosphor-icons/react";
import { useMount } from "ahooks";
import { ColumnDef } from "@tanstack/react-table";

import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import stationFinance from "@/apis/station/stationFinance";
import dayjs from "dayjs";

// 财务类型映射
const FINANCE_TYPE_MAP = {
    1: { label: "业务收入", color: "green" },
    2: { label: "业务支出", color: "red" },
    3: { label: "豁免", color: "gray" },
    4: { label: "津贴收入", color: "blue" },
    5: { label: "津贴支出", color: "orange" },
};

// 支付方式映射
const PAY_TYPE_MAP = {
    1: { label: "微信", color: "green" },
    2: { label: "支付宝", color: "blue" },
    3: { label: "网银", color: "violet" },
    4: { label: "豁免", color: "gray" },
};

// 状态映射
const STATUS_MAP = {
    1: { label: "未到账", color: "yellow" },
    2: { label: "已到账", color: "green" },
    3: { label: "已出账", color: "red" },
    4: { label: "已豁免", color: "gray" },
};

const GROUP_MAP = {
    0: { label: "普通人" },
    1: { label: "合伙人" },
    2: { label: "管理合伙人" },
    3: { label: "临时管理合伙人" },
};

const StaticCardItem = ({
    icon: Icon,
    title,
    value,
    color = "blue",
    trend,
    trendValue,
    trendColor,
}: {
    icon: any;
    title: string;
    value: string | number;
    color?: string;
    trend?: "up" | "down";
    trendValue?: number;
    trendColor?: string;
}) => (
    <Card
        shadow="sm"
        radius="md"
        withBorder
    >
        <Group>
            <Icon
                size={32}
                color={color}
            />
            <div>
                <Text
                    size="xs"
                    c="dimmed"
                >
                    {title}
                </Text>
                <Text
                    fw={700}
                    size="lg"
                >
                    ¥{typeof value === "number" ? value.toLocaleString() : value}
                </Text>
                {trend && trendValue !== undefined && trendValue !== 0 && (
                    <Group gap={4}>
                        {trend === "up" ? (
                            <TrendUp
                                size={12}
                                color={trendColor || "green"}
                            />
                        ) : (
                            <TrendDown
                                size={12}
                                color={trendColor || "red"}
                            />
                        )}
                        <Text
                            size="xs"
                            c={trendColor || (trend === "up" ? "green" : "red")}
                        >
                            +{trendValue}%
                        </Text>
                    </Group>
                )}
            </div>
        </Group>
    </Card>
);

const AdminStationFinancePage = () => {
    const tableRef = useRef<DataTableRef>(null);

    const [statistics, setStatistics] = useState<TStationFinanceStaticTotal | null>(null);
    const [tableData, setTableData] = useState<TStationFinance[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    // 表格列配置
    const columns: ColumnDef<TStationFinance>[] = [
        {
            accessorKey: "paymentNumber",
            header: "支付单号",
            enableSorting: false,
        },
        {
            accessorKey: "type",
            header: "财务类型",
            enableSorting: false,
            cell: ({ row }) => {
                const type = row.getValue("type") as number;
                const typeInfo = FINANCE_TYPE_MAP[type as keyof typeof FINANCE_TYPE_MAP];
                return typeInfo ? (
                    <Badge
                        color={typeInfo.color}
                        variant="light"
                    >
                        {typeInfo.label}
                    </Badge>
                ) : (
                    type
                );
            },
        },
        {
            accessorKey: "pay_type",
            header: "支付方式",
            enableSorting: false,
            cell: ({ row }) => {
                const payType = row.getValue("pay_type") as number;
                const payTypeInfo = PAY_TYPE_MAP[payType as keyof typeof PAY_TYPE_MAP];
                return payTypeInfo ? (
                    <Badge
                        color={payTypeInfo.color}
                        variant="light"
                    >
                        {payTypeInfo.label}
                    </Badge>
                ) : (
                    payType
                );
            },
        },
        {
            accessorKey: "fee",
            header: "应收金额",
            enableSorting: false,
        },
        {
            accessorKey: "amount",
            header: "已收金额",
            enableSorting: false,
            cell: ({ row }) => {
                const amount = row.getValue("amount") as string;
                const type = row.getValue("type") as number;
                return (
                    <Text
                        fw={500}
                        c={type === 1 || type === 4 ? "green" : "red"}
                    >
                        {type === 1 || type === 4 ? "+" : "-"}¥{amount}
                    </Text>
                );
            },
        },
        {
            accessorKey: "amount",
            header: "实收金额",
            enableSorting: false,
        },
        {
            accessorKey: "detail_name",
            header: "项目名称",
            enableSorting: false,
        },
        // {
        //     accessorKey: "status",
        //     header: "状态",
        //     enableSorting: false,
        //     cell: ({ row }) => {
        //         const status = row.getValue("status") as number;
        //         const statusInfo = STATUS_MAP[status as keyof typeof STATUS_MAP];
        //         return statusInfo ? (
        //             <Badge
        //                 color={statusInfo.color}
        //                 variant="light"
        //             >
        //                 {statusInfo.label}
        //             </Badge>
        //         ) : (
        //             status
        //         );
        //     },
        // },
        {
            accessorKey: "profile",
            header: "合伙人",
            enableSorting: false,
            cell: ({ row }) => {
                const profile = row.getValue("profile") as { profileName: string };
                return profile?.profileName || "-";
            },
        },
        {
            accessorKey: "groupId",
            header: "合伙人角色",
            enableSorting: false,
            cell: ({ row }) => {
                const groupId = row.getValue("groupId") as number;
                const groupInfo = GROUP_MAP[groupId as keyof typeof GROUP_MAP];
                return groupInfo ? groupInfo.label : "-";
            },
        },
        {
            accessorKey: "teamRankName",
            header: "三三制等级",
            enableSorting: false,
        },
        {
            accessorKey: "main",
            header: "公司主体",
            enableSorting: false,
        },
        {
            accessorKey: "commission",
            header: "手续费",
            enableSorting: false,
        },
        {
            accessorKey: "profilePartnerCode",
            header: "合伙人编码",
            enableSorting: false,
        },
        {
            accessorKey: "profileNRIC",
            header: "身份证号",
            enableSorting: false,
        },
        {
            accessorKey: "created_at",
            header: "创建时间",
            enableSorting: false,
            cell: ({ row }) => {
                const date = row.getValue("created_at") as string;
                return new Date(date).toLocaleString("zh-CN");
            },
        },
    ];

    // 全局搜索字段配置
    const globalFilterFields = [
        {
            field: "type",
            label: "财务类型",
            type: "select" as const,
            options: [
                { label: "业务收入", value: "1" },
                { label: "业务支出", value: "2" },
                { label: "豁免", value: "3" },
            ],
        },
        {
            field: "profile_name",
            label: "合伙人姓名",
            type: "text" as const,
        },
        {
            field: "date_range",
            label: "时间范围",
            type: "dateRange" as const,
        },
        {
            field: "profile_code",
            label: "合伙人编码",
            type: "text" as const,
        }
    ];

    // 获取统计数据
    const fetchStatistics = useCallback(async () => {
        try {
            const data = await stationFinance.staticTotal();
            if (data) {
                setStatistics(data);
            }
        } catch (error) {
            console.error("获取统计数据失败:", error);
        }
    }, []);

    // 获取表格数据
    const fetchTableData = useCallback(
        async (params: {
            page: number;
            pageSize: number;
            sorting: any[];
            columnFilters: any[];
            globalFilters: Record<string, any>;
        }) => {
            setLoading(true);
            try {
                // 处理日期范围
                let start_time, end_time;
                if (
                    params.globalFilters.date_range &&
                    Array.isArray(params.globalFilters.date_range)
                ) {
                    start_time =
                        params.globalFilters.date_range[0] &&
                        dayjs(params.globalFilters.date_range[0]).format("YYYY-MM-DD 00:00:00");
                    end_time =
                        params.globalFilters.date_range[1] &&
                        dayjs(params.globalFilters.date_range[1]).format("YYYY-MM-DD 23:59:59");
                }

                const apiParams: TStationFinanceSearchParams = {
                    page: params.page,
                    page_size: params.pageSize,
                    type: params.globalFilters.type
                        ? (Number(params.globalFilters.type) as 1 | 2 | 3)
                        : undefined,
                    profile_name: params.globalFilters.profile_name || undefined,
                    profile_code: params.globalFilters.profile_code || undefined,
                    start_time: start_time || undefined,
                    end_time: end_time || undefined,
                };

                const response = await stationFinance.list(apiParams);

                if (response) {
                    setTableData(response.data);
                    setTotalCount(response.total);
                } else {
                    setTableData([]);
                    setTotalCount(0);
                }
            } catch (error) {
                console.error("获取表格数据失败:", error);
                setTableData([]);
                setTotalCount(0);
            } finally {
                setLoading(false);
            }
        },
        []
    );

    useMount(() => {
        fetchStatistics();
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="合伙人应收明细"
                desc="查询和管理合伙人应收明细"
            />

            {/* 统计卡片 */}
            <Grid>
                <Grid.Col span={{ base: 12, md: 3 }}>
                    <StaticCardItem
                        icon={CurrencyDollar}
                        title="总收入"
                        value={statistics?.income || 0}
                        color="green"
                        trend={statistics?.yesterday?.incomeIncreasePercentage > 0 ? "up" : "down"}
                        trendValue={statistics?.yesterday?.incomeIncreasePercentage}
                        trendColor={
                            statistics?.yesterday?.incomeIncreasePercentage > 0 ? "green" : "red"
                        }
                    />
                </Grid.Col>

                <Grid.Col span={{ base: 12, md: 3 }}>
                    <StaticCardItem
                        icon={Receipt}
                        title="总支出"
                        value={statistics?.expense || 0}
                        color="red"
                        trend={statistics?.yesterday?.expenseIncreasePercentage > 0 ? "down" : "up"}
                        trendValue={statistics?.yesterday?.expenseIncreasePercentage}
                        trendColor="red"
                    />
                </Grid.Col>

                <Grid.Col span={{ base: 12, md: 3 }}>
                    <StaticCardItem
                        icon={CurrencyDollar}
                        title="净收入"
                        value={statistics?.total || 0}
                        color="blue"
                    />
                </Grid.Col>

                <Grid.Col span={{ base: 12, md: 3 }}>
                    <StaticCardItem
                        icon={HandCoins}
                        title="可抵扣"
                        value={statistics?.deductible || 0}
                        color="orange"
                    />
                </Grid.Col>
            </Grid>

            <Divider my="md" />

            <DataTable
                ref={tableRef}
                columns={columns}
                data={tableData}
                totalCount={totalCount}
                loading={loading}
                onFetch={fetchTableData}
                globalFilterFields={globalFilterFields}
                serverSideSort={true}
                getRowId={(row) => row.id}
            />
        </Stack>
    );
};

export default AdminStationFinancePage;
