import { useState } from "react";
import { SortingState, ColumnFiltersState, VisibilityState } from "@tanstack/react-table";
import { useDebounce } from "ahooks";

export function useDataTableState() {
    // 表格状态
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = useState({});
    const [columnOrder, setColumnOrder] = useState<string[]>([]);

    // 本地筛选状态
    const [localColumnFilters, setLocalColumnFilters] = useState<Record<string, any>>({});
    const [localGlobalFilters, setLocalGlobalFilters] = useState<Record<string, any>>({});
    const [globalFilter, setGlobalFilter] = useState<Record<string, any>>({});

    // 防抖处理
    const debouncedGlobalFilters = useDebounce(localGlobalFilters, { wait: 1000 });
    const debouncedLocalFilters = useDebounce(localColumnFilters, { wait: 1000 });

    return {
        sorting,
        setSorting,
        columnFilters,
        setColumnFilters,
        columnVisibility,
        setColumnVisibility,
        rowSelection,
        setRowSelection,
        columnOrder,
        setColumnOrder,
        localColumnFilters,
        setLocalColumnFilters,
        localGlobalFilters,
        setLocalGlobalFilters,
        globalFilter,
        setGlobalFilter,
        debouncedGlobalFilters,
        debouncedLocalFilters,
    };
}
