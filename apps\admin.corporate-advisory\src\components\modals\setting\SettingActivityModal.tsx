import { <PERSON><PERSON>, <PERSON>ack, Group, Button } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import { t } from "@code.8cent/i18n";
import CnaButton from "@code.8cent/react/components/CnaButton";
import useSettingStore from "@code.8cent/store/setting";
import { X } from "@phosphor-icons/react";

const ActivityLogModal = () => {
    const lang = useSettingStore.use.lang();

    const activityLog = [
        // Example logs can be added here
    ];

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.settingActivity,
            close: state.close,
        }))
    );

    return (
        <Modal
            opened={show}
            onClose={() => close("settingActivity")}
            title={t("setting.active_log", lang)} // Using dynamic lang variable
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-6 tw-overflow-y-auto tw-max-h-[60vh] tw-px-3">
                <p className="tw-text-gray-500">{t("activity.log.check", lang)}</p>
                {
                    activityLog.length === 0 && (
                        <p className="tw-text-gray-500 tw-text-center tw-my-56">{t("activity.log.empty", lang)}</p>
                    )
                }
                {activityLog.map((log, index) => (
                    <Group
                        key={index}
                        className="tw-text-sm tw-border tw-rounded-lg tw-p-3 tw-justify-between tw-items-center tw-mb-0 -tw-mt-3"
                    >
                        <div className="tw-w-full">
                            <div className="tw-text-gray-500 tw-mt-1">
                                {dayjs(log.date).format("YYYY-MM-DD HH:mm:ss")}
                            </div>
                            <div className="tw-text-gray-700 tw-font-medium">
                                {log.action}
                            </div>
                        </div>
                    </Group>
                ))}
            </Stack>
            <div className="tw-border-t tw-pt-4 tw-mt-4 tw-flex tw-justify-end tw-space-x-3">
                <CnaButton
                    color="cna"
                    variant="outline"
                    onClick={() => close("settingActivity")}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </div>
        </Modal>
    );
};

export default ActivityLogModal;
