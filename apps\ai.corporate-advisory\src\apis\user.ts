import cnaRequest from "@code.8cent/utils/cnaRequest";

const user = {
    updatePassword: async (oldPassword: string, newPassword: string) => {
        let updatePasswordRes = await cnaRequest(
            "/api/v1/password/update",
            "POST",
            {
                oldPassword,
                newPassword,
            }
        );

        if (updatePasswordRes.error) {
            return false;
        } else {
            return true;
        }
    },
    resetPassword: async (params: {
        code: string;
        email: string;
        password: string;
    }) => {
        const resetPasswordRes = await cnaRequest(
            "/api/v1/password/forgotPsw/reset",
            "POST",
            params
        );

        if (resetPasswordRes.error) {
            return false;
        } else {
            return true;
        }
    },
    getUserProfile: async () => {
        let profileRes = await cnaRequest<UserProfileResponse>(
            "/api/v1/user/profile",
            "POST"
        );

        const { result, error } = profileRes;

        if (result) {
            return result.data;
        } else {
            return null;
        }
    },
    updateUserProfile: async (
        profile: Partial<
            UserProfileResponse & {
                professional: UserProfileResponse["userProfessional"];
                skill: UserProfileResponse["userSkill"];
                experience: UserProfileResponse["userExperience"];
            }
        >
    ) => {
        let updateProfileRes = await cnaRequest(
            "/api/v1/user/updateProfile",
            "POST",
            profile
        );

        const { result, error } = updateProfileRes;

        if (result) {
            return true;
        } else {
            return false;
        }
    },
    /**
     * Updates the user's avatar.
     *
     * @param {File} avatar The avatar file selected by the user.
     * @returns Returns the updated avatar URL, or null if the update fails.
     */
    updateAvatar: async (avatar: File) => {
        const formData = new FormData();

        formData.append("avatar", avatar);

        let updateAvatarRes = await cnaRequest<string>(
            "/api/v1/user/updateAvatar",
            "POST",
            formData
        );

        const { result, error } = updateAvatarRes;

        if (error) {
            return null;
        } else {
            return result.data;
        }
    },

    /**
     * Asynchronously fetches the notification list.
     *
     * This function sends a network request to retrieve the user's notifications based on the specified state and pagination parameters.
     * It uses an optional state parameter to filter notifications. If no state parameter is provided, all notifications are returned.
     *
     * @param state The state of the notifications: 0 for unread, 1 for read. Omitting this parameter returns all states. Optional, default is null.
     * @param page The page number indicating which page of notifications to retrieve. Optional, default is 1.
     * @param size The page size indicating how many notifications each page should contain. Optional, default is 5.
     * @returns An array containing the notifications filtered by the specified state and pagination parameters. Returns an empty array if the retrieval fails or there are no results.
     */
    notificationList: async (
        status: "all" | "read" | "unread" = "all",
        page: number = 1,
        pageSize: number = 5
    ) => {
        let params: { page: number; pageSize: number; status?: number } = {
            page,
            pageSize,
        };

        if (status !== "all") {
            params.status = status === "read" ? 1 : 0;
        }

        let notificationListRes = await cnaRequest<NotifcationsResponse>(
            "/api/v1/notifications",
            "GET",
            params
        );

        const { result, error } = notificationListRes;

        if (result) {
            return result.data.notification;
        } else {
            return [];
        }
    },
    readNotification: async (params: ReadNotificationParams) => {
        let url = "";

        if (params.mode === "all") {
            url = "/api/v1/notification/readAll";
        } else {
            url = "/api/v1/notification/readSingle";
        }

        let readNotificationRes = await cnaRequest(
            url,
            "POST",
            params.mode === "all"
                ? {}
                : { notificationID: params.notificationID }
        );

        const { result, error } = readNotificationRes;

        if (result) {
            return true;
        } else {
            return false;
        }
    },

    getNotificationCount: async () => {
        let url = "/api/v1/notification/notificationStatusNumber";

        type NotificationCountResponse = {
            all: {num: number};
            unread: {num: number};
        };

        let countRes = await cnaRequest<NotificationCountResponse>(url, "GET");

        const { result, error } = countRes;

        if (error) {
            return {
                all: 0,
                unread: 0,
            };
        } else {
            return {
                all: result.data.all.num,
                unread: result.data.unread.num,
            };
        }
    },

    getUserDocumentList: async (
        page: number = 1,
        pageSize: number = 5,
        keyword: string = ""
    ) => {
        let documentListRes = await cnaRequest<DocumentResponse>(
            "/api/v1/documents",
            "GET",
            {
                page,
                pageSize,
                keyword,
            }
        );

        const { error, result } = documentListRes;

        if (error) {
            return [];
        } else {
            return result.data.document;
        }
    },

    getDocumentToken: async (documentID: number) => {
        let docRealLinkRes = await cnaRequest<string>(
            "/api/v1/document/generateKey",
            "POST",
            {
                documentID,
            }
        );

        const { error, result } = docRealLinkRes;

        if (error) {
            return null;
        } else {
            return result.data;
        }
    },

    getBillList: async (params: {
        keyword?: string;
        page: number;
        pageSize: number;
    }) => {
        let { result, error } = await cnaRequest<BillListResponse>(
            "/api/v1/bills",
            "GET",
            params
        );

        if (error) {
            return [];
        } else {
            return result.data.bills;
        }
    },

    updateLanguageRegionSetting: async (langRegion: {
        settingCurrency?: string;
        settingTimeFormat?: string;
        settingTimezone?: string;
        settingDateFormat?: string;
        settingLanguage?: string;
    }) => {
        const { result, error } = await cnaRequest(
            "/api/v1/setting/languageArea/update",
            "POST",
            JSON.stringify(langRegion)
        );

        if (error) {
            return false;
        } else {
            return true;
        }
    },
};

type ReadNotificationParams =
    | {
          mode: "all";
      }
    | {
          mode: "single";
          notificationID: string;
      };

export default user;
