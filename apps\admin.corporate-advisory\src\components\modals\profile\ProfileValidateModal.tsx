import api from "@/apis";
import useModalStore from "@/store/modal";
import useProfileStore from "@/store/profile";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMemoizedFn } from "ahooks";
import React, { useEffect, useState } from "react";
import { Button, Group, Modal, Stack, TextInput } from "@mantine/core";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { useShallow } from "zustand/react/shallow";
import CnaButton from "@code.8cent/react/components/CnaButton";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";

type VerifyFormInput = {
    code: string;
};

const verifyFormSchema = z.object({
    code: z.string().min(1, "Verify code is required"),
});

const initalValues = {
    code: "",
};

const ProfileValidateModal: React.FC<{
    type: "contact" | "email";
}> = ({ type }) => {
    const lang = useSettingStore.use.lang();

    const { show, close, alert } = useModalStore(
        useShallow((state) => ({
            show: state.profileValidate,
            close: state.close,
            alert: state.openAlert,
        }))
    );

    const { profileEmail, profileContact, setProfileValue } = useProfileStore(
        useShallow((state) => ({
            profileEmail: state.profileEmail,
            profileContact: state.profileContact,
            setProfileValue: state.setProfileValue,
        }))
    );

    const {
        register,
        handleSubmit,
        formState: { errors },
        setError,
        reset,
    } = useForm<VerifyFormInput>({
        defaultValues: initalValues,
        resolver: zodResolver(verifyFormSchema),
    });

    const [countdown, setCountdown] = useState<number | null>(null);

    const [requestingCode, setRequestingCode] = useState(false);

    const [verifying, setVerifying] = useState(false);

    const closeModal = () => {
        close("profileValidate");
        reset();
    };

    const getEmailCode = useMemoizedFn(async () => {
        setRequestingCode(true);

        let send_res = await api.email.sendVerifyEmail(profileEmail);

        if (send_res === true) {
            setCountdown(60);
        }

        setRequestingCode(false);
    });

    const verifyEmail: SubmitHandler<VerifyFormInput> = useMemoizedFn(
        async (data) => {
            setVerifying(true);

            let verify_res = await api.email.verifyEmail(profileEmail, data.code);

            if (verify_res === true) {
                alert(
                    t("validation.success", lang),
                    t("validation.email.success", lang),
                    "success"
                );
                setProfileValue("profileEmailValidate", "Y");
                closeModal();
            } else {
                alert(
                    t("validation.fail"),
                    t("validation.input", lang),
                    "danger"
                );
            }

            setVerifying(false);
        }
    );

    useEffect(() => {
        if (countdown === null || countdown === 0) return;

        const timer = setTimeout(() => {
            setCountdown((prev) => (prev !== null ? prev - 1 : null));
        }, 1000);

        return () => clearTimeout(timer);
    }, [countdown]);

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            title={`${type === "contact" ? t("project.company_form.label.phone", lang) : t("project.company_form.label.mail", lang)}${t("common.verify", lang)}`}
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-my-5">
                {type === "email" && (
                    <>
                        <TextInput
                            readOnly
                            value={profileEmail}
                            label={t("login.label.email", lang)}
                            rightSectionWidth={120}
                            rightSectionProps={{ className: "tw-justify-end" }}
                            rightSection={
                                countdown !== null && countdown > 0 ? (
                                    <CnaButton
                                        variant="transparent"
                                        className="disabled:tw-bg-transparent tw-font-normal"
                                        disabled
                                        color="cna"
                                    >
                                    {`${t("validation.before.second", lang)} ${countdown} ${t("validation.after.second", lang)}`}
                                    </CnaButton>
                                ) : (
                                    <CnaButton
                                        variant="transparent"
                                        onClick={getEmailCode}
                                        loading={requestingCode}
                                        color="cna"
                                    >
                                        {t("validation.send", lang)}
                                    </CnaButton>
                                )
                            }
                        />

                        <TextInput
                            error={errors.code?.message}
                            label={t("validation.code", lang)}
                            placeholder={t("validation.enter.code", lang)}
                            {...register("code")}
                        />
                    </>
                )}
                {type === "contact" && (
                    <p className="text-center">{t("validate.empty", lang)}</p>
                )}
            </Stack>
            <Group className="tw-pt-5" justify="end">
                {type === "email" && (
                    <CnaButton
                        onClick={handleSubmit(verifyEmail)}
                        loading={verifying}
                        color="cna"
                    >
                        {t("common.submit", lang)}
                    </CnaButton>
                )}
            </Group>
        </Modal>
    );
};

export default ProfileValidateModal;
