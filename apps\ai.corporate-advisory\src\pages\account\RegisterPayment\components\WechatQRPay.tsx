import api from "@/apis";
import { useRequest } from "ahooks";
import { Image, Stack } from "@mantine/core";
import { CnaButton } from "@code.8cent/react/components";
import { ArrowClockwise } from "@phosphor-icons/react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";

const WechatQRPay = ({ token }: { token: string }) => {
    const { lang } = useSettingStore();

    const { run: generatePayQRCode, data: payQRCode } = useRequest(async () => {
        let qrCode = await api.register.generateWechatPayQRCode(token);

        return qrCode;
    });

    return (
        <Stack gap={2}>
            <Image
                className="tw-mx-auto"
                src={payQRCode}
                w={180}
                h={180}
            />

            <CnaButton
                variant="transparent"
                leftSection={<ArrowClockwise />}
                color="basic"
                onClick={generatePayQRCode}
                w="auto"
                className="tw-mx-auto"
            >
                {t("payment.qr.code", lang)}
            </CnaButton>
        </Stack>
    );
};

export default WechatQRPay;
