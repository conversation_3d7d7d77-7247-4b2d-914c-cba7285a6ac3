import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const benefit = {
    list: async (params: TBenefitSearchParams) => {
        const { error, result } = await cnaRequest<TBenefitsResponse>(
            "/api/v1/admin/benefitInfo/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (
        params: Omit<
            TBenefit,
            "benefitID" | "createUser" | "createTime" | "editUser" | "editTime"
        >
    ) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/benefitInfo/create",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (
        params: Omit<
            TBenefit,
            "benefitID" | "createUser" | "createTime" | "editUser" | "editTime"
        >,
        id: number
    ) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/benefitInfo/edit/${id}`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    destroy: async (id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/benefitInfo/destroy/${id}`,
            "DELETE"
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    applications: async (params: TBenefitApplicationSearchParams) => {
        const { error, result } =
            await cnaRequest<TBenefitApplicationsResponse>(
                "/api/v1/admin/benefitUser/index",
                "GET",
                params
            );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    reviewApplication: async (params: TBenefitApplicationReviewParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/benefitUser/check",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default benefit;
