import api from "@/apis";
import { Stack } from "@mantine/core";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { GearSix } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import dayjs from "dayjs";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";

const columnHelper = createColumnHelper<TBill>();

const AdminBillingsPage = () => {
    const lang = useSettingStore.use.lang();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const pageButtons = [
        {
            key: "setting",
            label: "设置",
            leftSection: <GearSix size={14} />,
            onClick: () => {},
        },
    ];

    const rowActions = (row) => [
        {
            key: "profile",
            label: "查看详情",
            onClick: () => {},
        },
    ];

    const tableColumns = [
        columnHelper.accessor("billPaymentCode", {
            header: "发票编码",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("billState", {
            header: "状态",
            enableSorting: false,
            cell: (info) => t(`bill.state.${info.getValue()}`, lang),
        }),
        columnHelper.accessor("billCompanyName", {
            header: "企业名字",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("billDescription", {
            header: "详情",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("billAmount", {
            header: "数额",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("created_at", {
            header: "创建日期",
            enableSorting: false,
            cell: (info) =>
                info.getValue() && dayjs(info.getValue() as string).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("billType", {
            header: "类型",
            enableSorting: false,
            cell: (info) => t(`bill.type.${info.getValue()}`, lang),
        }),
        columnHelper.accessor("billPaymentType", {
            header: "付款方式",
            enableSorting: false,
            cell: (info) => t(`bill.payment_type.${info.getValue()}`, lang),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        }),
    ];

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.bill.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    const handleExport = async () => {
        console.log("export");
    };

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="账单"
                desc="查询账单"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={handleExport}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
            />
        </Stack>
    );
};

export default AdminBillingsPage;
