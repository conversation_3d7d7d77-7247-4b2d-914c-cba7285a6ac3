import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import useModalStore from "@/store/modal";
import useSettingStore from "@code.8cent/store/setting";
import { Group, Modal, Stack, Text, Button } from "@mantine/core";
import { ArrowCircleRight, X } from "@phosphor-icons/react";
import { useShallow } from "zustand/react/shallow";

const supportOptions = [
    { label: "setting.help.privacy_policy", value: "privacyPolicy" },
    { label: "setting.help.service_term", value: "termsOfService" },
    { label: "setting.help.cookie_term", value: "cookiesPolicy" },
];

const SettingSupportModal = () => {
    const lang = useSettingStore.use.lang();

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.settingSupport,
            close: state.close,
        }))
    );

    return (
        <Modal
            opened={show}
            onClose={() => close("settingSupport")}
            title={t("setting.help", lang)}
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-0">
                {supportOptions.map((item) => (
                    <Group
                        key={item.value}
                        className="tw-justify-between tw-px-5 tw-pb-5 tw-border-b tw-cursor-pointer"
                    >
                        <Text>{t(item.label, lang)}</Text>
                        <ArrowCircleRight size={28} />
                    </Group>
                ))}
            </Stack>
            <Group justify="end" className="tw-border-t tw-pt-4">
                <CnaButton
                    color="cna"
                    variant="outline"
                    leftSection={<X weight="bold" />}
                    onClick={() => close("settingSupport")}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default SettingSupportModal;
