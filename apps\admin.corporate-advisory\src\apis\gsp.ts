import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const gsp = {
    list: async (params: TGspApplicationSearchParams) => {
        const { error, result } = await cnaRequest<TGspApplicationResponse>(
            `/api/v1/admin/gsp/index`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    reviewApplication: async (
        params: TReviewGspApplicationParams | FormData,
        type: "form" | "report" | "contract" | "reCheck"
    ) => {
        const url =
            type === "form"
                ? `/api/v1/admin/gsp/checkForm`
                : type === "report"
                ? `/api/v1/admin/gsp/checkReport`
                : type === "contract"
                ? `/api/v1/admin/gsp/checkContract`
                : `/api/v1/admin/gsp/caCheckReport`;

        const { error } = await cnaRequest(url, "POST", params);

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 获取尽调报告上传文件
    getReportFiles: async (applicationId: string) => {
        const { result, error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/gsp/reportDetail?id=${applicationId}`,
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 审核尽调报告文件
    checkReportFile: async (params: { id: string; status: number; file_id: number }) => {
        const { error } = await cnaRequest(
            `/api/v1/admin/gsp/checkReportFile`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 退款列表
    refundList: async (params: TGspRefundSearchParams) => {
        const { error, result } = await cnaRequest<TGspRefundListResponse>(
            `/api/v1/admin/gsp/orderRefund`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 退款审核
    refundCheck: async (params: TRefundExamineParams) => {
        const { error } = await cnaRequest(
            `/api/v1/admin/gsp/refundCheck`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },

    // 线下付款列表
    paymentVoucherList: async ({page, page_size, }: {page?: number, page_size?: number}) => {
        const { error, result } = await cnaRequest<TGspPaymentVoucherListResponse>(
            `/api/v1/admin/gsp/paymentVouchers`,
            "GET",
            {page, page_size}
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 线下付款审核
    paymentVoucherCheck: async (params: TGspVoucherCheckParams) => {
        const { error } = await cnaRequest(
            `/api/v1/admin/gsp/voucherCheck`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default gsp;
