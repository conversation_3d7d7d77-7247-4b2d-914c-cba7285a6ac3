import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, json, Navigate } from "react-router-dom";
import RouteError from "@code.8cent/react/components/RouteError";
import RootLayout from "@/components/layouts/RootLayout";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import AdminAssociatesPage from "@/pages/admin/Associates";
import AccountWizardPage from "@/pages/Wizard";
import LoginPage from "@/pages/Login";
import ForgetPasswordPage from "@code.8cent/react/pages/ForgetPasswordPage";
import AdminClientsPage from "@/pages/admin/Clients";
import AdminNoticesPage from "@/pages/admin/Notices";
import AdminFieldTypePage from "@/pages/admin/FieldType";
import AdminReviewPage from "@/pages/admin/Review";
import AdminBillingsPage from "@/pages/admin/Billings";
import AdminAccoutsPage from "@/pages/admin/Accounts";
import AdminDocumentsPage from "@/pages/admin/Documents";
import AdminTeamsPage from "@/pages/admin/Teams";
import AdminBenefitsPage from "@/pages/admin/Benefits";
import SettingPage from "@/pages/Setting";
import WebStructurePage from "@/pages/web/Structure";
import AdminFirmPage from "@/pages/admin/Firm";
import AssociatePaidDetail from "@/pages/admin/Finance/AssociatePaidDetail";
import AssociateReceivedDetail from "@/pages/admin/Finance/AssociateReceivedDetail";
import AssociatePaidTotal from "@/pages/admin/Finance/AssociatePaidTotal";
import AssociateReceivedTotal from "@/pages/admin/Finance/AssociateReceivedTotal";
import AdminDigitalHumanPage from "@/pages/admin/DigitalHuman";
import AdminGspApplicationsPage from "@/pages/admin/Gsp/Applications";
import AdminVisitorAppsPage from "@/pages/admin/Visitor/Apps";
import AdminVisitorVipsPage from "@/pages/admin/Visitor/Vips";
import AdminVisitorReportsPage from "@/pages/admin/Visitor/Reports";
import AdminActivityPage from "@/pages/admin/Activity";
import AdminInterviewQuestionPage from "@/pages/admin/InterviewQuestion";
import AdminExamQuestionPage from "@/pages/admin/Station/ExamQuestion";
import AdminDeskAssociatesPage from "@/pages/admin/DeskAssociates";
import AdminStationCurrencyPage from "@/pages/admin/Station/Currency";
import AdminStationRefundPage from "@/pages/admin/Station/Refund";
import AdminStationFinancePage from "@/pages/admin/Station/Finance";
import AdminGspRefundsPage from "@/pages/admin/Gsp/Refund";
import AdminCompanyReceivedDetailPage from "@/pages/admin/Finance/CompanyReceivedDetail";
import AdminFirmProcessPage from "@/pages/admin/FirmProcess";
import AdminGspPaymentVouchersPage from "@/pages/admin/Gsp/PaymentVouchers";

const router = createBrowserRouter([
    {
        path: "/",
        element: <RootLayout />,
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        children: [
            {
                path: "account/login",
                element: <LoginPage />,
            },
            {
                path: "account/forget-password",
                element: <ForgetPasswordPage />,
            },
            {
                path: "account/wizard",
                element: <AccountWizardPage />,
            },
            {
                path: "admin",
                element: <DashboardLayout />,
                children: [
                    {
                        path: "review",
                        element: <AdminReviewPage />,
                    },
                    {
                        path: "associates",
                        element: <AdminAssociatesPage />,
                    },
                    {
                        path: "desk-associates",
                        element: <AdminDeskAssociatesPage />,
                    },
                    {
                        path: "clients",
                        element: <AdminClientsPage />,
                    },
                    {
                        path: "notices",
                        element: <AdminNoticesPage />,
                    },
                    {
                        path: "fieldType",
                        element: <AdminFieldTypePage />,
                    },
                    {
                        path: "bills",
                        element: <AdminBillingsPage />,
                    },
                    {
                        path: "accounts",
                        element: <AdminAccoutsPage />,
                    },
                    {
                        path: "documents",
                        element: <AdminDocumentsPage />,
                    },
                    {
                        path: "teams",
                        element: <AdminTeamsPage />,
                    },
                    {
                        path: "benefits",
                        element: <AdminBenefitsPage />,
                    },
                    {
                        path: "settings",
                        element: <SettingPage />,
                    },
                    {
                        path: "web/structures",
                        element: <WebStructurePage />,
                    },
                    {
                        path: "firms/app",
                        element: <AdminFirmPage />,
                    },
                    {
                        path: "firms/process",
                        element: <AdminFirmProcessPage />,
                    },
                    {
                        path: "finance/associate-paid-detail",
                        element: <AssociatePaidDetail />,
                    },
                    {
                        path: "finance/associate-received-detail",
                        element: <AssociateReceivedDetail />,
                    },
                    {
                        path: "finance/associate-paid-total",
                        element: <AssociatePaidTotal />,
                    },
                    {
                        path: "finance/associate-received-total",
                        element: <AssociateReceivedTotal />,
                    },
                    {
                        path: "finance/company-received-detail",
                        element: <AdminCompanyReceivedDetailPage />,
                    },
                    {
                        path: "digital-human",
                        element: <AdminDigitalHumanPage />,
                    },
                    {
                        path: "projects/green-earth/applications",
                        element: <AdminGspApplicationsPage />,
                    },
                    {
                        path: "projects/green-earth/refunds",
                        element: <AdminGspRefundsPage />,
                    },
                    {
                        path: "projects/green-earth/payment-vouchers",
                        element: <AdminGspPaymentVouchersPage />,
                    },
                    {
                        path: "visitors/apps",
                        element: <AdminVisitorAppsPage />,
                    },
                    {
                        path: "visitors/vips",
                        element: <AdminVisitorVipsPage />,
                    },
                    {
                        path: "visitors/reports",
                        element: <AdminVisitorReportsPage />,
                    },
                    {
                        path: "activities",
                        element: <AdminActivityPage />,
                    },
                    {
                        path: "questions/interview",
                        element: <AdminInterviewQuestionPage />,
                    },
                    {
                        path: "questions/exam",
                        element: <AdminExamQuestionPage />,
                    },
                    {
                        path: "station/currency",
                        element: <AdminStationCurrencyPage />,
                    },
                    {
                        path: "station/refund",
                        element: <AdminStationRefundPage />,
                    },
                    {
                        path: "station/finance",
                        element: <AdminStationFinancePage />,
                    },
                    {
                        path: "*",
                        errorElement: <RouteError className="tw-h-[100%] tw-w-[100%]" />,
                        loader: () => {
                            throw json({}, { status: 404, statusText: "Page Not Found" });
                        },
                    },
                ],
            },
        ],
    },
    {
        path: "*",
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        loader: () => {
            throw json({}, { status: 404, statusText: "Page Not Found" });
        },
    },
]);

export default router;
