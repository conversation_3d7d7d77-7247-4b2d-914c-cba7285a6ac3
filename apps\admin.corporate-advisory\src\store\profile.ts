import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";

type ProfileState = Partial<UserProfileResponse>;

type ProfileAction = {
    setProfileValue: (
        key: keyof UserProfileResponse,
        value: number | string | boolean
    ) => void;
    setProfile: (user: UserProfileResponse) => void;
};

type ProfileStateAndAction = ProfileState & ProfileAction;

const baseProfileStore = create<ProfileStateAndAction>()(
    devtools(
        (set) => ({
            profileName: "",
            profileLastName: "",
            profileGivenName: "",
            profileAvatar: "",
            profileEmail: "",
            profileAddressUnit: "",
            profileAddressStreet: "",
            profileAddressCity: "",
            profileAddressState: "",
            profileAddressPostcode: "",
            profileAddressCountry: "",
            profileContact: "",
            userExperience: [],
            userProfessional: [],
            userSkill: [],
            permissions: [],
            setProfileValue(key, value) {
                set((state) => ({
                    ...state,
                    [key]: value,
                }));
            },

            setProfile(user) {
                set((state) => ({
                    ...state,
                    ...user,
                }));
            },
        }),
        {
            name: "user-store",
        }
    )
);

const useProfileStore = createSelectors(baseProfileStore);

export default useProfileStore;
