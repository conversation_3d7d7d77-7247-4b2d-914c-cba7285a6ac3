declare global {
    type TVisitorSearchParams = {
        keyword: string | null;
        status?: string | null;
        start_date?: string | null;
        end_date?: string | null;
    } & TPageQueryParams;

    type TVisitorApply = {
        id: number;
        invite_people: string;
        name: string;
        phone: string;
        visitor_date: string;
        visitor_time: string;
        leave_time: string;
        avatar: string;
        numbers: string;
        visitor_name_list: string;
        company: string;
        company_information: string;
        is_read_cna: number;
        visitor: string;
        matters: string;
        place: number;
        expect: string;
        is_book_meeting: number;
        book_detail: string;
        is_food: number;
        food_preference: string;
        status: number;
        reject_reason: string;
        create_date: string;
        create_time: string;
        remark: string;
        created_at: string;
        updated_at: string;
        attach?: string[] | null;
        invite_phone?: string;
        location?: string;
    };

    type TVisitorApplyResponse = {
        items: TVisitorApply[];
        paginate: BasePaginateResponse;
    };

    type TVisitorVip = {
        id: number;
        invite_people: string;
        name: string;
        phone: string;
        visitor_date: string;
        visitor_time: string;
        leave_time: string;
        avatar: string;
        numbers: string;
        visitor_name_list: string;
        company_information: string;
        is_read_cna: number;
        visitor: string;
        matters: string;
        place: number;
        expect: string;
        is_parking: number;
        cart_number: string;
        is_book_meeting: number;
        book_detail: string;
        is_food: number;
        food_preference: string;
        remark: string;
        created_at: string;
        updated_at: string;
        attach?: string[] | null;
        invite_phone?: string;
        location?: string;
        lang?: string;
    };

    type TVisitorsVipResponse = {
        items: TVisitorVip[];
        paginate: BasePaginateResponse;
    };

    type TVisitorReport = {
        id: number;
        visitor_id: number;
        report_author: string;
        visitor_date: string;
        visitor_time: string;
        visitor_purpose: string;
        expect: string;
        content: string;
        developments: string;
        result: string;
        follow_content: string;
        remark: string;
        type: number; // 1: 来访登记 2: 贵宾登记
        created_at: string;
        updated_at: string;
        attach?: string[] | null;
    };

    type TVisitorReportResponse = {
        items: TVisitorReport[];
        paginate: BasePaginateResponse;
    };
}

export {};
