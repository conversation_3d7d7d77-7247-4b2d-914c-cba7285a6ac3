import api from "@/apis";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Modal, Stack, TextInput, Textarea } from "@mantine/core";
import React, { useCallback, useState, useEffect } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm, Controller } from "react-hook-form";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";

const structureSchema = z.object({
    structureCode: z.string().min(1, "请输入结构码"),
    structureEN: z.string().min(1, "请输入结构英文"),
    structureMS: z.string().min(1, "请输入结构马来文"),
    structureZH: z.string().min(1, "请输入结构中文"),
    structureZT: z.string().min(1, "请输入结构繁体"),
});

type StructureForm = z.infer<typeof structureSchema>;

interface StructureInfoProps {
    onUpdateSuccess?: () => void;
    onCloseSuccess?: () => void;
}

const StructureInfo: React.FC<StructureInfoProps> = React.memo(
    ({ onUpdateSuccess = () => {}, onCloseSuccess = () => {} }) => {
        const { lang } = useSettingStore();

        const [loading, setLoading] = useState(false);

        const modalParams = useModalStore((state) => state.modalParams.webStructureModal) || {};
        const { row } = modalParams;

        const openConfirm = useModalStore.use.openConfirm();

        const { isVisible, close } = useModalStore(
            useShallow((state) => ({
                isVisible: state.webStructureModal,
                close: state.close,
            }))
        );

        useEffect(() => {
            if (isVisible === false) {
                reset();
            }
        }, [isVisible]);

        const closeModal = useMemoizedFn(() => {
            close("webStructureModal");
            onCloseSuccess();
        });

        const {
            control,
            handleSubmit,
            formState: { errors },
            reset,
        } = useForm<StructureForm>({
            defaultValues: {
                structureCode: "",
                structureEN: "",
                structureMS: "",
                structureZH: "",
                structureZT: "",
            },
            resolver: zodResolver(structureSchema),
        });

        useEffect(() => {
            if (row) {
                reset({
                    structureCode: row.structureCode,
                    structureEN: row.structureEN,
                    structureMS: row.structureMS,
                    structureZH: row.structureZH,
                    structureZT: row.structureZT,
                });
            } else {
                reset({
                    structureCode: "",
                    structureEN: "",
                    structureMS: "",
                    structureZH: "",
                    structureZT: "",
                });
            }
        }, [row, reset]);

        const submitForm: SubmitHandler<StructureForm> = useCallback(
            async (data) => {
                setLoading(true);
                try {
                    const payload = {
                        structureCode: data.structureCode,
                        structureEN: data.structureEN,
                        structureMS: data.structureMS,
                        structureZH: data.structureZH,
                        structureZT: data.structureZT,
                    };

                    let res;
                    if (row) {
                        res = await api.web.structureUpdate(payload, row.structureID);
                    } else {
                        res = await api.web.structureStore(payload);
                    }

                    if (res) {
                        noty.success("操作成功");
                        onUpdateSuccess();
                        closeModal();
                    }
                } catch (error) {
                    noty.error("操作失败，请重试");
                } finally {
                    setLoading(false);
                }
            },
            [row, onUpdateSuccess, closeModal]
        );

        const handleSave = useCallback(() => {
            openConfirm({
                title: "提示",
                message: "您确定此操作么？",
                onConfirm: handleSubmit(submitForm),
            });
        }, [openConfirm, handleSubmit, submitForm]);

        return (
            <Modal
                opened={isVisible}
                onClose={closeModal}
                title="简介信息"
                size="xl"
            >
                <form onSubmit={handleSubmit(handleSave, (errors) => console.log(errors))}>
                    <Stack gap="lg">
                        <Controller
                            name="structureCode"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="结构码"
                                    placeholder="请输入结构码"
                                    error={errors.structureCode?.message}
                                />
                            )}
                        />
                        <Controller
                            name="structureEN"
                            control={control}
                            render={({ field }) => (
                                <Textarea
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="结构英文"
                                    placeholder="请输入结构英文"
                                    error={errors.structureEN?.message}
                                    minRows={2}
                                    autosize
                                />
                            )}
                        />
                        <Controller
                            name="structureMS"
                            control={control}
                            render={({ field }) => (
                                <Textarea
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="结构马来文"
                                    placeholder="请输入结构马来文"
                                    error={errors.structureMS?.message}
                                    minRows={2}
                                    autosize
                                />
                            )}
                        />
                        <Controller
                            name="structureZH"
                            control={control}
                            render={({ field }) => (
                                <Textarea
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="结构中文"
                                    placeholder="请输入结构中文"
                                    error={errors.structureZH?.message}
                                    minRows={2}
                                    autosize
                                />
                            )}
                        />
                        <Controller
                            name="structureZT"
                            control={control}
                            render={({ field }) => (
                                <Textarea
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="结构繁体"
                                    placeholder="请输入结构繁体"
                                    error={errors.structureZT?.message}
                                    minRows={2}
                                    autosize
                                />
                            )}
                        />

                        <ModalFooter
                            buttons={[
                                {
                                    key: "update",
                                    label: "保存",
                                    leftSection: <Check size={18} />,
                                    loading: loading,
                                    type: "submit",
                                },
                                {
                                    key: "close",
                                    label: "关闭",
                                    style: "outline",
                                    leftSection: <X size={18} />,
                                    onClick: closeModal,
                                },
                            ]}
                        />
                    </Stack>
                </form>
            </Modal>
        );
    }
);

export default StructureInfo;
