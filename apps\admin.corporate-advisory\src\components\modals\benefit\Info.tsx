import useModalStore from "@/store/modal";
import React, { useState, useEffect, useMemo } from "react";
import { Modal, Stack, TextInput, SimpleGrid, Textarea } from "@mantine/core";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Check, X } from "@phosphor-icons/react";
import { CnaButton } from "@code.8cent/react/components";
import { useShallow } from "zustand/react/shallow";
import ModalFooter from "@/components/common/ModalFooter";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import dayjs from "dayjs";

interface BenefitInfoProps {
    benefit: TBenefit | null;
    onClose: () => void;
    onUpdateSuccess: () => void;
}

type LanguageKey = "ZH" | "ZT" | "EN" | "MS";

const LanguageButton = ({ title, active, onClick }) => (
    <CnaButton
        size="sm"
        variant={active ? "filled" : "default"}
        onClick={onClick}
    >
        {title}
    </CnaButton>
);

// 添加验证 schema
const benefitSchema = z.object({
    ZH: z.object({
        title: z.string().min(1, "请输入简体中文标题"),
        content: z.string().min(1, "请输入简体中文详情"),
    }),
    ZT: z.object({
        title: z.string().min(1, "请输入繁体中文标题"),
        content: z.string().min(1, "请输入繁体中文详情"),
    }),
    EN: z.object({
        title: z.string().min(1, "请输入英文标题"),
        content: z.string().min(1, "请输入英文详情"),
    }),
    MS: z.object({
        title: z.string().min(1, "请输入马来文标题"),
        content: z.string().min(1, "请输入马来文详情"),
    }),
});

const Info = React.memo(({ benefit, onClose, onUpdateSuccess }: BenefitInfoProps) => {
    const lang = useSettingStore.use.lang();
    const openConfirm = useModalStore.use.openConfirm();
    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.benefitInfoModal,
            close: state.close,
        }))
    );

    // 初始化数据
    const initialBenefitData = useMemo(
        () => ({
            ZH: {
                title: benefit?.benefitTitleZH || "",
                content: benefit?.benefitDescriptionZH || "",
            },
            ZT: {
                title: benefit?.benefitTitleZT || "",
                content: benefit?.benefitDescriptionZT || "",
            },
            EN: {
                title: benefit?.benefitTitleEN || "",
                content: benefit?.benefitDescriptionEN || "",
            },
            MS: {
                title: benefit?.benefitTitleMS || "",
                content: benefit?.benefitDescriptionMS || "",
            },
        }),
        [benefit]
    );

    const [activeLanguage, setActiveLanguage] = useState<LanguageKey>("ZH");

    const {
        register,
        handleSubmit,
        setValue,
        formState: { errors },
        reset,
        watch,
    } = useForm({
        resolver: zodResolver(benefitSchema),
        defaultValues: initialBenefitData,
    });

    useEffect(() => {
        reset(initialBenefitData);
        setActiveLanguage("ZH");
    }, [benefit, initialBenefitData, reset]);

    const onSubmit = async (formData) => {
        try {
            const payload = {
                benefitTitleZH: formData.ZH.title,
                benefitTitleZT: formData.ZT.title,
                benefitTitleEN: formData.EN.title,
                benefitTitleMS: formData.MS.title,
                benefitDescriptionZH: formData.ZH.content,
                benefitDescriptionZT: formData.ZT.content,
                benefitDescriptionEN: formData.EN.content,
                benefitDescriptionMS: formData.MS.content,
            };

            let res = false;
            if (benefit) {
                res = await api.benefit.update(payload, benefit.benefitID);
            } else {
                res = await api.benefit.store(payload);
            }

            if (res) {
                noty.success("保存成功");
                onUpdateSuccess();
                close("benefitInfoModal");
            }
        } catch (error) {
            noty.error("保存失败");
            console.error("保存失败:", error);
        }
    };

    const handleLanguageButtonClick = (key) => setActiveLanguage(key);

    const handleSave = () => {
        openConfirm({
            title: "更新福利内容",
            message: "您确定此操作么？",
            onConfirm: handleSubmit(onSubmit),
        });
    };

    const closeModal = () => {
        onClose();
        close("benefitInfoModal");
    };

    const modalFooterButtons = [
        {
            key: "save",
            label: "保存",
            leftSection: <Check size={16} />,
            onClick: handleSave,
        },
        {
            key: "close",
            label: "关闭",
            style: "outline",
            leftSection: <X size={16} />,
            onClick: () => closeModal(),
        },
    ];

    const languageButtons = [
        { key: "ZH", title: "中文 (简体)" },
        { key: "ZT", title: "中文 (繁体)" },
        { key: "EN", title: "英文" },
        { key: "MS", title: "马来文" },
    ].map((item) => (
        <LanguageButton
            key={item.key}
            title={item.title}
            active={item.key === activeLanguage}
            onClick={() => handleLanguageButtonClick(item.key)}
        />
    ));

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            title="创建福利"
            size="xl"
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack gap="md">
                    <SimpleGrid cols={4}>{languageButtons}</SimpleGrid>

                    <TextInput
                        label="福利标题"
                        labelProps={{
                            className: "profile-form-label",
                        }}
                        placeholder="请输入福利标题"
                        {...register(`${activeLanguage}.title` as const)}
                        value={watch(`${activeLanguage}.title`)}
                        onChange={(e) =>
                            setValue(`${activeLanguage}.title` as const, e.target.value)
                        }
                        error={errors[activeLanguage]?.title?.message}
                    />

                    <Textarea
                        label="福利详情"
                        labelProps={{
                            className: "profile-form-label",
                        }}
                        placeholder="输入福利详情"
                        autosize
                        minRows={4}
                        {...register(`${activeLanguage}.content` as const)}
                        value={watch(`${activeLanguage}.content`)}
                        onChange={(e) =>
                            setValue(`${activeLanguage}.content` as const, e.target.value)
                        }
                        error={errors[activeLanguage]?.content?.message}
                    />

                    <ModalFooter
                        timelineContent={
                            (benefit?.edit_user_info &&
                                `最近修改: ${benefit.edit_user_info.profileName} (${dayjs(
                                    benefit.editTime
                                ).format("YYYY-MM-DD HH:mm:ss")})`) ||
                            (benefit?.create_user_info &&
                                `创建: ${benefit.create_user_info.profileName} (${dayjs(
                                    benefit.createTime
                                ).format("YYYY-MM-DD HH:mm:ss")})`)
                        }
                        buttons={modalFooterButtons}
                    />
                </Stack>
            </form>
        </Modal>
    );
});

export default Info;
