import { Table, TextInput, Select, Group, NumberInput } from "@mantine/core";
import { DatePickerInput, DateTimePicker } from "@mantine/dates";

export function ColumnFilters({
    table,
    filterTypes,
    filterOptions,
    localColumnFilters,
    setLocalColumnFilters,
    enableMultiSelect,
}) {
    return (
        <Table.Tr>
            {enableMultiSelect && <Table.Th />}
            {table.getAllLeafColumns().map(
                (column) =>
                    column.getIsVisible() && (
                        <Table.Th key={column.id}>
                            {filterTypes[column.id] === "text" && (
                                <TextInput
                                    size="xs"
                                    placeholder={`筛选 ${column.columnDef.header?.toString()}`}
                                    value={localColumnFilters[column.id] || ""}
                                    onChange={(e) => {
                                        const newValue = e.target.value;
                                        setLocalColumnFilters((prev) => ({
                                            ...prev,
                                            [column.id]: newValue === "" ? undefined : newValue,
                                        }));
                                    }}
                                />
                            )}
                            {filterTypes[column.id] === "select" && (
                                <Select
                                    size="xs"
                                    placeholder={`选择 ${column.columnDef.header?.toString()}`}
                                    data={filterOptions[column.id] || []}
                                    value={localColumnFilters[column.id] || ""}
                                    onChange={(value) => {
                                        setLocalColumnFilters((prev) => ({
                                            ...prev,
                                            [column.id]: value === "" ? undefined : value,
                                        }));
                                    }}
                                    clearable
                                    searchable
                                />
                            )}
                            {filterTypes[column.id] === "numberRange" && (
                                <Group
                                    gap="xs"
                                    grow
                                >
                                    <NumberInput
                                        size="xs"
                                        placeholder="最小值"
                                        value={(localColumnFilters[column.id] || [])[0] ?? ""}
                                        onChange={(value) =>
                                            setLocalColumnFilters((prev) => ({
                                                ...prev,
                                                [column.id]: [value, (prev[column.id] || [])[1]],
                                            }))
                                        }
                                        hideControls
                                    />
                                    <NumberInput
                                        size="xs"
                                        placeholder="最大值"
                                        value={(localColumnFilters[column.id] || [])[1] ?? ""}
                                        onChange={(value) =>
                                            setLocalColumnFilters((prev) => ({
                                                ...prev,
                                                [column.id]: [(prev[column.id] || [])[0], value],
                                            }))
                                        }
                                        hideControls
                                    />
                                </Group>
                            )}
                            {filterTypes[column.id] === "dateRange" && (
                                <DatePickerInput
                                    type="range"
                                    allowSingleDateInRange
                                    size="xs"
                                    clearable
                                    placeholder="选择日期范围"
                                    valueFormat="YYYY-MM-DD"
                                    value={localColumnFilters[column.id] || [null, null]}
                                    onChange={(value) => {
                                        setLocalColumnFilters((prev) => ({
                                            ...prev,
                                            [column.id]: value || undefined,
                                        }));
                                    }}
                                />
                            )}
                            {filterTypes[column.id] === "datetimeRange" && (
                                <Group
                                    gap="xs"
                                    grow
                                >
                                    <DateTimePicker
                                        size="xs"
                                        clearable
                                        placeholder="选择开始时间"
                                        valueFormat="YYYY-MM-DD HH:mm:ss"
                                        withSeconds
                                        value={(localColumnFilters[column.id] || [])[0] || null}
                                        onChange={(value) =>
                                            setLocalColumnFilters((prev) => ({
                                                ...prev,
                                                [column.id]: [value, (prev[column.id] || [])[1]],
                                            }))
                                        }
                                    />
                                    <DateTimePicker
                                        size="xs"
                                        clearable
                                        placeholder="选择结束时间"
                                        valueFormat="YYYY-MM-DD HH:mm:ss"
                                        withSeconds
                                        value={(localColumnFilters[column.id] || [])[1] || null}
                                        onChange={(value) =>
                                            setLocalColumnFilters((prev) => ({
                                                ...prev,
                                                [column.id]: [(prev[column.id] || [])[0], value],
                                            }))
                                        }
                                    />
                                </Group>
                            )}
                        </Table.Th>
                    )
            )}
        </Table.Tr>
    );
}
