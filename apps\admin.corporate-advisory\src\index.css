@layer tailwind {
    @tailwind base;
}
@tailwind components;
@tailwind utilities;

@media print {
    *{
        overflow: visible !important;
    }
}

.custom-tabs {
    gap: 0;
    position: relative;
    border-bottom: 2px solid;
    @apply !tw-border-b-gray-300;
}

.custom-tabs .nav-link {
    padding: 0.5rem 1rem;
    margin-bottom: -2px;
    border-bottom: 2px solid transparent;
    @apply !tw-text-gray-500;
    @apply !tw-font-normal;
    @apply hover:!tw-border-b-gray-800;
    @apply hover:!tw-text-gray-800;
}

.custom-tabs .nav-link.active {
    @apply !tw-border-b-gray-800;
    @apply !tw-text-gray-800;
}

.table-fixed-header {
    overflow-y: auto;
}

.table-fixed-header thead th {
    position: sticky;
    top: 0;
    background: #f8f9fa; /* Background color for the header */
    z-index: 1;
}


.account-form-label{
    @apply tw-text-[16px] tw-font-normal md:tw-mb-1 tw-mb-0;
}

.profile-update-password-btn{
    @apply tw-border tw-bg-transparent tw-border-neutral-300 tw-text-black tw-font-normal;
    @apply hover:tw-border hover:!tw-bg-transparent hover:tw-border-neutral-300 hover:tw-text-black hover:tw-font-normal;
}

.mantine-Button-root{
    @apply tw-font-normal;
}

.profile-form-label{
    @apply tw-mb-2 tw-w-full tw-bg-basic-5 tw-text-gray-50 tw-py-1 tw-px-2;
}

/* mantine 富文本编辑器 去除焦点框线 */
.mantine-RichTextEditor-root .ProseMirror:focus {
    outline: none !important;
    border-color: transparent !important;
    box-shadow: none !important;
}
