import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const refund = {
    // 退款列表
    list: async (params: TRefundSearchParams) => {
        const { error, result } = await cnaRequest<TRefundResponse>(
            `/api/v1/admin/refund/list`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 退款审核
    examine: async (params: TRefundExamineParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/refund/examine`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default refund;
