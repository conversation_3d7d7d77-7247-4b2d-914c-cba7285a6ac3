import api from "@/apis";
import { Group, Stack } from "@mantine/core";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import dayjs from "dayjs";
import CnaAdminButton from "@/components/common/CnaAdminButton";
import { useMemoizedFn, useRequest } from "ahooks";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import GspDetail from "@/components/modals/gsp/Detail";
import GspReview from "@/components/modals/gsp/Review";
import GspReportFilesV2 from "@/components/modals/gsp/ReportFilesV2";

const columnHelper = createColumnHelper<TGspApplication>();

const STATUS_MAP = {
    0: "未上传预审表格",
    1: "提交预审表格",
    2: "预审通过",
    3: "预审失败",
    4: "上传签署合同",
    5: "审核合同通过",
    6: "审核合同失败",
    7: "提交尽调报告",
    8: "合伙人核对通过",
    9: "合伙人核对不通过",
    10: "材料审核通过",
    11: "材料审核失败",
    12: "尽调审核通过",
    13: "尽调审核不通过",
    14: "开通",
    15: "管理合伙人审核通过",
};

// todo 各步骤付款状态
// pay_one 付款申请费和预审保证金(0未付款;1已付款)
// pay_two 已付款企业开户费(0未付款;1已付款)
// pay_three 已付款AI账号开通费(0未付款;1已付款)
// pay_one_return 第一步退款申请(0未申请;1已申请)

const PAY_STATUS_MAP = {
    0: "未付款",
    1: "已付款",
};

const AdminGspApplicationsPage = () => {
    const lang = useSettingStore.use.lang();

    const openModal = useModalStore.use.open();
    const openUpload = useModalStore.use.openUpload();
    const openConfirm = useModalStore.use.openConfirm();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [selectOptions, setSelectOptions] = useState({
        economics: [],
        industries: [],
        countries: [],
    });

    const { run: getSelectOptions } = useRequest(async () => {
        const { result } = await cnaRequest(
            "/api/v1/admin/gsp/select-options",
            "GET"
        );

        if (result) {
            setSelectOptions(result.data);
        }
    });

    // 查看预审详情
    // 审核预审（通过、驳回）
    // 审核合同（通过，驳回）
    // 下载尽调材料
    // 生成尽调报告
    // 尽调报告（查看、下载）
    // 审核尽调（通过、驳回）
    const rowActions = (row) => [
        {
            key: "app-detail",
            label: "查看详情",
            onClick: () => openModal("gspApplicationModal", { gspApplication: row, selectOptions }),
        },
        {
            key: "review-audit",
            label: "审核预审",
            disabled: row.status !== 1,
            onClick: () => openModal("gspReviewModal", { gspApplication: row, type: "form" }),
        },
        {
            key: "review-contract",
            label: "审核合同",
            disabled: row.status !== 4,
            onClick: () => openModal("gspReviewModal", { gspApplication: row, type: "contract" }),
        },
        {
            key: "download-materials",
            label: "下载尽调材料",
            disabled: row.status < 7,
            onClick: () => confirmDownload(row),
        },
        {
            key: "review-report-files",
            label: "审核尽调文件",
            disabled: row.status !== 15,
            onClick: () => openModal("gspReportFilesModal", { gspApplication: row }),
        },
        // {
        //     key: "generate-report",
        //     label: "生成尽调",
        //     disabled: true, // 暂未实现
        //     onClick: () => {},
        // },
        {
            key: "reports",
            label: "尽调报告",
            disabled: true, // 暂未实现
            onClick: () => {},
        },
        {
            key: "review-report",
            label: "审核尽调",
            disabled: row.status !== 15,
            onClick: () => openModal("gspReviewModal", { gspApplication: row, type: "report" }),
        },
        {
            key: "recheck",
            label: "复审报告",
            disabled: ![10, 12, 13, 14].includes(row.status),
            onClick: () => openModal("gspReviewModal", { gspApplication: row, type: "reCheck" }),
        },
    ];

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "序号",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profile.profileName", {
            header: "合伙人姓名",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("name", {
            header: "名称",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("credit_code", {
            header: "统一社会信用代码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("contact_name", {
            header: "联系人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("contact_position", {
            header: "联系人职位",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("desc", {
            header: "描述",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("phone", {
            header: "联系电话",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("email", {
            header: "邮箱",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("status", {
            header: "状态",
            enableSorting: false,
            cell: (info) => STATUS_MAP[info.getValue()],
        }),
        columnHelper.accessor("pay_one", {
            header: "预审费和保证金",
            enableSorting: false,
            cell: (info) => PAY_STATUS_MAP[info.getValue()],
        }),
        columnHelper.accessor("pay_two", {
            header: "企业开户费",
            enableSorting: false,
            cell: (info) => PAY_STATUS_MAP[info.getValue()],
        }),
        columnHelper.accessor("pay_three", {
            header: "AI账号开通费",
            enableSorting: false,
            cell: (info) => PAY_STATUS_MAP[info.getValue()],
        }),
        columnHelper.accessor("created_at", {
            header: "创建时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("updated_at", {
            header: "更新时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            size: 200,
            cell: (info) => {
                // 查看预审详情
                // 审核预审（通过、驳回）
                // 下载尽调材料
                // 生成尽调报告
                // 尽调报告（查看、下载）
                // 审核尽调（通过、驳回）
                const row = info.row.original;
                return (
                    <Group>
                        {/* 显示当前状态的对应操作 */}
                        {row.status < 2 && (
                            <CnaAdminButton
                                size="xs"
                                variant="outline"
                                onClick={() =>
                                    openModal("gspApplicationModal", {
                                        gspApplication: row,
                                        selectOptions,
                                    })
                                }
                            >
                                查看详情
                            </CnaAdminButton>
                        )}
                        {row.status === 1 && (
                            <CnaAdminButton
                                size="xs"
                                variant="outline"
                                onClick={() =>
                                    openModal("gspReviewModal", {
                                        gspApplication: row,
                                        type: "form",
                                    })
                                }
                            >
                                审核预审
                            </CnaAdminButton>
                        )}
                        {row.status === 4 && (
                            <CnaAdminButton
                                size="xs"
                                variant="outline"
                                onClick={() =>
                                    openModal("gspReviewModal", {
                                        gspApplication: row,
                                        type: "contract",
                                    })
                                }
                            >
                                审核合同
                            </CnaAdminButton>
                        )}
                        {row.status === 8 && (
                            <CnaAdminButton
                                size="xs"
                                variant="outline"
                                onClick={() =>
                                    openModal("gspReviewModal", {
                                        gspApplication: row,
                                        type: "report",
                                    })
                                }
                            >
                                审核尽调
                            </CnaAdminButton>
                        )}
                        <TableRowDropActionMenu items={rowActions(info.row.original)} />
                    </Group>
                );
            },
        }),
    ];

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                status: globalFilters?.status || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.gsp.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    // 处理下载打包的 zip 文件
    const handleDownloadZip = useMemoizedFn(async (row) => {
        setLoading(true);
        // 获取服务端的 zip 文件路径
        const { result } = await cnaRequest(`/api/v1/admin/gsp/zip`, "GET", {
            id: row.id,
        });
        const fileBasename = result.data.split("/").pop();

        // 下载 zip 文件
        try {
            const response = await cnaRequest(
                "/api/v1/admin/digital-humans/download-file",
                "POST",
                { file_path: fileBasename, type: "local" },
                // 指定响应类型为 Blob
                { responseType: "blob" }
            );

            // 获取文件内容（Blob）
            const blob = new Blob([response.result?.data]);

            // 创建一个临时链接
            const url = window.URL.createObjectURL(blob);

            // 创建一个 <a> 标签并触发下载
            const a = document.createElement("a");
            a.href = url;
            // 使用文件路径的最后一部分作为文件名
            a.download = fileBasename || "download";
            document.body.appendChild(a);
            a.click();

            // 清理临时链接
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error("下载失败:", error);
            noty.error("下载失败，请稍后重试");
        } finally {
            setLoading(false);
        }
    });

    const confirmDownload = useMemoizedFn((row) => {
        openConfirm({
            title: "确定下载此文件吗？",
            message: "下载后文件将保存在本地，请及时保存",
            onConfirm: () => handleDownloadZip(row),
        });
    });

    const refreshTable = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="绿智地球"
                desc="查询绿智地球申请列表"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                    {
                        field: "status",
                        label: "状态",
                        type: "select",
                        options: Object.entries(STATUS_MAP).map(([key, value]) => ({
                            label: value,
                            value: key,
                        })),
                    },
                ]}
            />

            {/* 申请详情 */}
            <GspDetail />
            {/* 审核详情 */}
            <GspReview onSubmitSuccess={refreshTable} />
            {/* 审核尽调报告文件V2 */}
            <GspReportFilesV2 />
        </Stack>
    );
};

export default AdminGspApplicationsPage;
