import { t } from "@code.8cent/i18n";
import {
    AddressSelect,
    CnaButton,
    CountrySelect,
    ProfileAvatar,
} from "@code.8cent/react/components";
import useDataStore from "@code.8cent/store/data";
import useSettingStore from "@code.8cent/store/setting";
import {
    ActionIcon,
    Box,
    Grid,
    Group,
    Input,
    Modal,
    Stack,
    TextInput,
} from "@mantine/core";
import ProfileUserData from "@/components/profile/ProfileUserData";
import { PaperPlaneTilt } from "@phosphor-icons/react";

const ProfileInfoModal = ({
    profile = {},
    opened = false,
    onClose = () => {},
}: {
    profile?: Partial<MemberListResponse["items"][number]>;
    opened?: boolean;
    onClose?: () => void;
}) => {
    const { lang } = useSettingStore();

    const { countryDatas } = useDataStore();

    return (
        <Modal
            opened={opened}
            closeOnClickOutside={false}
            onClose={onClose}
            title={"合伙人资料"}
            size="xl"
        >
            <Stack gap={"lg"}>
                <Group justify="center">
                    {profile.profileAvatar && profile.profileAvatar !== "" && (
                        <ProfileAvatar
                            style={{
                                width: 120,
                            }}
                            src={`${window.api_base_url}${profile.profileAvatar}`}
                        />
                    )}
                </Group>
                <Grid>
                    <Grid.Col span={12}>
                        <TextInput
                            label={t("introduction.label.given_name", lang)}
                            labelProps={{ className: "profile-form-label" }}
                            value={profile.profileName ?? ""}
                            readOnly
                        />
                    </Grid.Col>
                </Grid>
                <Grid>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                        <CountrySelect
                            readOnly
                            label={t("introduction.nationality", lang)}
                            labelProps={{ className: "profile-form-label" }}
                            value={String(profile.profileNationalityID)}
                            {...{
                                data: countryDatas,
                                flagKey: "countryISOCode2",
                                labelKey:
                                    `country${lang}` as keyof CountryDataItem,
                                valueKey: "countryID",
                            }}
                        />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                        <TextInput
                            label={t("introduction.label.email", lang)}
                            labelProps={{ className: "profile-form-label" }}
                            value={profile.profileEmail}
                            rightSection={
                                <ActionIcon
                                    component="a"
                                    href={`mailto:${profile.profileEmail}`}
                                    variant="transparent"
                                    color="basic"
                                    target="_blank"
                                >
                                    <PaperPlaneTilt
                                        color="var(--cna-color-primary)"
                                        weight="bold"
                                    />
                                </ActionIcon>
                            }
                            readOnly
                        />
                    </Grid.Col>
                </Grid>
                <Grid>
                    <Grid.Col span={12}>
                        <AddressSelect<typeof profile, CountryDataItem>
                            label={t("introduction.label.address", lang)}
                            wrapperProps={{
                                className: "tw-mb-2",
                                labelProps: {
                                    className: "profile-form-label",
                                },
                            }}
                            addressFieldMap={{
                                unit: {
                                    key: "profileAddressUnit",
                                    placeholder: t(
                                        "introduction.label.unit",
                                        lang
                                    ),
                                },
                                street: {
                                    key: "profileAddressStreet",
                                    placeholder: t(
                                        "introduction.label.street",
                                        lang
                                    ),
                                },
                                district: {
                                    key: "profileAddressDistrictId",
                                    placeholder: t(
                                        "introduction.label.district",
                                        lang
                                    ),
                                },
                                city: {
                                    key: "profileAddressCityId",
                                    placeholder: t(
                                        "introduction.label.city",
                                        lang
                                    ),
                                },
                                state: {
                                    key: "profileAddressStateId",
                                    placeholder: t(
                                        "introduction.label.state",
                                        lang
                                    ),
                                },
                                postcode: {
                                    key: "profileAddressPostcode",
                                    placeholder: t(
                                        "introduction.label.postcode",
                                        lang
                                    ),
                                },
                                country: {
                                    key: "profileAddressCountry",
                                    placeholder: t(
                                        "introduction.label.country",
                                        lang
                                    ),
                                },
                            }}
                            countrySelectProps={{
                                data: countryDatas,
                                flagKey: "countryISOCode2",
                                labelKey:
                                    `country${lang}` as keyof CountryDataItem,
                                valueKey: "countryID",
                            }}
                            addressData={profile}
                            readOnly
                        />
                    </Grid.Col>
                </Grid>
                <Grid>
                    <Grid.Col span={12}>
                        <Input.Wrapper
                            label={"客户资源"}
                            labelProps={{ className: "profile-form-label" }}
                        >
                            <Box>
                                {profile?.company?.map?.((company, _idx) => (
                                    <TextInput
                                        key={_idx}
                                        value={company.companyName}
                                        readOnly
                                        className="tw-mb-3"
                                    />
                                ))}
                            </Box>
                        </Input.Wrapper>
                    </Grid.Col>
                </Grid>
                <Grid>
                    <Grid.Col span={12}>
                        <ProfileUserData
                            dataKey="professional"
                            value={profile.userProfessional}
                            readOnly
                        />
                    </Grid.Col>
                </Grid>
                <Grid>
                    <Grid.Col span={12}>
                        <ProfileUserData
                            dataKey="skill"
                            value={profile.userSkill}
                            readOnly
                        />
                    </Grid.Col>
                </Grid>
                <Group justify="end">
                    <CnaButton color="basic" onClick={onClose}>
                        {t("common.close", lang)}
                    </CnaButton>
                </Group>
            </Stack>
        </Modal>
    );
};

export default ProfileInfoModal;
