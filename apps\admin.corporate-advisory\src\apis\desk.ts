import noty from "@code.8cent/react/noty";
import { cnaRequest } from "@code.8cent/utils";

export default {
    // 合伙人相关接口
    associate: {
        // 获取合伙人列表
        list: async (params: TAssociateSearchParams) => {
            const { error, result } = await cnaRequest<AssociatesResponse>(
                `/api/v1/admin/partner/list`,
                "GET",
                params
            );

            if (!error) {
                return result.data;
            } else {
                noty.error(error.message);
                return null;
            }
        },
        // 获取合伙人选项
        options: async () => {
            const { error, result } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/partner/options`,
                "GET"
            );

            if (!error) {
                return result;
            } else {
                noty.error(error.message);
                return null;
            }
        },
        // 更新合伙人设置
        updateSetting: async (params: TUpdateSettingParams, id: number) => {
            const { error } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/partner/setting/${id}`,
                "POST",
                params
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        // 设置三三制总
        setTeamLeader: async (id: number, industry_name: string, market_code: string) => {
            const { error } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/partner/setTeamTop`,
                "POST",
                {
                    id,
                    industry_name, // 行业名
                    market_code, // 市场标识符
                }
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        // 审核合伙人
        review: async (params: TReviewAssociateParams) => {
            const { error, result } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/partner/check`,
                "POST",
                params
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        // 获取合伙人详情
        info: async (id: number) => {
            const { error, result } = await cnaRequest<UserProfileResponse>(
                `/api/v1/admin/partner/info/${id}`,
                "GET"
            );

            if (!error) {
                return result.data;
            } else {
                noty.error(error.message);
                return null;
            }
        },
        // 标记合伙人审核状态
        markReviewed: async (id: number, type: "info_status" | "register_payment_status") => {
            const { error } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/partner/mark-reviewed`,
                "POST",
                {
                    id,
                    type,
                }
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        // 标记可以申请联号事务所
        setCanApplyFirm: async (id: number) => {
            const { error } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/affiliatedFirm/point_user/${id}`,
                "POST"
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
    },
    // 文件相关接口
    file: {
        // 获取文件资源
        getResource: (params) => cnaRequest("/api/v1/admin/file/resource", "GET", params),
    },
    // 备注相关接口
    remark: {
        // 添加备注
        store: async (params: TStoreRemarkParams) => {
            const { error } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/remark/create`,
                "POST",
                params
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
    },
};
