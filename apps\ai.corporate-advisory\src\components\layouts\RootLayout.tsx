import { Outlet, useLocation, useNavigate } from "react-router-dom";
import ConfirmModal from "../modals/ConfirmModal";
import { useLockFn, useMemoizedFn, useMount, useNetwork, useUnmount } from "ahooks";
import { useEffect, useState } from "react";
import api from "@/apis";
import useDataStore from "@code.8cent/store/data";
import { Center, Group, Stack, Text, Image } from "@mantine/core";
import { Spinner } from "@phosphor-icons/react";
import useSettingStore from "@code.8cent/store/setting";
import Offline from "@code.8cent/react/components/Offline";
import Bowser from "bowser";

const RootLayout = () => {
    const [inited, setInited] = useState<boolean>(false);

    const { online } = useNetwork();

    const {
        setLanguages,
        setCurrencies,
        setTimezones,
        setTimeFormats,
        setDateFormats,
        setLanguageStructures,
        setCountryDatas,
    } = useDataStore();

    const { updateSetting } = useSettingStore();

    const loadFromCache = useLockFn(async () => {
        const cachedOptions = await window.localForage.getItem<SettingConfigOptionResponse>(
            "configOptions"
        );

        const cachedDicts = await window.localForage.getItem<LanguageStructure[]>(
            "languageStructure"
        );

        const cachedCountryDatas = await window.localForage.getItem<CountryDataItem[]>(
            "countryDatas"
        );

        if (cachedOptions && cachedDicts && cachedCountryDatas) {
            console.log("has cached");
            // 使用缓存数据并立即设置inited为true
            setLanguageStructures(cachedDicts ?? []);
            setLanguages(cachedOptions.languageList ?? []);
            setCurrencies(cachedOptions.currencyList ?? []);
            setTimezones(cachedOptions.timezoneList ?? []);
            setTimeFormats(cachedOptions.timeFormatList ?? []);
            setDateFormats(cachedOptions.dateFormatList ?? []);
            setCountryDatas(cachedCountryDatas ?? []);
            updateSetting("countryID", cachedOptions.countryID);
            setInited(true); // 缓存数据存在，立即初始化
        }
    });

    const fetchDataAndUpdateCache = useLockFn(async () => {
        try {
            // 从API获取最新数据
            const options = await api.config.getConfigOptions();
            const dicts = await api.config.getLanguageStructure();
            const countries = await api.config.getCountryDatas();

            // 更新store和缓存
            if (options) {
                setLanguages(options.languageList ?? []);
                setCurrencies(options.currencyList ?? []);
                setTimezones(options.timezoneList ?? []);
                setTimeFormats(options.timeFormatList ?? []);
                setDateFormats(options.dateFormatList ?? []);
                updateSetting("countryID", options.countryID);
                await window.localForage.setItem("configOptions", options); // 缓存更新
            }

            if (dicts?.length) {
                setLanguageStructures(dicts ?? []);
                await window.localForage.setItem("languageStructure", dicts); // 缓存语言结构数据
            }

            if (countries.length) {
                setCountryDatas(countries ?? []);
                await window.localForage.setItem("countryDatas", countries);
            }

            setInited(true);
        } catch (error) {
            console.error("Error fetching config:", error);
            // 处理错误情况，如提示用户网络问题等
        }
    });

    useMount(() => {
        api.test();

        const browser = Bowser.getParser(window.navigator.userAgent);

        if (browser.getBrowserName() === "WeChat") {
            updateSetting("isWechat", true);
        };

        loadFromCache().finally(() => {
            fetchDataAndUpdateCache();
        });
    });

    if (online === false) {
        return <Offline className="tw-h-screen tw-w-screen" />;
    }

    return inited === true ? (
        <div>
            {/* <Image
                    className="sm:tw-absolute tw-hidden sm:tw-block tw-z-10 tw-left-10 tw-top-6 tw-w-5"
                    src="/images/C&A-Logo-Icon-White.svg"
                    alt=""
                    w={80}
                />
             <Image
                    className="sm:tw-absolute sm:tw-hidden  tw-z-10 tw-left-10 tw-top-6 tw-w-5 tw-mx-auto"
                    src="/images/C&A-Logo-Full-White.svg"
                    alt=""
                    w={220}
                /> */}
            <Outlet />
            {/* <div className="sm:tw-fixed tw-bottom-5 tw-w-full tw-text-center tw-text-white tw-py-2 tw-text-sm">陈玮伦合伙人事务所 版权所有 © 2009 - 2025</div> */}
            <ConfirmModal />
        </div>
    ) : (
        <Center className="tw-h-screen tw-w-screen">
            <Stack>
                <Image
                    className="tw-mx-auto"
                    src="/images/C&A-logo-icon-blue.svg"
                    alt=""
                    w={120}
                />
                <Text className="tw-text-center tw-my-6 tw-text-xl">陈玮伦合伙人事务所</Text>
                <Group>
                    <Spinner
                        className="tw-animate-spin"
                        size={32}
                    />
                    <Text>正在初始化配置...</Text>
                </Group>
            </Stack>
        </Center>
    );
};

export default RootLayout;
