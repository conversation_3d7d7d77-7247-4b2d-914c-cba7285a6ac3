import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const fieldType = {
    list: async (params: any) => {
        const { error, result } = await cnaRequest<any>(
            `/api/v1/admin/teamProfile/industry`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 创建
    store: async (params: TaddFieldItemParam) => {
        const { error, result } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/teamProfile/industry",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    //
    teamList: async (id: string) => {
        const { error, result } = await cnaRequest<any>(
            `/api/v1/admin/teamProfile/industry/detail/${id}`,
            "GET",
        );
        if (!error) {
            console.log('result:', result)
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    addTeam: async (data: { industry_name: string, market_code: string; number: string }) => {
        const { error, result } = await cnaRequest<any>(
            `/api/v1/admin/teamProfile/industry`,
            "POST",
            data
        );
        if (!error) {
            return true
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // // 更新
    // update: async (params: UserProfileResponse, id: number) => {
    //     const { error, result } = await cnaRequest<BaseApiResponse>(
    //         `/api/v1/admin/teamProfile/industry/update/${id}`,
    //         "PUT",
    //         params
    //     );

    //     if (!error) {
    //         return true;
    //     } else {
    //         noty.error(error.message);
    //         return false;
    //     }
    // },


};

export default fieldType;
