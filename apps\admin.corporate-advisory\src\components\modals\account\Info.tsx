import { t } from "@code.8cent/i18n";
import {
    AddressInput,
    CnaButton,
    CountrySelect,
    PhoneInput,
    ProfileAvatar,
} from "@code.8cent/react/components";
import useDataStore from "@code.8cent/store/data";
import useSettingStore from "@code.8cent/store/setting";
import { Box, Grid, Group, Modal, Stack, TextInput, Text } from "@mantine/core";
import { useCallback, useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import dayjs from "dayjs";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";
import { useMemoizedFn } from "ahooks";

const userProfileSchema = z.object({
    profileID: z.number(),
    profileName: z.string().min(1, "请输入全名"),
    profileGivenName: z.string().min(1, "请输入名字"),
    profilelastName: z.string().min(1, "请输入姓氏"),
    profileNRIC: z.string().min(1, "请输入身份证号"),
    profileEmail: z.string().email("请输入有效的邮箱地址"),
    profileNationalityID: z.number().min(1, "请选择国籍"),
    profileContact: z.string().min(1, "请输入联系电话"),
    profileAvatar: z.string().optional(),
    profileAddressUnit: z.string().optional(),
    profileAddressStreet: z.string().optional(),
    profileAddressCity: z.string().optional(),
    profileAddressState: z.string().optional(),
    profileAddressPostcode: z.string().optional(),
    profileAddressCountry: z.string().optional(),
    profileAddressDistrict: z.string().optional(),
    mobilePrefixID: z.number().optional(),
});

type UserProfileForm = z.infer<typeof userProfileSchema>;

const Info = ({
    profile = {},
    opened = false,
    onClose = () => {},
    onUpdateSuccess = () => {},
}: {
    profile?: Partial<UserProfileForm>;
    opened?: boolean;
    onClose?: () => void;
    onUpdateSuccess?: () => void;
}) => {
    const { lang } = useSettingStore();

    const { countryDatas } = useDataStore();
    const [loading, setLoading] = useState(false);

    const openConfirm = useModalStore.use.openConfirm();

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
    } = useForm<UserProfileForm>({
        defaultValues: {
            profileID: profile.profileID,
            profileName: profile.profileName,
            profileGivenName: profile.profileName?.split(" ")[0] ?? "",
            profilelastName: profile.profileName?.split(" ")[1] ?? "",
            profileNRIC: profile.profileNRIC ?? "",
            profileEmail: profile.profileEmail,
            profileNationalityID: profile.profileNationalityID ?? 0,
            profileContact: profile.profileContact ?? "",
            profileAddressUnit: profile.profileAddressUnit ?? "",
            profileAddressStreet: profile.profileAddressStreet ?? "",
            profileAddressCity: profile.profileAddressCity ?? "",
            profileAddressState: profile.profileAddressState ?? "",
            profileAddressPostcode: profile.profileAddressPostcode ?? "",
            profileAddressCountry: profile.profileAddressCountry ?? "",
        },
        resolver: zodResolver(userProfileSchema),
    });

    const handleInputChange = useCallback(
        (key: string, value: any) => {
            setValue(key as keyof UserProfileForm, value);
        },
        [setValue]
    );

    const handleAddressDataChange = (addressData: Partial<UserProfileForm>) => {
        // Update the form state with the new address data
        setValue("profileAddressUnit", addressData.profileAddressUnit);
        setValue("profileAddressStreet", addressData.profileAddressStreet);
        setValue("profileAddressCity", addressData.profileAddressCity);
        setValue("profileAddressState", addressData.profileAddressState);
        setValue("profileAddressPostcode", addressData.profileAddressPostcode);
        setValue("profileAddressCountry", addressData.profileAddressCountry);
    };

    const submitForm: SubmitHandler<UserProfileForm> = useMemoizedFn(async (data) => {
        // 拼接账户名
        const profileName = `${data.profileGivenName} ${data.profilelastName}`.trim();
        const updatedData = {
            ...data,
            profileName,
        } as UserProfileResponse;

        console.log(updatedData);

        setLoading(true);
        try {
            // 更新账户信息
            const res = await api.account.update(updatedData, profile.profileID);

            if (res) {
                noty.success("用户资料更新成功");
                onUpdateSuccess();
                onClose();
            }
        } catch (error) {
            noty.error("用户资料更新失败，请重试");
        } finally {
            setLoading(false);
        }
    });

    const handleSave = useMemoizedFn(() => {
        openConfirm({
            title: "提示",
            message: "您确定更新账户的信息么？",
            onConfirm: handleSubmit(submitForm),
        });
    });

    return (
        <Modal
            opened={opened}
            onClose={onClose}
            title={`行政 ${profile.profileName} 简介`}
            size="xl"
        >
            <form
                onSubmit={handleSubmit(handleSave, (error) => {
                    console.log(error);
                })}
            >
                <Stack gap={"lg"}>
                    <Group justify="center">
                        {profile.profileAvatar && profile.profileAvatar !== "" && (
                            <ProfileAvatar
                                style={{
                                    width: 120,
                                }}
                                src={`${window.api_base_url}${profile.profileAvatar}`}
                            />
                        )}
                    </Group>
                    <Grid>
                        <Grid.Col span={{ base: 12, md: 6 }}>
                            <Stack>
                                <TextInput
                                    label={t("introduction.label.given_name", lang)}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    {...register("profileGivenName")}
                                    error={errors.profileGivenName?.message}
                                />
                                <CountrySelect
                                    label={t("introduction.nationality", lang)}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    value={String(profile.profileNationalityID)}
                                    {...register("profileNationalityID")}
                                    {...{
                                        data: countryDatas,
                                        flagKey: "countryISOCode2",
                                        labelKey: `country${lang}` as keyof CountryDataItem,
                                        valueKey: "countryID",
                                    }}
                                    onChange={(value) =>
                                        handleInputChange("profileNationalityID", value)
                                    }
                                />
                                <PhoneInput<CountryDataItem>
                                    data={countryDatas}
                                    label={t("introduction.label.phone", lang)}
                                    prefixFlagKey="countryISOCode2"
                                    prefixValueKey="countryID"
                                    prefixLabelKey="countryCode"
                                    wrapperProps={{
                                        labelProps: {
                                            className: "profile-form-label",
                                        },
                                    }}
                                    inputProps={{
                                        value: profile.profileContact ?? "",
                                    }}
                                    prefixProps={{
                                        w: 120,
                                        value: String(profile.mobilePrefixID ?? ""),
                                    }}
                                    readOnly
                                />
                            </Stack>
                        </Grid.Col>
                        <Grid.Col span={{ base: 12, md: 6 }}>
                            <Stack>
                                <TextInput
                                    label={t("introduction.label.last_name", lang)}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    {...register("profilelastName")}
                                    error={errors.profilelastName?.message}
                                />
                                <TextInput
                                    label={t("introduction.label.phone", lang)}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    {...register("profileNRIC")}
                                    error={errors.profileNRIC?.message}
                                />
                                <TextInput
                                    label={t("introduction.label.email", lang)}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    {...register("profileEmail")}
                                    error={errors.profileEmail?.message}
                                />
                            </Stack>
                        </Grid.Col>
                        <Grid.Col span={12}>
                            <AddressInput<typeof profile, CountryDataItem>
                                label={t("introduction.label.address", lang)}
                                wrapperProps={{
                                    className: "tw-mb-2",
                                    labelProps: {
                                        className: "profile-form-label",
                                    },
                                }}
                                addressFieldMap={{
                                    unit: {
                                        key: "profileAddressUnit",
                                        placeholder: t("introduction.label.unit", lang),
                                    },
                                    street: {
                                        key: "profileAddressStreet",
                                        placeholder: t("introduction.label.street", lang),
                                    },
                                    city: {
                                        key: "profileAddressCity",
                                        placeholder: t("introduction.label.city", lang),
                                    },
                                    district: {
                                        key: "profileAddressDistrict",
                                        placeholder: t("introduction.label.district", lang),
                                    },
                                    state: {
                                        key: "profileAddressState",
                                        placeholder: t("introduction.label.state", lang),
                                    },
                                    postcode: {
                                        key: "profileAddressPostcode",
                                        placeholder: t("introduction.label.postcode", lang),
                                    },
                                    country: {
                                        key: "profileAddressCountry",
                                        placeholder: t("introduction.label.country", lang),
                                    },
                                }}
                                countrySelectProps={{
                                    data: countryDatas,
                                    flagKey: "countryISOCode2",
                                    labelKey: `country${lang}` as keyof CountryDataItem,
                                    valueKey: "countryID",
                                }}
                                addressData={profile}
                                onAddressDataChange={handleAddressDataChange}
                            />
                        </Grid.Col>
                    </Grid>

                    <ModalFooter
                        // timelineContent="最近修改: Amos Wu (2024-11-18 12:00:00)"
                        buttons={[
                            {
                                key: "save",
                                label: "更新",
                                leftSection: <Check size={16} />,
                                type: "submit",
                                loading: loading,
                            },
                            {
                                key: "close",
                                label: "取消",
                                style: "outline",
                                leftSection: <X size={16} />,
                                onClick: onClose,
                            },
                        ]}
                    />
                </Stack>
            </form>
        </Modal>
    );
};

export default Info;
