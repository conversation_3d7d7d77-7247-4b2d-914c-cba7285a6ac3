import xlsx from "xlsx";

interface ExcelHeader {
    key: string;
    title: string;
    format?: (value: any) => any;
    width?: number; // 自定义列宽
}

/**
 * 计算字符串显示宽度（考虑中文字符）
 */
function getStringWidth(str: string): number {
    return [...str].reduce((width, char) => {
        return width + (/[\u4e00-\u9fa5]/.test(char) ? 2 : 1);
    }, 0);
}

/**
 * 导出数据到 Excel 文件
 * @param data 要导出的数据数组
 * @param headers 表头配置 { key: string, title: string, format?: (value: any) => any }[]
 * @param filename 导出的文件名
 * @param totalData 合计行数据, 格式为 { key1: string, key2: string }
 */
export function exportToExcel(
    data: Record<string, any>[],
    headers: ExcelHeader[],
    filename: string = "export.xlsx",
    totalData?: Record<string, string>
): void {
    try {
        // 转换数据格式
        const excelData = data.map((item) => {
            const row: Record<string, any> = {};
            headers.forEach((header) => {
                const value = item[header.key];
                row[header.title] = header.format ? header.format(value) : value;
            });
            return row;
        });

        // 如果有合计数据，添加合计行
        if (totalData) {
            const totalRow: Record<string, string> = {};
            headers.forEach((header) => {
                totalRow[header.title] = totalData[header.key] || "";
            });
            excelData.push(totalRow);
        }

        // 创建工作簿
        const workbook = xlsx.utils.book_new();

        // 创建工作表
        const worksheet = xlsx.utils.json_to_sheet(excelData);

        // 列宽计算
        const columnWidths: { [key: string]: number } = {};
        headers.forEach((header) => {
            // 使用预设宽度或计算标题宽度
            columnWidths[header.title] = header.width || getStringWidth(header.title);
        });

        // 计算数据宽度
        excelData.forEach((row) => {
            headers.forEach((header) => {
                const cellValue = String(row[header.title] || "");
                const cellWidth = getStringWidth(cellValue);
                if (cellWidth > (columnWidths[header.title] || 0)) {
                    columnWidths[header.title] = cellWidth;
                }
            });
        });

        // 设置列宽
        worksheet["!cols"] = headers.map((header) => ({
            // 设置最小宽度为8，最大宽度为50，并添加2个字符的padding
            wch: Math.min(Math.max(columnWidths[header.title] + 2, 8), 50),
        }));

        // 将工作表添加到工作簿
        xlsx.utils.book_append_sheet(workbook, worksheet, "Sheet1");

        // 导出文件
        xlsx.writeFile(workbook, filename);
    } catch (error) {
        console.error("导出 Excel 失败:", error);
        if (error instanceof Error) {
            throw new Error(`导出失败: ${error.message}`);
        } else {
            throw new Error("导出失败: 未知错误");
        }
    }
}
