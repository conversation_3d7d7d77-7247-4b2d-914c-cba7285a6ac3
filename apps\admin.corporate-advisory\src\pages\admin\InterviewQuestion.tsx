import { Group, Stack } from "@mantine/core";
import { useRef, useState, useEffect } from "react";
import { useMemoizedFn } from "ahooks";
import { PageHeader, DataTable, DataTableRef, CnaButton } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import useModalStore from "@/store/modal";
import { Plus } from "@phosphor-icons/react";
import interviewQuestion from "@/apis/interviewQuestion";
import Form from "@/components/modals/interviewQuestion/Form";
import NextList from "@/components/modals/interviewQuestion/NextList";
import { useConfirm } from "@/hooks/useConfirm";
import PageActionButtons from "@/components/common/PageActionButtons";
import noty from "@code.8cent/react/noty";

const columnHelper = createColumnHelper<TInterviewQuestion>();

const AdminInterviewQuestionPage = () => {
    const [data, setData] = useState<TInterviewQuestion[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [moduleOptions, setModuleOptions] = useState<{ value: string; label: string }[]>([]);
    const tableRef = useRef<DataTableRef>(null);
    const { confirm } = useConfirm();

    const { open } = useModalStore();

    // 获取模块选项
    useEffect(() => {
        const fetchModuleOptions = async () => {
            const modules = await interviewQuestion.moduleOptions();
            if (modules) {
                const options = Object.values(modules).map((item: any) => ({
                    value: item.value.toString(),
                    label: item.label,
                }));
                setModuleOptions(options);
            }
        };
        fetchModuleOptions();
    }, []);

    // 表格列定义
    const tableColumns = [
        columnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("module_name", {
            header: "模块",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("question", {
            header: "问题内容",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                const row = info.row.original;
                return (
                    <Group gap="xs">
                        <CnaButton
                            variant="outline"
                            size="xs"
                            onClick={() => handleEdit(row)}
                        >
                            编辑
                        </CnaButton>
                        <CnaButton
                            variant="outline"
                            size="xs"
                            onClick={() => handleViewNext(row)}
                        >
                            追问
                        </CnaButton>
                        {/* <CnaButton
                            color="red"
                            size="xs"
                            onClick={() => handleDelete(row.id)}
                        >
                            删除
                        </CnaButton> */}
                    </Group>
                );
            },
        }),
    ];

    // 刷新表格数据
    const refreshTable = useMemoizedFn(() => {
        if (tableRef.current) {
            tableRef.current.refresh();
        }
    });

    // 获取数据
    const handleFetch = async (params: any) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams: TInterviewQuestionSearchParams = {
                page,
                page_size: pageSize,
                keyword: globalFilters?.keyword || "",
                module: globalFilters?.module || undefined,
            };

            const result = await interviewQuestion.list(requestParams);
            if (result) {
                setData(result.items);
                setTotalCount(result.paginate.total);
            }
        } finally {
            setLoading(false);
        }
    };

    // 添加问题
    const handleAdd = useMemoizedFn(() => {
        open("interviewQuestionForm");
    });

    // 编辑问题
    const handleEdit = useMemoizedFn((row: TInterviewQuestion) => {
        open("interviewQuestionForm", row);
    });

    // 查看追问问题
    const handleViewNext = useMemoizedFn((row: TInterviewQuestion) => {
        open("interviewQuestionNextList", row);
    });

    // 删除问题
    const handleDelete = useMemoizedFn(async (id: number) => {
        confirm({
            title: "确认删除",
            message: "您确定要删除这个问题吗？相关的追问问题也将被删除，此操作不可撤销。",
            variant: "danger",
            onConfirm: async () => {
                try {
                    const success = await interviewQuestion.destroy(id.toString());
                    if (success) {
                        noty.success("删除成功");
                        refreshTable();
                    }
                } catch (error) {
                    console.error("删除问题失败", error);
                    noty.error("删除失败");
                }
            },
        });
    });

    const pageButtons = [
        {
            key: "add",
            label: "新增",
            leftSection: <Plus size={14} />,
            onClick: handleAdd,
        },
    ];

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="面试问题管理"
                desc="管理面试问题和追问问题"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "关键词搜索",
                        type: "text",
                    },
                    {
                        field: "module",
                        label: "模块",
                        type: "select",
                        options: moduleOptions,
                    },
                ]}
            />

            {/* 引入组件 */}
            <Form refreshTable={refreshTable} />
            <NextList />
        </Stack>
    );
};

export default AdminInterviewQuestionPage;
