import { Modal, Group, Text, Box } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { Upload, X, File } from "@phosphor-icons/react";
import useModalStore from "@/store/modal";

export const UploadModal = () => {
    const upload = useModalStore((state) => state.upload);
    const closeUpload = useModalStore((state) => state.closeUpload);

    return (
        <Modal
            opened={upload.show}
            onClose={closeUpload}
            title={upload.title}
            size="lg"
        >
            <Dropzone
                onDrop={(files) => {
                    if (upload.onSuccess && files.length > 0) {
                        upload.onSuccess(files[0]);
                        closeUpload();
                    }
                }}
                onReject={(files) => {
                    upload.onError?.(files);
                }}
                maxSize={upload.maxSize}
                accept={upload.accept}
            >
                <Group className="tw-p-8 tw-pointer-events-none">
                    <Dropzone.Accept>
                        <Upload size={32} />
                    </Dropzone.Accept>
                    <Dropzone.Reject>
                        <X size={32} />
                    </Dropzone.Reject>
                    <Dropzone.Idle>
                        <File size={32} />
                    </Dropzone.Idle>

                    <Box>
                        <Text
                            size="xl"
                            inline
                        >
                            拖拽文件到此处或点击上传
                        </Text>
                        <Text
                            size="sm"
                            c="dimmed"
                            inline
                            mt={10}
                        >
                            文件大小不能超过{" "}
                            {upload.maxSize ? `${upload.maxSize / 1024 / 1024}MB` : "限制"}
                        </Text>
                        <Text
                            size="sm"
                            c="dimmed"
                            inline
                            mt={10}
                        >
                            上传文件类型 {upload.accept.join(", ")}
                        </Text>
                    </Box>
                </Group>
            </Dropzone>
        </Modal>
    );
};
