import useModalStore from "@/store/modal";
import { useMemoizedFn } from "ahooks";

type ConfirmOptions = {
    title?: string;
    message?: string;
    variant?: "success" | "danger" | "warning" | "info";
    onConfirm?: () => void;
};

export const useConfirm = () => {
    const { openConfirm } = useModalStore();

    const confirm = useMemoizedFn((options: ConfirmOptions) => {
        openConfirm({
            title: options.title || "确认操作",
            message: options.message || "您确定要执行此操作吗？",
            variant: options.variant || "warning",
            onConfirm: options.onConfirm || (() => {}),
        });
    });

    return { confirm };
};
