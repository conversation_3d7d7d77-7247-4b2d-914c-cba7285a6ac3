import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const activity = {
    list: async (params: TActivitySearchParams) => {
        const { error, result } = await cnaRequest<TActivitiesResponse>(
            `/api/v1/admin/activity/index`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (params: Omit<TActivity, "id" | "created_at" | "updated_at">) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/activity/add",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (params: Omit<TActivity, "created_at" | "updated_at">) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/activity/edit`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    destroy: async (id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>("/api/v1/admin/activity/del", "POST", {
            id,
        });

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 活动报名列表
    signUpList: async (params: TActivitySignupSearchParams) => {
        const { error, result } = await cnaRequest<TActivitySignup[]>(
            "/api/v1/admin/activity/signup",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default activity;
