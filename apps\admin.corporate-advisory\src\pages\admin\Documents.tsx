import api from "@/apis";
import { Space, Stack, Tabs } from "@mantine/core";
import { useMemoizedFn, useRequest } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Plus } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import Create from "@/components/modals/document/Create";
import Folder from "@/components/modals/document/Folder";
import ThreeCreate from "@/components/modals/document/ThreeCreate";
import useModalStore from "@/store/modal";
import noty from "@code.8cent/react/noty";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import dayjs from "dayjs";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";

const documentColumnHelper = createColumnHelper<TDocument>();
const folderColumnHelper = createColumnHelper<TDocumentFolder>();
const threeColumnHelper = createColumnHelper<ThreeItem>();

type TabValue = "folders" | "documents" | "threeList";

const TAB_OPTIONS = [
    { value: "folders", label: "文件夹列表" },
    { value: "documents", label: "文件列表" },
    { value: "threeList", label: "委员会" },
] as const;

const AdminDataPage = () => {
    const { lang } = useSettingStore();

    const openConfirm = useModalStore.use.openConfirm();
    const openModal = useModalStore.use.open();

    const [tab, setTab] = useState<TabValue>("folders");

    const documentTableRef = useRef<DataTableRef | null>(null);
    const [documentData, setDocumentData] = useState([]);
    const [documentTotalCount, setDocumentTotalCount] = useState(0);
    const [documentLoading, setDocumentLoading] = useState(false);

    const folderTableRef = useRef<DataTableRef | null>(null);
    const [folderData, setFolderData] = useState([]);
    const [folderTotalCount, setFolderTotalCount] = useState(0);
    const [folderLoading, setFolderLoading] = useState(false);

    const threeTableRef = useRef<DataTableRef | null>(null);
    const [threeData, setThreeData] = useState([]);
    const [threeTotalCount, setThreeTotalCount] = useState(0);
    const [threeLoading, setThreeLoading] = useState(false);

    const handleFolderDelete = useMemoizedFn((id: number) => {
        openConfirm({
            title: "提示",
            message: "您确定通删除此文件夹吗？",
            onConfirm: () => folderDelete(id),
        });
    });

    const folderDelete = useMemoizedFn(async (id: number) => {
        try {
            const res = await api.document.folders.delete(id);
            if (res) {
                noty.success("删除成功");
                refreshFolders();
            }
        } catch (error) {
            console.error("Failed to delete folder:", error);
        }
    });

    // const { data: folders } = useRequest(api.document.folders.list, {
    //     defaultParams: [{ keyword: "", page: 1, page_size: 1000 }],
    // });

    // const { data: threeListResponse } = useRequest(api.document.three.list, {
    //       defaultParams: [{ keyword: "", page: 1, page_size: 1000 }],
    //   });
       
    //    const threeList = threeListResponse?.data || [];

    const threePageButtons = [
        {
            key: "add",
            leftSection: (
                <Plus
                    weight="bold"
                    size={14}
                />
            ),
            label: "创建",
            onClick: () => openModal("threeCreateModal", { threeList: threeData}),
        },
    ];

    const documentPageButtons = [
        {
            key: "add",
            leftSection: (
                <Plus
                    weight="bold"
                    size={14}
                />
            ),
            label: "创建",
            onClick: () => openModal("documentCreateModal", { folders: folderData }),
        },
    ];

    const folderPageButtons = [
        {
            key: "add",
            leftSection: (
                <Plus
                    weight="bold"
                    size={14}
                />
            ),
            label: "创建",
            onClick: () => openModal("documentFolderModal", { folders: folderData, folder: {} }),
        },
    ];

    const documentRowActions = (row) => [
        {
            key: "edit",
            label: "编辑",
            onClick: () =>
                openModal("documentCreateModal", { folders: folderData, document: row }),
        },
        {
            key: "delete",
            label: "删除",
            onClick: () => handleDocumentDelete(row.documentID),
        },
    ];

    const folderRowActions = (row) => {
        // 检查是否为受保护的文件夹
        const isProtectedFolder = row.foldersNameZH === "团队文件" || row.foldersNameZH === "招募工具";
        
        return [
            {
                key: "edit",
                label: "编辑",
                onClick: () => openModal("documentFolderModal", { folders: folderData, folder: row }),
            },
            {
                key: "delete",
                label: "删除",
                disabled: isProtectedFolder,
                onClick: () => handleFolderDelete(row.id),
            },
        ];
    };

    const threeRowActions = (row) => [
        {
            key: "edit",
            label: "编辑",
            onClick: () => openModal("threeCreateModal", { threeList: threeData, three: row }),
        },
    ];

    const handleDocumentFetch = async (params) => {
        setDocumentLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.document.list(requestParams);
            setDocumentData(items.filter((item) => item.documentTitle !== "团队文件" && item.documentTitle !== "三三制") || []);
            setDocumentTotalCount(paginate?.total || 0);
        } finally {
            setDocumentLoading(false);
        }
    };

    const handleFolderFetch = async (params) => {
        setFolderLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.document.folders.list(requestParams);
            setFolderData(items || []);
            setFolderTotalCount(paginate?.total || 0);
        } finally {
            setFolderLoading(false);
        }
    };

    const documentDelete = useMemoizedFn(async (documentId: number) => {
        try {
            const res = await api.document.delete(documentId);
            if (res) {
                noty.success("删除成功");
                refreshDocuments();
            }
        } catch (error) {
            console.error("Failed to delete document:", error);
        }
    });

    const handleDocumentDelete = useMemoizedFn((id: number) => {
        openConfirm({
            title: "提示",
            message: "您确定通删除此文件吗？",
            onConfirm: () => documentDelete(id),
        });
    });

    const refreshDocuments = useMemoizedFn(() => {
        if (documentTableRef?.current) {
            documentTableRef.current.refresh();
        }
    });

    const refreshFolders = useMemoizedFn(() => {
        if (folderTableRef?.current) {
            folderTableRef.current.refresh();
        }
    });

    const refreshThree = useMemoizedFn(() => {
        if (threeTableRef?.current) {
            threeTableRef.current.refresh();
        }
    });

    const handleThreeFetch = async (params) => {
        setThreeLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const response = await api.document.three.list(requestParams);
            setThreeData(response?.data || []);
            console.log("🚀 ~ Documents.tsx:250 ~ handleThreeFetch ~ response?.data:", response?.data)
            setThreeTotalCount(response?.total || 0);
        } finally {
            setThreeLoading(false);
        }
    };

    const documentColumns = [
        documentColumnHelper.accessor("documentTitle", {
            header: "文档标题",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        documentColumnHelper.accessor("folderId", {
            header: "所在文件夹",
            enableSorting: false,
            cell: (info) =>
                info.getValue()
                    ? folderData.find((folder) => folder.id === info.getValue())?.[
                          `foldersName${lang}`
                      ]
                    : "",
        }),
        documentColumnHelper.accessor("documentVersion", {
            header: "文件版本",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        documentColumnHelper.accessor("createUserName", {
            header: "上传者",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        documentColumnHelper.accessor("created_at", {
            header: "上传时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        documentColumnHelper.accessor("updated_at", {
            header: "更新时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        documentColumnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => (
                <TableRowDropActionMenu items={documentRowActions(info.row.original)} />
            ),
        }),
    ];

    const folderColumns = [
        folderColumnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        folderColumnHelper.accessor("parentId", {
            header: "上级文件夹",
            enableSorting: false,
            cell: (info) =>
                info.getValue()
                    ? folderData.find((folder) => folder.id === info.getValue())?.[
                          `foldersName${lang}`
                      ]
                    : "",
        }),
        folderColumnHelper.accessor("foldersNameEN", {
            header: "文件夹名称(英文)",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        folderColumnHelper.accessor("foldersNameZH", {
            header: "文件夹名称(中文)",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        folderColumnHelper.accessor("foldersNameZT", {
            header: "文件夹名称(繁体)",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        folderColumnHelper.accessor("foldersNameMS", {
            header: "文件夹名称(马来)",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        folderColumnHelper.accessor("order", {
            header: "排序",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        folderColumnHelper.accessor("created_at", {
            header: "创建时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        folderColumnHelper.accessor("updated_at", {
            header: "更新时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        folderColumnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={folderRowActions(info.row.original)} />,
        }),
    ];

    const threeColumns = [
        threeColumnHelper.accessor("job", {
            header: "职位",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        threeColumnHelper.accessor("level", {
            header: "级别",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        threeColumnHelper.accessor("file_version", {
            header: "文件版本",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        threeColumnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={threeRowActions(info.row.original)} />,
        }),
    ];

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="资料"
                desc="资料信息"
            />

            {tab === "folders" && (
                <PageActionButtons
                    buttons={folderPageButtons}
                    className="tw-fixed tw-right-6"
                />
            )}

            {tab === "documents" && (
                <PageActionButtons
                    buttons={documentPageButtons}
                    className="tw-fixed tw-right-6"
                />
            )}

            {tab === "threeList" && (
                <PageActionButtons
                    buttons={threePageButtons}
                    className="tw-fixed tw-right-6"
                />
            )}

            <Tabs
                value={tab}
                onChange={(tab: TabValue) => setTab(tab)}
            >
                <Tabs.List>
                    {TAB_OPTIONS.map(({ value, label }) => (
                        <Tabs.Tab
                            key={value}
                            value={value}
                            className={tab === value ? "tw-bg-basic-5 tw-text-white" : ""}
                        >
                            {label}
                        </Tabs.Tab>
                    ))}
                </Tabs.List>

                <Tabs.Panel value="folders">
                    <Space h={10} />
                    <DataTable
                        ref={folderTableRef}
                        data={folderData}
                        columns={folderColumns as any}
                        totalCount={folderTotalCount}
                        loading={folderLoading}
                        onFetch={handleFolderFetch}
                        globalFilterFields={[
                            {
                                field: "keyword",
                                label: "搜索关键字",
                                type: "text",
                            },
                        ]}
                    />

                    <Folder onUpdateSuccess={refreshFolders} />
                </Tabs.Panel>
                <Tabs.Panel value="documents">
                    <Space h={10} />
                    <DataTable
                        ref={documentTableRef}
                        data={documentData}
                        columns={documentColumns as any}
                        totalCount={documentTotalCount}
                        loading={documentLoading}
                        onFetch={handleDocumentFetch}
                        globalFilterFields={[
                            {
                                field: "keyword",
                                label: "搜索关键字",
                                type: "text",
                            },
                        ]}
                    />

                    <Create onUpdateSuccess={refreshDocuments} />
                </Tabs.Panel>
                <Tabs.Panel value="threeList">
                    <Space h={10} />
                    <DataTable
                        ref={threeTableRef}
                        data={threeData}
                        columns={threeColumns as any}
                        totalCount={threeTotalCount}
                        loading={threeLoading}
                        onFetch={handleThreeFetch}
                        globalFilterFields={[
                            {
                                field: "keyword",
                                label: "搜索关键字",
                                type: "text",
                            },
                        ]}
                    />

                    <ThreeCreate onUpdateSuccess={refreshThree} />
                </Tabs.Panel>
            </Tabs>
        </Stack>
    );
};

export default AdminDataPage;
