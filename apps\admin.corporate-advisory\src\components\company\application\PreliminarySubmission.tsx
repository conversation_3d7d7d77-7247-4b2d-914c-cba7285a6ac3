import api from "@/apis";
import {
    Button,
    Checkbox,
    Group,
    Input,
    Stack,
    Text,
    Tooltip,
} from "@mantine/core";
import { Dropzone, PDF_MIME_TYPE } from "@mantine/dropzone";
import { useMemoizedFn, useMount, useRequest, useUnmount } from "ahooks";
import { useContext, useState } from "react";

import { pdfjs, Document, Page } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

import type { PDFDocumentProxy } from "pdfjs-dist";
import { CompanyApplicationContext } from "@/contexts/project";
import useModalStore from "@/store/modal";
import { useEventBus, useListener } from "@/utils/eventBus";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { DownloadSimple, FileText, Question } from "@phosphor-icons/react";
import useCompanyApplicationFormEnabled from "@/hooks/project/useCompanyApplicationFormEnabled";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
    "pdfjs-dist/build/pdf.worker.min.mjs",
    import.meta.url
).toString();

const options = {
    cMapUrl: "/cmaps/",
    standardFontDataUrl: "/standard_fonts/",
};

const CompanyPreliminarySubmission: React.FC = () => {
    const lang = useSettingStore.use.lang();

    const { companyInfo } = useContext(CompanyApplicationContext);

    const [preliminarySubmissonData, setPreliminarySubmissonData] = useState<
        Partial<PreliminarySubmissionData>
    >({});

    const [inited, setInited] = useState(false);

    const [file, setFile] = useState<File | null>(null);

    const bus = useEventBus();

    const alert = useModalStore.use.openAlert();

    const formEnabled = useCompanyApplicationFormEnabled(companyInfo, 1);

    const { data: services } = useRequest(
        async () => {
            let services = await api.project.getProjectServiceList();

            return services;
        },
        {
            ready: inited,
        }
    );

    useRequest(
        async () => {
            let info = await api.project.getPreliminarySubmissonData(
                companyInfo.companyID
            );

            if (!info.companyServices[0]) {
                info.companyServices[0] = String(services[0].serviceID);
            }

            setPreliminarySubmissonData(info);
        },
        {
            ready: inited && services?.length > 0,
        }
    );

    useListener("project.company.application.submit.click", () => {
        if (formEnabled === true) {
            updatePreliminarySubmission();
        }
    });

    useMount(() => {
        setInited(true);
    });

    useUnmount(() => {
        setInited(false);
        setFile(null);
    });

    useRequest(
        async () => {
            if (formEnabled !== true) {
                let pdfFile =
                    await api.project.getUploadedPreliminarySubmissionForm(
                        companyInfo.companyID
                    );

                if (pdfFile) {
                    setFile(pdfFile);
                }
            }
        },
        {
            ready: inited && formEnabled === false,
        }
    );

    const { run: downloadCompanyApplicationPDF, loading: downloading } =
        useRequest(
            async () => {
                let res = await api.project.downloadCompanyApplicationPDF(
                    companyInfo.companyID
                );

                window.open(
                    `${window.api_base_url}/api/v1/company/progressOne/download/${res}`,
                    "_blank"
                );
            },
            { manual: true }
        );

    const updatePreliminarySubmission = useMemoizedFn(async () => {
        bus.emit("project.company.application.submitting", true);

        console.log(preliminarySubmissonData);

        let submit_res = await api.project.updatePreliminarySubmission({
            companyID: companyInfo.companyID,
            companyServices:
                preliminarySubmissonData.companyServices.toString(),
            preAuditForm: file!,
        });

        if (submit_res === true) {
            alert(
                t("file.submit.title", lang),
                t("file.submit.title.success", lang),
                "success"
            );
            bus.emit("project.company.application.close");
            bus.emit("project.list.refresh");
        } else {
            alert(
                t("file.submit.title", lang),
                t("file.submit.title.fail", lang),
                "danger"
            );
        }

        bus.emit("project.company.application.submitting", false);
    });

    return (
        inited === true && (
            <Stack>
                <Checkbox.Group
                    label={t(
                        "project.edit.step1.form.title.company_services",
                        lang
                    )}
                    value={
                        preliminarySubmissonData?.companyServices?.map(
                            (service) => String(service)
                        ) ?? []
                    }
                    onChange={(pick_services) => {
                        if (
                            !pick_services.includes(
                                String(services[0].serviceID)
                            )
                        ) {
                            pick_services = [
                                String(services[0].serviceID),
                                ...pick_services,
                            ];
                        }
                        setPreliminarySubmissonData((prev) => ({
                            ...prev,
                            companyServices: pick_services,
                        }));
                    }}
                >
                    <Stack className="tw-mt-4">
                        {services?.map?.((service, sidx) => (
                            <Checkbox
                                disabled={!formEnabled}
                                key={sidx}
                                size="xs"
                                value={String(service.serviceID)}
                                classNames={{
                                    body: "tw-items-center",
                                    labelWrapper: "tw-flex-1",
                                }}
                                label={
                                    <Group justify="space-between">
                                        <Text
                                            size="sm"
                                            className="tw-text-gray-500 tw-leading-none"
                                        >
                                            {service.serviceTitle}
                                        </Text>
                                        <Group gap={3}>
                                            <Text
                                                size="sm"
                                                className="tw-text-gray-500 tw-leading-none"
                                            >
                                                {service.serviceCurrency}
                                            </Text>
                                            <Text
                                                size="sm"
                                                className="tw-text-gray-500 tw-leading-none"
                                            >
                                                {service.servicePrice}
                                            </Text>
                                            <Tooltip label={service.serviceTip}>
                                                <Question
                                                    className="tw-cursor-pointer tw-text-gray-500"
                                                    size={14}
                                                />
                                            </Tooltip>
                                        </Group>
                                    </Group>
                                }
                            />
                        ))}
                    </Stack>
                </Checkbox.Group>
                <Input.Wrapper
                    label={t(
                        "project.edit.step1.form.title.download_form",
                        lang
                    )}
                    classNames={{
                        label: "tw-mb-3",
                    }}
                >
                    <Stack>
                        <CnaButton
                            fullWidth
                            color="gray.2"
                            c="dark.2"
                            onClick={downloadCompanyApplicationPDF}
                            loading={downloading}
                            disabled={!formEnabled}
                        >
                            <DownloadSimple
                                className="tw-mr-2"
                                weight="regular"
                                size={18}
                            />
                            <Text className="tw-leading-none tw-text-sm">
                                {t("common.download", lang)}
                            </Text>
                        </CnaButton>
                    </Stack>
                </Input.Wrapper>
                <Input.Wrapper
                    label={t("project.edit.step1.form.title.upload_form", lang)}
                    classNames={{
                        label: "tw-mb-3",
                    }}
                >
                    <Stack>
                        <Dropzone
                            accept={PDF_MIME_TYPE}
                            onDrop={(e) => {
                                setFile(e[0]);
                            }}
                            multiple={false}
                            disabled={!formEnabled}
                        >
                            <Stack align="center">
                                <FileText
                                    size={26}
                                    weight="regular"
                                    className="tw-text-dimmed"
                                />

                                <Text c="dimmed" size="sm">
                                    {file instanceof File === true
                                        ? file.name
                                        : t(
                                              "project.edit.step1.form.title.file_drag",
                                              lang
                                          )}
                                </Text>
                            </Stack>
                        </Dropzone>
                    </Stack>
                </Input.Wrapper>
            </Stack>
        )
    );
};

export default CompanyPreliminarySubmission;
