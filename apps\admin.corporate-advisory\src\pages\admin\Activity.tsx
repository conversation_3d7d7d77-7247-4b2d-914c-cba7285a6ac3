import api from "@/apis";
import { ActionIcon, Space, Stack, Tabs } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef, useEffect } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Plus, Copy } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import Info from "@/components/modals/activity/Info";
import dayjs from "dayjs";
import { PageHeader, DataTable, DataTableRef, CnaButton } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import SignUpInfo from "@/components/modals/activity/SignUpInfo";
import { exportToExcel } from "@/utils/xlsx";

const activityColumnHelper = createColumnHelper<TActivity>();
const signupColumnHelper = createColumnHelper<TActivitySignup>();

type TabValue = "activities" | "signups";

const TAB_OPTIONS = [
    { value: "activities", label: "活动列表" },
    { value: "signups", label: "报名列表" },
] as const;

const AdminActivityPage = () => {
    const lang = useSettingStore.use.lang();

    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();

    const [tab, setTab] = useState<TabValue>("activities");

    const [activityId, setActivityId] = useState<number | string>("");

    const activityTableRef = useRef<DataTableRef | null>(null);
    const [activityData, setActivityData] = useState([]);
    const [activityTotalCount, setActivityTotalCount] = useState(0);
    const [activityLoading, setActivityLoading] = useState(false);

    const signupTableRef = useRef<DataTableRef | null>(null);
    const [signupData, setSignupData] = useState([]);
    const [signupTotalCount, setSignupTotalCount] = useState(0);
    const [signupLoading, setSignupLoading] = useState(false);

    const activityRowActions = (row) => [
        {
            key: "signup",
            label: "报名列表",
            onClick: () => {
                setTab("signups");
                setActivityId(row.id);
            },
        },
        {
            key: "edit",
            label: "编辑",
            onClick: () => {
                openModal("activityInfoModal", {
                    activity: row,
                });
            },
        },
        {
            key: "delete",
            label: "删除",
            onClick: () => {},
        },
    ];

    const activityColumns = [
        activityColumnHelper.accessor("id", {
            header: "序号",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        activityColumnHelper.accessor("active_name", {
            header: "活动名称",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        activityColumnHelper.accessor("active_time", {
            header: "活动时间",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        activityColumnHelper.accessor("active_place", {
            header: "活动地点",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        activityColumnHelper.accessor("active_url", {
            header: "活动链接",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => {
                const value = info.getValue();
                return (
                    <ActionIcon
                        size="xs"
                        variant="outline"
                        onClick={() => {
                            if (value) {
                                navigator.clipboard.writeText(value);
                            }
                        }}
                    >
                        <Copy size={14} />
                    </ActionIcon>
                );
            },
        }),
        activityColumnHelper.accessor("created_at", {
            header: "创建时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        activityColumnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => (
                <TableRowDropActionMenu items={activityRowActions(info.row.original)} />
            ),
        }),
    ];

    const signupColumns = [
        signupColumnHelper.accessor("call", {
            header: "称谓",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        signupColumnHelper.accessor("name", {
            header: "报名人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        signupColumnHelper.accessor("org_fullname", {
            header: "机构全称",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        signupColumnHelper.accessor("phone", {
            header: "联系方式",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        signupColumnHelper.accessor("email", {
            header: "邮箱",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        signupColumnHelper.accessor("inviter", {
            header: "邀约人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        signupColumnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                return (
                    <CnaButton
                        variant="outline"
                        size="xs"
                        onClick={() => {
                            openModal("activitySignupInfoModal", {
                                signup: info.row.original,
                            });
                        }}
                    >
                        查看详情
                    </CnaButton>
                );
            },
        }),
    ];

    const handleActivityFetch = async (params) => {
        setActivityLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.activity.list(requestParams);
            setActivityData(items || []);
            setActivityTotalCount(paginate?.total || 0);
        } finally {
            setActivityLoading(false);
        }
    };

    const handleSignupFetch = async (params) => {
        // activityId 为空，不执行请求
        if (!activityId) {
            setSignupData([]);
            setSignupTotalCount(0);
            return;
        }

        setSignupLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
                id: activityId,
            };

            const res = await api.activity.signUpList(requestParams);
            setSignupData(res || []);
            setSignupTotalCount(res?.length || 0);
        } finally {
            setSignupLoading(false);
        }
    };

    const pageButtons = [
        {
            key: "add",
            label: "新增",
            leftSection: <Plus size={14} />,
            onClick: () => openModal("activityInfoModal"),
        },
    ];

    const refreshActivityTable = useMemoizedFn(() => {
        if (activityTableRef.current) {
            activityTableRef.current.refresh();
        }
    });

    const refreshSignupTable = useMemoizedFn(() => {
        if (signupTableRef.current) {
            signupTableRef.current.refresh();
        }
    });

    useEffect(() => {
        if (tab === "signups" && activityId) {
            refreshSignupTable();
        }
    }, [tab, activityId]);

    const handleSignupExport = async () => {
        openConfirm({
            title: "提示",
            message: "您确定要导出数据吗？",
            onConfirm: async () => {
                const requestParams = {
                    id: activityId,
                };

                const res = await api.activity.signUpList(requestParams);

                // 检查返回数据是否为空
                if (!res || !Array.isArray(res)) {
                    console.warn("导出数据为空或格式不正确");
                    return;
                }

                const orgTypeOptions = [
                    { value: "1", label: "政府" },
                    { value: "2", label: "学术" },
                    { value: "3", label: "企业" },
                    { value: "4", label: "非政府组织" },
                    { value: "5", label: "国际组织" },
                    { value: "6", label: "其他" },
                ];

                const foodBanOptions = [
                    { value: "1", label: "素食" },
                    { value: "2", label: "清真" },
                    { value: "3", label: "犹太洁食" },
                    { value: "4", label: "乳糖不耐" },
                    { value: "5", label: "坚果过敏" },
                    { value: "6", label: "其他" },
                ];

                const healthNeedOptions = [
                    { value: "1", label: "轮椅通道" },
                    { value: "2", label: "临时休息室" },
                    { value: "3", label: "心脏病/高血压" },
                    { value: "4", label: "医疗协助" },
                ];

                const transportNeedOptions = [
                    { value: "1", label: "机场VIP接送" },
                    { value: "2", label: "酒店穿梭巴士" },
                    { value: "3", label: "自驾" },
                ];

                const roomNeedOptions = [
                    { value: "1", label: "组委会协议酒店" },
                    { value: "2", label: "自行安排" },
                    { value: "3", label: "需要套房" },
                    { value: "4", label: "无烟房" },
                    { value: "5", label: "同一楼层" },
                ];

                const cultureOptions = [
                    { value: "1", label: "祈祷室需求" },
                    { value: "2", label: "禁忌猪肉" },
                    { value: "3", label: "禁忌饮酒" },
                    { value: "4", label: "禁忌颜色/物品" },
                ];

                const socialHobbyOptions = [
                    { value: "1", label: "参与企业对接" },
                    { value: "2", label: "加入学者交流群" },
                    { value: "3", label: "不参与晚间社交活动" },
                ];

                const mediaOptions = [
                    { value: "1", label: "接受采访" },
                    { value: "2", label: "仅限指定媒体" },
                    { value: "3", label: "不参与采访" },
                ];

                const idHideOptions = [
                    { value: "1", label: "隐藏职务" },
                    { value: "2", label: "隐藏机构名称（仅显示姓名）" },
                ];

                const vipRoomOptions = [{ value: "1", label: "需要专属休息室（仅限部长级/CEO）" }];

                // 解析多选值函数
                const parseMultiSelect = (value: string | undefined, options: any[]) => {
                    if (!value) return "";
                    const values = value.split(",").map((v) => v.trim());
                    const labels = values.map((v) => {
                        const option = options.find((opt) => opt.value === v);
                        return option ? option.label : v;
                    });
                    return labels.join("、");
                };

                // 处理导出数据
                const processedData = res.map((item) => ({
                    call: item.call || "",
                    name: item.name || "",
                    gender: item.gender || "",
                    job: item.job || "",
                    org_fullname: item.org_fullname || "",
                    org_type: parseMultiSelect(item.org_type, orgTypeOptions),
                    phone: item.phone || "",
                    crash_contact: item.crash_contact || "",
                    email: item.email || "",
                    inviter: item.inviter || "",
                    follow_number: item.follow_number || "",
                    food_ban: parseMultiSelect(item.food_ban, foodBanOptions),
                    health_need: parseMultiSelect(item.health_need, healthNeedOptions),
                    transport_need: parseMultiSelect(item.transport_need, transportNeedOptions),
                    room_need: parseMultiSelect(item.room_need, roomNeedOptions),
                    culture: parseMultiSelect(item.culture, cultureOptions),
                    social_hobby: parseMultiSelect(item.social_hobby, socialHobbyOptions),
                    follow_help_number: item.follow_help_number || "",
                    follow_help_list: item.follow_help_list || "",
                    follow_translate_number: item.follow_translate_number || "",
                    follow_translate_list: item.follow_translate_list || "",
                    follow_mate_number: item.follow_mate_number || "",
                    follow_mate_list: item.follow_mate_list || "",
                    business_need: item.business_need || "",
                    media: parseMultiSelect(item.media, mediaOptions),
                    id_hide: parseMultiSelect(item.id_hide, idHideOptions),
                    vip_room: parseMultiSelect(item.vip_room, vipRoomOptions),
                    scheduleValue: item.scheduleValue ? item.scheduleValue.join("；") : "",
                    others: item.others || "",
                    photo: item.photo || "",
                    social_hobby_value4: item.social_hobby_value4 || "",
                }));

                exportToExcel(
                    processedData,
                    [
                        { key: "call", title: "称谓" },
                        { key: "name", title: "姓名" },
                        { key: "gender", title: "性别" },
                        { key: "job", title: "职位" },
                        { key: "follow_number", title: "随同人数" },
                        { key: "inviter", title: "邀约人" },
                        { key: "org_fullname", title: "机构全称" },
                        { key: "org_type", title: "机构类型" },
                        { key: "phone", title: "联系方式" },
                        { key: "crash_contact", title: "紧急联系人" },
                        { key: "email", title: "电子邮箱" },
                        { key: "photo", title: "照片" },
                        { key: "food_ban", title: "餐饮禁忌" },
                        { key: "health_need", title: "健康需求" },
                        { key: "transport_need", title: "交通安排" },
                        { key: "room_need", title: "住宿偏好" },
                        { key: "culture", title: "宗教/文化" },
                        { key: "social_hobby", title: "社交偏好" },
                        { key: "follow_help_number", title: "随行人员数量" },
                        { key: "follow_help_list", title: "随行人员名单" },
                        { key: "business_need", title: "商务对接需求" },
                        { key: "media", title: "媒体互动" },
                        { key: "id_hide", title: "证件信息隐藏" },
                        { key: "vip_room", title: "贵宾休息室使用权" },
                        { key: "scheduleValue", title: "行程安排" },
                        { key: "others", title: "其它" },
                        { key: "social_hobby_value4", title: "其他个性化服务需求" },
                    ],
                    `活动报名表_${dayjs().format("YYYYMMDD")}.xlsx`
                );
            },
        });
    };

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="活动报名"
                desc="查询活动报名"
            />

            {tab === "activities" && (
                <PageActionButtons
                    buttons={pageButtons}
                    className="tw-fixed tw-right-6"
                />
            )}

            <Tabs
                value={tab}
                onChange={(value) => {
                    setTab(value as TabValue);
                    setActivityId(""); // 切换 tab 时，清空活动 ID，先这样处理
                }}
            >
                <Tabs.List>
                    {TAB_OPTIONS.map(({ value, label }) => (
                        <Tabs.Tab
                            key={value}
                            value={value}
                            className={tab === value ? "tw-bg-basic-5 tw-text-white" : ""}
                            disabled={value === "signups" && !activityId}
                        >
                            {label}
                        </Tabs.Tab>
                    ))}
                </Tabs.List>

                <Tabs.Panel value="activities">
                    <Space h={10} />
                    <DataTable
                        ref={activityTableRef}
                        data={activityData}
                        columns={activityColumns as any}
                        totalCount={activityTotalCount}
                        loading={activityLoading}
                        onFetch={handleActivityFetch}
                        globalFilterFields={[
                            {
                                field: "keyword",
                                label: "搜索关键字",
                                type: "text",
                            },
                        ]}
                    />

                    <Info onUpdateSuccess={refreshActivityTable} />
                </Tabs.Panel>
                <Tabs.Panel value="signups">
                    <Space h={10} />
                    <DataTable
                        ref={signupTableRef}
                        data={signupData}
                        columns={signupColumns as any}
                        totalCount={signupTotalCount}
                        loading={signupLoading}
                        onFetch={handleSignupFetch}
                        onExport={handleSignupExport}
                        globalFilterFields={[
                            {
                                field: "keyword",
                                label: "搜索关键字",
                                type: "text",
                            },
                        ]}
                    />

                    <SignUpInfo />
                </Tabs.Panel>
            </Tabs>
        </Stack>
    );
};

export default AdminActivityPage;
