import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const examQuestion = {
    list: async (params: {
        page?: number;
        page_size?: number;
        get_type?: string; // random
        type?: string; // 0: 单选题, 1: 多选题
        exam_id?: number;
    }) => {
        const { error, result } = await cnaRequest<TExamQuestionListReponse>(
            "/api/v1/admin/exam-questions",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (params: TExamQuestionStoreOrUpdateParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/exam-questions/save",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (id: number, params: TExamQuestionStoreOrUpdateParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/exam-questions/save/${id}`,
            "PUT",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    destroy: async (id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/exam-questions/destroy/${id}`,
            "DELETE"
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default examQuestion;
