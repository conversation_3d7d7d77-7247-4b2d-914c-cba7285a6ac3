import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import {
    Group,
    Modal,
    Stack,
    Switch,
    Select,
    Button,
    Input,
    Text,
} from "@mantine/core";
import { Check, X } from "@phosphor-icons/react";
import { useRequest } from "ahooks";
import { useContext, useState } from "react";
import noty from "@code.8cent/react/noty";
import SettingPageContext from "../context";
import { useShallow } from "zustand/react/shallow";
import { cnaRequest } from "@code.8cent/utils";

const notificationsList = [
    {
        label: "setting.info_notify.switch.safe_setting",
        value: "settingNotifySafeUpdated",
    },
    {
        label: "setting.info_notify.switch.emergency_notice",
        value: "settingNotifyEmergency",
    },
    {
        label: "setting.info_notify.switch.dubious_action",
        value: "settingNotifySuspiciousOperation",
    },
    {
        label: "setting.info_notify.switch.aceept_letter",
        value: "settingNotifyRecPrivateMsg",
    },
    {
        label: "setting.info_notify.switch.importanct_update",
        value: "settingNotifyImportanceUpdate",
    },
    {
        label: "setting.info_notify.switch.join",
        value: "settingNotifyJoinInvestigate",
    },
    {
        label: "setting.info_notify.switch.update",
        value: "settingNotifySystemUpdate",
    },
];
const SettingNotificationModal = () => {
    const { SettingNotificationModal: show, close } =
        useContext(SettingPageContext);

    const lang = useSettingStore.use.lang();

    const setting = useSettingStore(
        useShallow((state) => {
            console.log(state);
            return {
                settingNotifyEmergency: state.settingNotifyEmergency,
                settingNotifyImportanceUpdate:
                    state.settingNotifyImportanceUpdate,
                settingNotifyJoinInvestigate:
                    state.settingNotifyJoinInvestigate,
                settingNotifyRecPrivateMsg: state.settingNotifyRecPrivateMsg,
                settingNotifySafeUpdated: state.settingNotifySafeUpdated,
                settingNotifySuspiciousOperation:
                    state.settingNotifySuspiciousOperation,
                settingNotifySystemUpdate: state.settingNotifySystemUpdate,
            };
        })
    );

    const updateSetting = useSettingStore.use.updateSetting();

    const { run: updateUserSetting, loading: saving } = useRequest(
        async () => {
            const { result, error } = await cnaRequest(
                "/api/v1/setting/notification/update",
                "POST",
                {
                    settingNotifyType: 1,
                    ...setting,
                }
            );

            if (!error) {
                noty.success(
                    t("notification.user.settings", lang),
                    t("notification.settings.saved.success", lang)
                );
            } else {
                noty.error(
                    t("notification.user.settings", lang),
                    error.message
                );
            }
        },
        {
            manual: true,
        }
    );
    const handleSwitchChange = (key, checked: boolean) => {
        updateSetting(key, checked ? 1 : 0);
    };

    return (
        <Modal
            opened={show}
            onClose={() => close("SettingNotificationModal")}
            title={t("setting.info_notification", lang)}
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-6">
                <Select
                    label={t("setting.info_notify.label.notify_type", lang)}
                    data={[
                        {
                            value: "email",
                            label: t("project.company_form.label.mail", lang),
                        },
                        // {
                        //     value: "sms",
                        //     label: t("notification.message", lang),
                        // },
                    ]}
                    value="email"
                />
                <Input.Wrapper label={t("system.notification", lang)}>
                    <Stack>
                        {notificationsList.map((item) => {
                            return (
                                <Group
                                    className="tw-border tw-border-gray-300 tw-p-2 tw-py-3 tw-rounded tw-justify-between"
                                    key={item.value}
                                >
                                    <Text size="sm">{t(item.label, lang)}</Text>
                                    <Switch
                                        checked={
                                            setting[item.value] === 1
                                                ? true
                                                : false
                                        }
                                        onChange={(e) =>
                                            handleSwitchChange(
                                                item.value,
                                                e.target.checked
                                            )
                                        }
                                        size="sm"
                                        color="basic.4"
                                    />
                                </Group>
                            );
                        })}
                    </Stack>
                </Input.Wrapper>
            </Stack>
            <Group justify="end" className="tw-border-t tw-pt-4 tw-mt-4">
                <CnaButton
                    onClick={updateUserSetting}
                    loading={saving}
                    color="basic"
                    leftSection={<Check weight="bold" />}
                >
                    {t("common.save", lang)}
                </CnaButton>
                <Button
                    color="basic"
                    variant="outline"
                    onClick={() => close("SettingNotificationModal")}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </Button>
            </Group>
        </Modal>
    );
};

export default SettingNotificationModal;
