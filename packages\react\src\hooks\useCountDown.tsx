import { useEffect, useState } from "react";
import { useCountDown as __useCountDown } from "ahooks";

const useCountdown = () => {
    const [countSec, setCountSec] = useState<number>(0);

    const [countdown] = __useCountDown({
        targetDate: countSec,
    });

    const startCountdown = (seconds: number = 60) => {
        setCountSec(Date.now() + seconds * 1000);
    };

    return { countdown, startCountdown };
};

export default useCountdown;
