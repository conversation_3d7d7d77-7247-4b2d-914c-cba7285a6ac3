import useModalStore from "@/store/modal";
import { Center, Modal, Stack, Text, Group } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { Check, FilePdf, FileText, Image, X } from "@phosphor-icons/react";
import { useEffect, useState } from "react";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { filesize } from "filesize";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

const UploadModal = () => {
    const { show, title, accept, maxSize, required } =
        useModalStore.use.upload();

    const close = useModalStore.use.closeUpload();
    const lang = useSettingStore.use.lang();

    const [files, setFiles] = useState<File[]>([]);

    useEffect(() => {
        if (show === false) {
            setFiles([]);
        }
    }, [show]);

    console.log(files[0]);

    return (
        <Modal
            zIndex={1116}
            opened={show}
            title={title}
            onClose={close}
            size="lg"
        >
            <Stack>
                <Center>
                    <FilePdf
                        size={72}
                        weight="light"
                        className="tw-text-[#666]"
                    />
                </Center>

                <Dropzone
                    onDrop={(_files) => {
                        setFiles(_files);
                    }}
                    multiple={false}
                    accept={accept}
                >
                    <Center>
                        <Stack>
                            <FileText
                                size={36}
                                className="tw-text-dimmed tw-mx-auto tw-mb-3"
                            />
                            <Text size="sm" c="dimmed">
                                {files[0]
                                    ? files[0].name
                                    : t("upload.files", lang)}
                            </Text>
                        </Stack>
                    </Center>
                </Dropzone>

                <Stack>
                    {required === true && (
                        <Group gap={4}>
                            {files[0] instanceof File === true ? (
                                <Check size={24} className="tw-text-green-600"/>
                            ) : (
                                <X size={24} className="tw-text-red-600" />
                            )}
                            <Text size="sm" c="dimmed">{t("upload.files.requirement", lang)}</Text>
                        </Group>
                    )}
                    <Group gap={4}>
                        {files[0] instanceof File === true &&
                        files[0].size <= maxSize ? (
                            <Check size={24} className="tw-text-green-600"/>
                        ) : (
                            <X size={24} className="tw-text-red-600" />
                        )}
                        <Text size="sm" c="dimmed">
                        {t("upload.files.exceed", lang)}{filesize(maxSize, { standard: "jedec" })}
                        </Text>
                    </Group>
                    <Group gap={4}>
                        {files[0] instanceof File === true &&
                        accept.includes(files[0].type) === true ? (
                            <Check size={24} className="tw-text-green-600"/>
                        ) : (
                            <X size={24} className="tw-text-red-600" />
                        )}
                        <Text size="sm" c="dimmed">{t("upload.files.format", lang)}{accept.join(" ")}</Text>
                    </Group>
                </Stack>
            </Stack>

            <Group justify="end" className="tw-border-t tw-pt-4 tw-mt-4">
                <CnaButton color="cna" leftSection={<Check />} >
                {t("common.submit", lang)}
                </CnaButton>
                <CnaButton
                    color="rgb(242, 242, 242)"
                    c="dark"
                    leftSection={<X />}
                    onClick={close}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default UploadModal;
