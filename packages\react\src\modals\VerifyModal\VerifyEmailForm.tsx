import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { zodResolver } from "@hookform/resolvers/zod";
import { TextInput } from "@mantine/core";
import { useCountDown, useMemoizedFn, useRequest } from "ahooks";
import {
    useRef,
    useImperativeHandle,
    forwardRef,
    ForwardRefExoticComponent,
    Ref,
    RefAttributes,
    useState,
} from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import CnaButton from "../../components/common/CnaButton";
import { cnaRequest } from "@code.8cent/utils";
import noty from "../../library/noty";
import useVerifyModalStore from "./store";

export type EmailVerifyInput = {
    code: string;
    email: string;
};

const emailVerifySchema = z.object({
    code: z.string().min(1, "Verify code is required"),
    email: z.string().email("form.email.incorrect"),
});

const VerifyEmailForm: ForwardRefExoticComponent<
    {
        onSuccess?: (data: EmailVerifyInput) => void;
        isUpdate?: boolean;
        initialEmail?: string;
        getCodeUrl: string;
        verifyUrl: string;
        countdownID: string;
        startCountdown: (seconds?: number) => void;
    } & RefAttributes<SharedFormRef<EmailVerifyInput>>
> = forwardRef(
    (
        {
            onSuccess,
            initialEmail = "",
            isUpdate = false,
            getCodeUrl,
            verifyUrl,
            countdownID,
            startCountdown,
        },
        ref: Ref<SharedFormRef<EmailVerifyInput>>
    ) => {
        const state = useVerifyModalStore();

        const { lang } = useSettingStore();

        const formRef = useRef<HTMLFormElement>();

        const {
            register,
            handleSubmit,
            formState: { errors },
            getValues,
            trigger,
            reset,
        } = useForm<EmailVerifyInput>({
            resolver: zodResolver(emailVerifySchema),
            defaultValues: {
                email: initialEmail,
                code: "",
            },
        });

        const { run: verify, loading: verifying } = useRequest(
            async (data: EmailVerifyInput) => {
                const { result, error } = await cnaRequest(verifyUrl, "POST", {
                    email: data.email,
                    code: data.code,
                });

                const successTitle = `${t(
                    isUpdate === true
                        ? "update.email.success"
                        : "verify.email.success",
                    lang
                )}`;

                const failTitle = `${t(
                    isUpdate === true
                        ? "update.email.fail"
                        : "verify.email.fail",
                    lang
                )}`;

                if (!error === true) {
                    noty.success(t(successTitle, lang));

                    if (typeof onSuccess === "function") {
                        onSuccess(data);
                    }
                } else {
                    noty.error(t(failTitle, lang), error.message);
                }
            },
            {
                manual: true,
            }
        );

        const submit = useMemoizedFn(() => {
            handleSubmit(verify)();
        });

        useImperativeHandle(ref, () => ({
            reset,
            submit,
        }));

        const { run: getEmailCode, loading: requestingEmailCode } = useRequest(
            async () => {
                let isEmailOk = await trigger("email");

                if (isEmailOk === true) {
                    let { error } = await cnaRequest(getCodeUrl, "POST", {
                        email: getValues("email"),
                    });

                    if (!error) {
                        startCountdown(60);
                    } else {
                        noty.error(error.message);
                    }
                }
            },
            {
                manual: true,
            }
        );

        const handleKeyDown = useMemoizedFn(
            (event: React.KeyboardEvent<HTMLFormElement>) => {
                if (event.key === "Enter") {
                    submit();
                }
            }
        );

        return (
            <form onSubmit={submit} onKeyDown={handleKeyDown} ref={formRef}>
                <TextInput
                    label={t("login.label.email", lang)}
                    placeholder={t("login.placeholder.email", lang)}
                    className="tw-mb-3"
                    rightSectionWidth={120}
                    rightSection={
                        state[`countdown_${countdownID}`] > 0 ? (
                            <CnaButton
                                variant="transparent"
                                color="dark.5"
                                className="tw-cursor-not-allowed"
                            >
                                {`${Math.floor(
                                    state[`countdown_${countdownID}`] / 1000
                                )} ${t("validate.after.second", lang)}`}
                            </CnaButton>
                        ) : (
                            <CnaButton
                                variant="transparent"
                                color="dark.5"
                                onClick={getEmailCode}
                                className="!tw-bg-transparent tw-w-full"
                                loading={requestingEmailCode}
                            >
                                {t(
                                    "forget_password.btn.send_validate_code",
                                    lang
                                )}
                            </CnaButton>
                        )
                    }
                    {...register("email")}
                    autoComplete="new-password"
                    readOnly={isUpdate === true ? false : true}
                    error={errors.email ? true : false}
                />
                <TextInput
                    className="tw-mb-3"
                    error={errors.code ? true : false}
                    label={t("validation.code", lang)}
                    placeholder={t("validation.enter.code", lang)}
                    autoComplete="off"
                    {...register("code")}
                />
            </form>
        );
    }
);

export default VerifyEmailForm;
