declare global {
    type TTeamSearchParams = {
        keyword: string | null;
    } & TPageQueryParams;

    type TTeam = {
        id: number;
        group: number;
        pre_id: number;
        rank: number;
        rankName: string;
        name: string;
        email: string;
        phone: string;
        created_at: string;
        updated_at: string;
    }

    type TTeamStoreParams = {
        name: string;
        email: string;
        phone: string;
    };

    type TRankOption = {
        value: string;
        label: string;
    };

    type TTeamsResponse = {
        items: TTeam[];
        paginate: BasePaginateResponse;
    };
}

export {};
