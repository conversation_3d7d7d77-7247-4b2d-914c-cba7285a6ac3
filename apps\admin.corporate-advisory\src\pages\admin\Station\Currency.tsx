import currencyApi from "@/apis/station/currency";
import { Stack } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { Plus } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import { createColumnHelper } from "@tanstack/react-table";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import CurrencyModal from "@/components/modals/station/Currency";

const columnHelper = createColumnHelper<TCurrency>();

const AdminCurrencyPage = () => {
    const lang = useSettingStore.use.lang();
    const openModal = useModalStore.use.open();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const pageButtons = [
        {
            key: "add",
            leftSection: (
                <Plus
                    weight="bold"
                    size={14}
                />
            ),
            label: "新增货币",
            onClick: () => openModal("currencyModal"),
        },
    ];

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("currency", {
            header: "货币名称",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("currency_code", {
            header: "货币代码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("rate", {
            header: "汇率",
            enableSorting: false,
            cell: (info) => info.getValue() || "-",
        }),
        columnHelper.accessor("is_default", {
            header: "默认货币",
            enableSorting: false,
            cell: (info) => (
                <span
                    className={`tw-px-2 tw-py-1 tw-rounded tw-text-xs ${
                        info.getValue() === 1
                            ? "tw-bg-green-100 tw-text-green-800"
                            : "tw-bg-gray-100 tw-text-gray-600"
                    }`}
                >
                    {info.getValue() === 1 ? "是" : "否"}
                </span>
            ),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        }),
    ];

    const rowActions = (row: TCurrency) => [
        {
            key: "edit",
            label: "编辑",
            onClick: () => openModal("currencyModal", { currency: row }),
        },
        {
            key: "delete",
            label: "删除",
            onClick: () => handleDelete(row),
        },
    ];

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize } = params;

            const requestParams = {
                page,
                page_size: pageSize,
            };

            const result = await currencyApi.list(requestParams);
            if (result) {
                setData(result.items || []);
                setTotalCount(result.paginate?.total || 0);
            }
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = useMemoizedFn(async (currency: TCurrency) => {
        // 这里可以添加删除确认逻辑
        console.log("删除货币:", currency);
    });

    // 刷新表格
    const refreshTable = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="货币管理"
                desc="管理系统货币配置"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
            />

            {/* 渲染货币新增/编辑弹窗 */}
            <CurrencyModal onUpdateSuccess={refreshTable} />
        </Stack>
    );
};

export default AdminCurrencyPage;
