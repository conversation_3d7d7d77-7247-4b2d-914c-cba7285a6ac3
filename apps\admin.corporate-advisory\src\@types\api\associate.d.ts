declare global {
    type TAssociateSearchParams = {
        status: string; // 0: pending, 1: approved, 2: rejected
        keyword: string | null;
        sortParams?: string;
    } & TPageQueryParams;

    type TReviewAssociateParams = {
        id: number;
        status: number;
        review_content?: string;
    }

    type TUpdateSettingParams = {
        settingLanguage?: string;
        settingTimezone?: string;
        settingDateFormat?: string;
        settingTimeFormat?: string;
        settingCurrency?: string;
        notifyType: number;
        notifySafeUpdated: number;
        notifySuspiciousOperation: number;
        notifyRecPrivateMsg: number;
        notifyImportanceUpdate: number;
        notifyJoinInvestigate: number;
        notifySystemUpdate: number;
        notifyEmergency: number;
        notifyBill: number;
    }

    type AssociatesResponse = {
        items: UserProfileResponse[];
        paginate: BasePaginateResponse;
    };
}

export {};
