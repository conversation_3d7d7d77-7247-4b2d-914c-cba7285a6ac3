declare global {
    type UserLoginResponse = {
        token: string;
        state?: number;
    };

    type UserProfileResponse = {
        profileID: number;
        mobilePrefixID: number;
        profileName: string;
        profileNRIC: string;
        profileGender: "M" | "F";
        profileNationality: string;
        profileNationalityID: number;
        profileBirthDate: string;
        profileRole: string;
        profileEmail: string;
        profileEmailValidate: "Y" | "N";
        profileContact: string;
        profileContactValidate: "Y" | "N";
        profileAddressUnit: string;
        profileAddressStreet: string;
        profileAddressCity: string;
        profileAddressState: string;
        profileAddressPostcode: string;
        profileAddressCountry: string;
        profileStatus: string;
        profilePartnerCode: string;
        profileAvatar: string;
        profileLastName: string;
        profileGivenName: string;
        settingCurrency: string;
        settingDateFormat: string;
        settingLanguage: LangCode;
        settingTimeFormat: string;
        settingTimezone: string;
        settingNotifyEmergency: number;
        settingNotifyImportanceUpdate: number;
        settingNotifyJoinInvestigate: number;
        settingNotifyRecPrivateMsg: number;
        settingNotifySafeUpdated: number;
        settingNotifySuspiciousOperation: number;
        settingNotifySystemUpdate: number;
        settingNotifyType: number;
        userProfessional: {
            professionalID: number;
            professionalDescription: string;
        }[];
        userExperience: {
            experienceID: number;
            experienceDescription: string;
        }[];
        userSkill: {
            skillID: number;
            skillDescription: string;
        }[];
        created_at: string;
    } & BaseOperateResponse;

    type MemberListResponse = {
        items: (UserProfileResponse & {
            match_word: string;
            company: { companyName: string }[];
        })[];
        paginate: BasePaginateResponse;
    };
}

export {};
