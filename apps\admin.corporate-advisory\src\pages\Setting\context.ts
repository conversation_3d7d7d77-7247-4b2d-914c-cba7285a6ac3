import React, { createContext, useContext, ReactNode, useState } from "react";

export type SettingModalState = {
    SettingActivityModal: boolean;
    SettingRegionModal: boolean;
    SettingNotificationModal: boolean;
    SettingLoginModal: boolean;
    SettingSupportModal: boolean;
    SettingDefaultModal: boolean;
    SettingPasswordModal: boolean;
};

type SettingPageContextProps = SettingModalState & {
    open: (modal: keyof SettingModalState) => void;
    close: (modal: keyof SettingModalState) => void;
};

export const initialState: SettingModalState = {
    SettingActivityModal: false,
    SettingRegionModal: false,
    SettingNotificationModal: false,
    SettingLoginModal: false,
    SettingSupportModal: false,
    SettingDefaultModal: false,
    SettingPasswordModal: false,
};

const SettingPageContext = createContext<SettingPageContextProps>({
    ...initialState,
    open: () => {},
    close: () => {},
});


export default SettingPageContext;
