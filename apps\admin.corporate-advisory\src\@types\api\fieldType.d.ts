declare global {

    type TaddFieldItemParam = {
        industry_name: string;
        market_code: string;

    }

    type TFieldItemResponse = {
        created_at: string;
        deleted_at: string
        id: number
        market_code: string
        name: string
        number: string
        updated_at: string
        userInfo: null
        user_id: number
    }
    type TFieldTypeResponse = {
        items: TFieldItemResponse[];
        paginate: BasePaginateResponse;
    };

    type TFieldItemListResponse = {
        id: number;
        market_code: string
        name: string
        number: string
        updated_at: string
        user_id: number
        info: any;
        levelList: any;
        profileID?: string
        profileName?: string
        profileContact?: string
        profileEmail?: string
        teamName: string;
        link: string
    }
}

export { };
