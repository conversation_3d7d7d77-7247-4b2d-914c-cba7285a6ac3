import api from "@/apis";
import { Group, Stack } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import { PageHeader, DataTable, DataTableRef, CnaButton } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import VipInfo from "@/components/modals/visitor/VipInfo";
import { exportToExcel } from "@/utils/xlsx";
import { exportToPDF } from "@/utils/pdf";
import { downloadBlob } from "@/utils/files";

const columnHelper = createColumnHelper<TVisitorVip>();

const YES_OR_NO_MAP = {
    1: "否",
    2: "是",
};

const PLACE_MAP = {
    1: "官方会议",
    2: "会所参观",
    3: "商务沟通",
    4: "其他",
};

const AdminVisitorVipsPage = () => {
    const { lang } = useSettingStore();
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState<TVisitorVip[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    // 下载邀请函
    const downloadInvitationCard = useMemoizedFn(async (row: TVisitorVip) => {
        try {
            const res = await api.visitor.downloadInvitationCard({
                id: row.id,
                type: row.lang === "en" ? "en" : "cn",
                form: "vip",
            });
            if (res) {
                downloadBlob(res?.data, "邀请函.jpg");
            }
        } catch (error) {
            console.error("下载邀请函失败:", error);
        }
    });

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "序号",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("invite_people", {
            header: "邀约人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("name", {
            header: "姓名",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("visitor_date", {
            header: "来访日期",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("visitor_time", {
            header: "来访时间",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("leave_time", {
            header: "离开时间",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("phone", {
            header: "手机号码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("created_at", {
            header: "创建日期",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => (
                <Group>
                    <CnaButton
                        variant="outline"
                        size="xs"
                        onClick={() => viewVipInfo(info.row.original)}
                    >
                        详情
                    </CnaButton>
                    <CnaButton
                        variant="outline"
                        size="xs"
                        onClick={() => downloadInvitationCard(info.row.original)}
                    >
                        下载邀请函
                    </CnaButton>
                </Group>
            ),
        }),
    ];

    // 贵宾详情
    const viewVipInfo = useMemoizedFn(async (vip: TVisitorVip) => {
        openModal("visitorVipInfoModal", { visitorInfo: vip });
    });

    const handleFetch = useMemoizedFn(async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                start_date: globalFilters?.dateRange?.[0] || "",
                end_date: globalFilters?.dateRange?.[1] || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.visitor.vipList(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    });

    // 导出数据
    const handleExport = useMemoizedFn(async () => {
        const { globalFilters } = tableRef.current?.getState();

        const requestParams = {
            keyword: globalFilters?.keyword || "",
            start_date: globalFilters?.dateRange?.[0] || "",
            end_date: globalFilters?.dateRange?.[1] || "",
            page: 1,
            pageSize: totalCount,
        };

        const { items } = await api.visitor.vipList(requestParams);

        exportToExcel(
            items,
            [
                { key: "id", title: "序号" },
                { key: "invite_people", title: "邀约人" },
                { key: "name", title: "姓名" },
                { key: "phone", title: "手机号码" },
                { key: "visitor_date", title: "来访日期" },
                { key: "visitor_time", title: "来访时间" },
                { key: "leave_time", title: "离开时间" },
                { key: "numbers", title: "人数" },
                { key: "visitor_name_list", title: "访客名单" },
                { key: "company_information", title: "单位信息" },
                {
                    key: "is_read_cna",
                    title: "是否阅读C&A",
                    format: (value) => YES_OR_NO_MAP[value],
                },
                { key: "visitor", title: "拜访对象" },
                { key: "matters", title: "洽谈事宜" },
                { key: "place", title: "到访目的", format: (value) => PLACE_MAP[value] },
                { key: "expect", title: "期望" },
                {
                    key: "is_book_meeting",
                    title: "是否预定会议室",
                    format: (value) => YES_OR_NO_MAP[value],
                },
                {
                    key: "is_book_meeting",
                    title: "是否预定会议室",
                    format: (value) => YES_OR_NO_MAP[value],
                },
                { key: "book_detail", title: "会议室预定详情" },
                { key: "is_food", title: "是否预定餐食", format: (value) => YES_OR_NO_MAP[value] },
                { key: "food_preference", title: "餐食偏好" },
                { key: "remark", title: "备注" },
                {
                    key: "created_at",
                    title: "创建日期",
                    format: (value) => dayjs(value).format("YYYY-MM-DD HH:mm:ss"),
                },
            ],
            `C&A贵宾登记记录_${dayjs().format("YYYYMMDD")}.xlsx`
        );
    });

    const confirmExport = useMemoizedFn(async () => {
        openConfirm({
            title: "提示",
            message: "确定要导出数据吗？",
            onConfirm: handleExport,
        });
    });

    // 导出PDF
    const handleExportPDF = useMemoizedFn(async () => {
        const selectedRows = tableRef.current?.getSelectedRows();
        if (!selectedRows?.length) {
            openConfirm({
                title: "提示",
                message: "请先选择要导出的数据",
                onConfirm: () => {},
            });
            return;
        }

        await exportToPDF({
            title: "C&A贵宾登记记录",
            data: selectedRows,
            fileName: "C&A贵宾登记记录",
            fields: [
                { key: "invite_people", label: "邀约人" },
                { key: "name", label: "申请人" },
                { key: "phone", label: "联系电话" },
                { key: "visitor_date", label: "到访日期" },
                { key: "visitor_time", label: "到访时间" },
                { key: "leave_time", label: "离开时间" },
                { key: "numbers", label: "到访人数" },
                { key: "company_information", label: "单位/职务" },
                {
                    key: "is_read_cna",
                    label: "是否已了解C&A",
                    format: (value) => YES_OR_NO_MAP[value],
                },
                { key: "visitor", label: "拜访对象" },
                { key: "place", label: "到访目的", format: (value) => PLACE_MAP[value] },
                { key: "is_food", label: "是否用餐", format: (value) => YES_OR_NO_MAP[value] },
                {
                    key: "is_book_meeting",
                    label: "是否需要预订会议室/预订任何设施",
                    format: (value) => YES_OR_NO_MAP[value],
                },
                { key: "food_preference", label: "饮食偏好/忌口" },
                { key: "visitor_name_list", label: "人员名单" },
                { key: "company_information", label: "企业资料" },
                { key: "matters", label: "洽谈事项" },
                { key: "expect", label: "您的期待是什么" },
                { key: "book_detail", label: "预订详细请求" },
                { key: "remark", label: "备注" },
                {
                    key: "created_at",
                    label: "创建日期",
                    format: (value) => (value ? dayjs(value).format("YYYY-MM-DD HH:mm:ss") : ""),
                },
            ],
        });
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="C&A贵宾登记记录"
                desc="查询C&A贵宾登记记录"
            />

            <DataTable
                ref={tableRef}
                columns={tableColumns as any}
                data={data}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={confirmExport}
                enableMultiSelect
                selectionActions={
                    <CnaButton
                        size="xs"
                        onClick={handleExportPDF}
                    >
                        导出PDF
                    </CnaButton>
                }
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                    {
                        field: "dateRange",
                        label: "来访日期",
                        type: "dateRange",
                    },
                ]}
            />

            <VipInfo />
        </Stack>
    );
};

export default AdminVisitorVipsPage;
