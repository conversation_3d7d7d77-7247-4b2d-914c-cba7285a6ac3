declare global {
    type TNoticeSearchParams = {
        status?: string;
        keyword: string | null;
    } & BasePaginateParams;

    type TNotice = {
        notificationID: number;
        notificationTitleZH: string;
        notificationDescriptionZH: string;
        notificationTitleZT: string;
        notificationDescriptionZT: string;
        notificationTitleEN: string;
        notificationDescriptionEN: string;
        notificationTitleMS: string;
        notificationDescriptionMS: string;
        notificationState: number;
        notificationDepartment: string;
        notificationViews: number;
        notificationTarget: number;
        notificationValidity: string;
        createUser: number;
        createRole: number;
        createTime: string;
        editUser: number;
        editRole: number;
        editTime: string;
        created_at: string;
        updated_at: string;
    };

    type TStoreNoticeParams = {
        type: string;
        notificationTitleZH: string;
        notificationDescriptionZH: string;
        notificationTitleEN: string;
        notificationDescriptionEN: string;
        notificationTitleZT: string;
        notificationDescriptionZT: string;
        notificationTitleMS: string;
        notificationDescriptionMS: string;
        notificationValidity: string;
        createTime: string;
    };

    type TUpdateNoticeParams = Omit<TStoreNoticeParams, "createTime" | "notificationValidity"> & {
        createTime?: string;
        notificationValidity?: string;
    };

    type TNoticesResponse = {
        items: TNotice[];
        paginate: BasePaginateResponse;
    }
}

export {};
