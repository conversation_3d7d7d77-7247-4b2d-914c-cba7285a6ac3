declare global {
    type TDigitalHumanSearchParams = {
        keyword: string | null;
    } & TPageQueryParams;

    type TDigitalHuman = {
        id: number;
        profile_id: string;
        pic: string;
        pic1: string;
        pic2: string;
        audio: string;
        video: string;
        created_at: string;
        updated_at: string;
        status: number;
        remark?: string;
        finished_files?: string;
        profile: {
            profileID: number;
            profileName: string;
            profilePartnerCode: string;
        }
    }

    type TDigitalHumanResponse = {
        items: TDigitalHuman[];
        paginate: BasePaginateResponse;
    };
}

export {};
