import {
    Table,
    Group,
    Pagination,
    Menu,
    ActionIcon,
    TextInput,
    Stack,
    Checkbox,
    Select,
    Text,
    NumberInput,
    ScrollArea,
    LoadingOverlay,
    Tooltip,
    Grid,
} from "@mantine/core";
import {
    CaretDown,
    CaretUp,
    CaretUpDown,
    GearSix,
    DownloadSimple,
    SplitVertical,
    ArrowsOut,
    ArrowsIn,
} from "@phosphor-icons/react";
import {
    useReactTable,
    getCoreRowModel,
    getPaginationRowModel,
    ColumnDef,
    flexRender,
    SortingState,
    ColumnFiltersState,
    VisibilityState,
    getSortedRowModel,
} from "@tanstack/react-table";
import { forwardRef, useEffect, useState, useImperativeHandle } from "react";
import { DatePickerInput, DateTimePicker } from "@mantine/dates";
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import { useDebounce, useMemoizedFn } from "ahooks";
import CnaButton from "@code.8cent/react/components/CnaButton";
import noty from "@code.8cent/react/noty";

// 扩展tanstack/react-table的ColumnMeta，添加enableTooltip属性
declare module "@tanstack/react-table" {
    interface ColumnMeta<TData extends unknown, TValue> {
        enableTooltip?: boolean;
    }
}

interface DataTableProps<T extends object> {
    columns: ColumnDef<T, any>[]; // 列定义
    data: T[]; // 数据
    totalCount: number; // 总条数
    enableMultiSelect?: boolean; // 是否启用多选
    selectionActions?: React.ReactNode; // 多选后操作组
    filterTypes?: Record<string, "text" | "select" | "numberRange" | "dateRange" | "datetimeRange">; // 筛选类型
    filterOptions?: Record<string, { label: string; value: any }[]>; // 筛选选项
    loading?: boolean; // 是否显示加载中
    onFetch: (params: {
        page: number;
        pageSize: number;
        sorting: SortingState;
        columnFilters: ColumnFiltersState;
        globalFilters: Record<string, any>;
    }) => Promise<void>; // 数据获取
    onExport?: () => Promise<void>; // 导出
    defaultDensity?: "xs" | "sm" | "md"; // 表格密度
    serverSideSort?: boolean; // 是否使用服务器端排序
    // table 顶部全局搜索字段
    globalFilterFields?: Array<{
        field: string;
        label: string;
        type?: "text" | "select" | "numberRange" | "date" | "dateRange";
        options?: Array<{ label: string; value: any }>;
    }>;
    getRowId?: (row: T) => string; // 获取行唯一标识的函数
}

export interface DataTableRef {
    // 刷新数据
    refresh: () => Promise<void>;
    // 获取表格状态（全局搜索、列筛选、排序）
    getState: () => {
        globalFilters: Record<string, any>;
        columnFilters: Array<{ id: string; value: any }>;
        sorting: Array<{ id: string; desc: boolean }>;
        pageSize: number;
    };
    // 获取选中的行
    getSelectedRows: () => any[];
}

interface DragResult {
    destination?: {
        index: number;
    };
    source: {
        index: number;
    };
}

const DENSITY_OPTIONS = [
    { value: "xs", label: "紧凑" },
    { value: "sm", label: "默认" },
    { value: "md", label: "宽松" },
] as const;

type DensityType = (typeof DENSITY_OPTIONS)[number]["value"];

const PAGE_SIZE_OPTIONS = [
    { value: "5", label: "5条/页" },
    { value: "10", label: "10条/页" },
    { value: "20", label: "20条/页" },
] as const;

export const DataTable = forwardRef<DataTableRef, DataTableProps<any>>(function DataTable<
    T extends object
>(
    {
        columns,
        data,
        totalCount,
        enableMultiSelect = false,
        selectionActions,
        filterTypes = {},
        filterOptions = {},
        loading = false,
        onFetch,
        onExport,
        defaultDensity = "sm",
        serverSideSort = true,
        globalFilterFields = [],
        getRowId = (row: any) => row.id, // 默认使用 id 作为唯一标识
    }: DataTableProps<T>,
    ref: React.Ref<DataTableRef>
) {
    // 状态管理
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
    const [rowSelection, setRowSelection] = useState({});
    const [columnOrder, setColumnOrder] = useState<string[]>([]);
    const [localColumnFilters, setLocalColumnFilters] = useState<Record<string, any>>({});

    const [localGlobalFilters, setLocalGlobalFilters] = useState<Record<string, any>>({});
    const [globalFilter, setGlobalFilter] = useState<Record<string, any>>({});

    const debouncedGlobalFilters = useDebounce(localGlobalFilters, { wait: 1000 });
    const debouncedLocalFilters = useDebounce(localColumnFilters, { wait: 1000 });

    // 行间距控制
    const [density, setDensity] = useState<DensityType>(defaultDensity);
    // 全屏控制
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isFirstRender, setIsFirstRender] = useState(true);

    // 存储所有选中的行ID
    const [selectedRowIds, setSelectedRowIds] = useState<Set<string>>(new Set());
    // Map 存储选中行的完整数据
    const [selectedRowsData] = useState<Map<string, T>>(new Map());

    useImperativeHandle(ref, () => ({
        // 刷新数据
        refresh: fetchData,
        // 获取表格状态（全局搜索、列筛选、排序）
        getState: () => ({
            globalFilters: debouncedGlobalFilters,
            columnFilters,
            sorting,
            pageSize: table.getState().pagination.pageSize,
        }),
        // 获取选中的行
        getSelectedRows: () => Array.from(selectedRowIds).map((id) => selectedRowsData.get(id)),
    }));

    useEffect(() => {
        const newColumnFilters = table
            .getAllLeafColumns()
            .filter((column) => filterTypes[column.id])
            .map((column) => ({
                id: column.id,
                value: debouncedLocalFilters[column.id] || undefined,
            }))
            .filter((filter) => filter.value !== undefined);

        setColumnFilters(newColumnFilters);
    }, [debouncedLocalFilters]);

    useEffect(() => {
        const newGlobalFilter = Object.entries(debouncedGlobalFilters).reduce(
            (acc, [key, value]) => {
                if (value !== undefined && value !== "") {
                    acc[key] = value;
                }
                return acc;
            },
            {} as Record<string, any>
        );
        setGlobalFilter(newGlobalFilter);
    }, [debouncedGlobalFilters]);

    const table = useReactTable({
        data,
        columns,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            globalFilter: debouncedGlobalFilters,
            rowSelection,
            columnOrder,
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onColumnVisibilityChange: setColumnVisibility,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true,
        manualFiltering: true,
        manualSorting: serverSideSort,
        enableRowSelection: enableMultiSelect,
        getRowId: (row) => getRowId(row),
        onRowSelectionChange: (updater) => {
            const oldState = rowSelection;
            const newState = typeof updater === "function" ? updater(oldState) : updater;

            setRowSelection(newState);

            // 更新selectedRowIds和selectedRowsData
            const currentPageRows = table.getRowModel().rows;
            const newSelectedIds = new Set(selectedRowIds);

            currentPageRows.forEach((row) => {
                const rowId = getRowId(row.original);
                if (newState[row.id]) {
                    newSelectedIds.add(rowId);
                    selectedRowsData.set(rowId, row.original);
                } else {
                    newSelectedIds.delete(rowId);
                    selectedRowsData.delete(rowId);
                }
            });

            setSelectedRowIds(newSelectedIds);
        },
        onColumnOrderChange: setColumnOrder,
        getSortedRowModel: serverSideSort ? undefined : getSortedRowModel(),
    });

    // 拖拽处理函数
    const handleDragEnd = useMemoizedFn((result: DragResult) => {
        if (!result.destination) return;

        const items = Array.from(table.getAllLeafColumns().map((d) => d.id));
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);

        table.setColumnOrder(items);
    });

    // 处理数据获取
    const fetchData = useMemoizedFn(async () => {
        try {
            await onFetch({
                page: table.getState().pagination.pageIndex + 1,
                pageSize: table.getState().pagination.pageSize,
                sorting,
                columnFilters,
                globalFilters: debouncedGlobalFilters,
            });
        } catch (error) {
            console.error("获取数据失败: ", error);
            noty.error("获取数据失败: " + error.message);
        }
    });

    const { pageIndex, pageSize } = table.getState().pagination;

    // 监听筛选条件变化
    useEffect(() => {
        if (isFirstRender) {
            return;
        }
        // 在使用服务器端排序时，排序变化才触发 fetchData todo fix
        if (!serverSideSort) {
            return;
        }
        table.resetPageIndex(); // 重置页码到第一页
        setRowSelection({});
        console.log("排序变化");
        fetchData();
    }, [sorting]);

    useEffect(() => {
        if (isFirstRender) {
            return;
        }
        table.resetPageIndex(); // 重置页码到第一页
        setRowSelection({});
        console.log("筛选变化");
        fetchData();
    }, [columnFilters, debouncedGlobalFilters]);

    // 单独处理分页变化
    useEffect(() => {
        if (isFirstRender) {
            return;
        }
        console.log("分页变化");
        fetchData();
    }, [pageIndex, pageSize]);

    // 初始化获取数据
    useEffect(() => {
        fetchData();
        setIsFirstRender(false);
    }, []);

    // 处理全屏切换
    const toggleFullscreen = () => {
        setIsFullscreen(!isFullscreen);
    };

    return (
        <Stack
            gap="md"
            className="tw-relative"
            style={{
                position: isFullscreen ? "fixed" : "relative",
                top: isFullscreen ? "0" : "auto",
                left: isFullscreen ? "0" : "auto",
                right: isFullscreen ? "0" : "auto",
                bottom: isFullscreen ? "0" : "auto",
                zIndex: isFullscreen ? 100 : "auto",
                background: "white",
                padding: isFullscreen ? "1rem" : "initial",
                height: isFullscreen ? "100%" : "auto",
                overflow: "auto",
                transition: "all 0.3s ease",
            }}
        >
            {/* 顶部全局搜索 */}
            {globalFilterFields.length > 0 && (
                <Stack className="tw-overflow-hidden">
                    <Grid gutter={10}>
                        {globalFilterFields.map(({ field, label, type = "", options = [] }) => (
                            <Grid.Col
                                key={field}
                                span={{ base: 12, md: 3 }}
                            >
                                {type === "text" && (
                                    <TextInput
                                        size="xs"
                                        placeholder={`${label}...`}
                                        value={localGlobalFilters[field] || ""}
                                        onChange={(e) =>
                                            setLocalGlobalFilters((prev) => ({
                                                ...prev,
                                                [field]: e.target.value || undefined,
                                            }))
                                        }
                                    />
                                )}
                                {type === "select" && (
                                    <Select
                                        size="xs"
                                        placeholder={`选择 ${label}`}
                                        data={options}
                                        value={localGlobalFilters[field] || ""}
                                        onChange={(value) =>
                                            setLocalGlobalFilters((prev) => ({
                                                ...prev,
                                                [field]: value || undefined,
                                            }))
                                        }
                                        clearable
                                        searchable
                                    />
                                )}
                                {type === "numberRange" && (
                                    <Group
                                        gap="xs"
                                        grow
                                    >
                                        <NumberInput
                                            size="xs"
                                            placeholder="最小值"
                                            value={(localGlobalFilters[field] || [])[0] ?? ""}
                                            onChange={(value) =>
                                                setLocalGlobalFilters((prev) => ({
                                                    ...prev,
                                                    [field]: [value, (prev[field] || [])[1]],
                                                }))
                                            }
                                            hideControls
                                        />
                                        <NumberInput
                                            size="xs"
                                            placeholder="最大值"
                                            value={(localGlobalFilters[field] || [])[1] ?? ""}
                                            onChange={(value) =>
                                                setLocalGlobalFilters((prev) => ({
                                                    ...prev,
                                                    [field]: [(prev[field] || [])[0], value],
                                                }))
                                            }
                                            hideControls
                                        />
                                    </Group>
                                )}
                                {type === "date" && (
                                    <DatePickerInput
                                        size="xs"
                                        placeholder="选择日期"
                                        valueFormat="YYYY-MM-DD"
                                        value={localGlobalFilters[field] || null}
                                        onChange={(value) =>
                                            setLocalGlobalFilters((prev) => ({
                                                ...prev,
                                                [field]: value || undefined,
                                            }))
                                        }
                                    />
                                )}
                                {type === "dateRange" && (
                                    <DatePickerInput
                                        type="range"
                                        allowSingleDateInRange
                                        size="xs"
                                        clearable
                                        placeholder="选择日期范围"
                                        valueFormat="YYYY-MM-DD"
                                        value={localGlobalFilters[field] || [null, null]}
                                        onChange={(value) =>
                                            setLocalGlobalFilters((prev) => ({
                                                ...prev,
                                                [field]: value || undefined,
                                            }))
                                        }
                                    />
                                )}
                            </Grid.Col>
                        ))}
                    </Grid>
                </Stack>
            )}

            <Group justify="flex-end">
                {/* 导出按钮 */}
                {onExport && (
                    <CnaButton
                        size="xs"
                        leftSection={<DownloadSimple size={16} />}
                        onClick={onExport}
                    >
                        导出数据
                    </CnaButton>
                )}

                <Group
                    gap={0}
                    style={{ zIndex: isFullscreen ? 200 : "auto" }}
                >
                    {/* 密度选择 */}
                    <Menu
                        shadow="md"
                        width={100}
                    >
                        <Menu.Target>
                            <ActionIcon variant="subtle">
                                <SplitVertical />
                            </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                            {DENSITY_OPTIONS.map(({ value, label }) => (
                                <Menu.Item
                                    key={value}
                                    onClick={() => setDensity(value)}
                                    rightSection={density === value ? "✓" : null}
                                >
                                    {label}
                                </Menu.Item>
                            ))}
                        </Menu.Dropdown>
                    </Menu>

                    {/* 列设置菜单 */}
                    <Menu
                        shadow="md"
                        width={200}
                    >
                        <Menu.Target>
                            <ActionIcon variant="subtle">
                                <GearSix />
                            </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                            <Menu.Label>显示列</Menu.Label>
                            <ScrollArea className="tw-h-64">
                                <Menu.Item closeMenuOnClick={false}>
                                    <Checkbox
                                        checked={table.getIsAllColumnsVisible()}
                                        indeterminate={
                                            !table.getIsAllColumnsVisible() &&
                                            table
                                                .getAllLeafColumns()
                                                .some((col) => col.getIsVisible())
                                        }
                                        onChange={(e) =>
                                            table.toggleAllColumnsVisible(e.currentTarget.checked)
                                        }
                                        label="全选"
                                    />
                                </Menu.Item>
                                {table.getAllLeafColumns().map((column) => (
                                    <Menu.Item
                                        key={column.id}
                                        closeMenuOnClick={false}
                                    >
                                        <Checkbox
                                            checked={column.getIsVisible()}
                                            onChange={(e) =>
                                                column.toggleVisibility(e.currentTarget.checked)
                                            }
                                            label={column.columnDef.header?.toString()}
                                        />
                                    </Menu.Item>
                                ))}
                            </ScrollArea>
                        </Menu.Dropdown>
                    </Menu>

                    {/* 全屏切换按钮 */}
                    <ActionIcon
                        variant="subtle"
                        onClick={toggleFullscreen}
                    >
                        {isFullscreen ? <ArrowsIn /> : <ArrowsOut />}
                    </ActionIcon>
                </Group>
            </Group>

            {/* 选择操作组 */}
            {enableMultiSelect && selectedRowIds.size > 0 && (
                <Group>
                    <Text size="sm">已选择 {selectedRowIds.size} 项</Text>
                    {selectionActions}
                </Group>
            )}

            {/* 表格主体 */}
            <ScrollArea className="tw-border tw-flex-1">
                <LoadingOverlay visible={loading} />
                <Table
                    striped
                    highlightOnHover
                    withTableBorder
                    verticalSpacing={density}
                    stickyHeader
                >
                    <DragDropContext onDragEnd={handleDragEnd}>
                        <Table.Thead>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <Droppable
                                    droppableId={headerGroup.id}
                                    direction="horizontal"
                                    key={headerGroup.id}
                                >
                                    {(provided) => (
                                        <Table.Tr
                                            ref={provided.innerRef}
                                            {...provided.droppableProps}
                                        >
                                            {enableMultiSelect && (
                                                <Table.Th>
                                                    <Checkbox
                                                        checked={table.getIsAllRowsSelected()}
                                                        indeterminate={table.getIsSomeRowsSelected()}
                                                        onChange={table.getToggleAllRowsSelectedHandler()}
                                                    />
                                                </Table.Th>
                                            )}
                                            {headerGroup.headers.map((header, index) => (
                                                <Draggable
                                                    key={header.id}
                                                    draggableId={header.id}
                                                    index={index}
                                                >
                                                    {(provided) => (
                                                        <Table.Th
                                                            ref={provided.innerRef}
                                                            {...provided.draggableProps}
                                                            {...provided.dragHandleProps}
                                                            onClick={
                                                                header.column.getCanSort()
                                                                    ? header.column.getToggleSortingHandler()
                                                                    : undefined
                                                            }
                                                            style={{
                                                                cursor: header.column.getCanSort()
                                                                    ? "pointer"
                                                                    : "move",
                                                                ...provided.draggableProps.style,
                                                                minWidth:
                                                                    filterTypes[
                                                                        header.column.id
                                                                    ] === "datetimeRange"
                                                                        ? 240
                                                                        : header.column.getSize(),
                                                            }}
                                                        >
                                                            <Group gap={2}>
                                                                {flexRender(
                                                                    header.column.columnDef.header,
                                                                    header.getContext()
                                                                )}

                                                                {/* 排序指示器 */}
                                                                {header.column.getCanSort() &&
                                                                    ({
                                                                        asc: (
                                                                            <CaretUp weight="fill" />
                                                                        ),
                                                                        desc: (
                                                                            <CaretDown weight="fill" />
                                                                        ),
                                                                    }[
                                                                        header.column.getIsSorted() as string
                                                                    ] ?? (
                                                                        <CaretUpDown weight="fill" />
                                                                    ))}
                                                            </Group>
                                                        </Table.Th>
                                                    )}
                                                </Draggable>
                                            ))}
                                            {provided.placeholder}
                                        </Table.Tr>
                                    )}
                                </Droppable>
                            ))}

                            {/* 筛选行 */}
                            {Object.keys(filterTypes).length > 0 && (
                                <Table.Tr>
                                    {enableMultiSelect && <Table.Th />}
                                    {table.getAllLeafColumns().map(
                                        (column) =>
                                            column.getIsVisible() && (
                                                <Table.Th key={column.id}>
                                                    {filterTypes[column.id] === "text" && (
                                                        <TextInput
                                                            size="xs"
                                                            placeholder={`筛选 ${column.columnDef.header?.toString()}`}
                                                            value={
                                                                localColumnFilters[column.id] || ""
                                                            }
                                                            onChange={(e) => {
                                                                const newValue = e.target.value;
                                                                setLocalColumnFilters((prev) => ({
                                                                    ...prev,
                                                                    [column.id]:
                                                                        newValue === ""
                                                                            ? undefined
                                                                            : newValue,
                                                                }));
                                                            }}
                                                        />
                                                    )}
                                                    {filterTypes[column.id] === "select" && (
                                                        <Select
                                                            size="xs"
                                                            placeholder={`选择 ${column.columnDef.header?.toString()}`}
                                                            data={filterOptions[column.id] || []}
                                                            value={
                                                                localColumnFilters[column.id] || ""
                                                            }
                                                            onChange={(value) => {
                                                                setLocalColumnFilters((prev) => ({
                                                                    ...prev,
                                                                    [column.id]:
                                                                        value === ""
                                                                            ? undefined
                                                                            : value,
                                                                }));
                                                            }}
                                                            clearable
                                                            searchable
                                                        />
                                                    )}
                                                    {filterTypes[column.id] === "numberRange" && (
                                                        <Group
                                                            gap="xs"
                                                            grow
                                                        >
                                                            <NumberInput
                                                                size="xs"
                                                                placeholder="最小值"
                                                                value={
                                                                    (localColumnFilters[
                                                                        column.id
                                                                    ] || [])[0] ?? ""
                                                                }
                                                                onChange={(value) =>
                                                                    setLocalColumnFilters(
                                                                        (prev) => ({
                                                                            ...prev,
                                                                            [column.id]: [
                                                                                value,
                                                                                (prev[column.id] ||
                                                                                    [])[1],
                                                                            ],
                                                                        })
                                                                    )
                                                                }
                                                                hideControls
                                                            />
                                                            <NumberInput
                                                                size="xs"
                                                                placeholder="最大值"
                                                                value={
                                                                    (localColumnFilters[
                                                                        column.id
                                                                    ] || [])[1] ?? ""
                                                                }
                                                                onChange={(value) =>
                                                                    setLocalColumnFilters(
                                                                        (prev) => ({
                                                                            ...prev,
                                                                            [column.id]: [
                                                                                (prev[column.id] ||
                                                                                    [])[0],
                                                                                value,
                                                                            ],
                                                                        })
                                                                    )
                                                                }
                                                                hideControls
                                                            />
                                                        </Group>
                                                    )}
                                                    {filterTypes[column.id] === "dateRange" && (
                                                        <DatePickerInput
                                                            type="range"
                                                            allowSingleDateInRange
                                                            size="xs"
                                                            clearable
                                                            placeholder="选择日期范围"
                                                            valueFormat="YYYY-MM-DD"
                                                            value={
                                                                localColumnFilters[column.id] || [
                                                                    null,
                                                                    null,
                                                                ]
                                                            }
                                                            onChange={(value) => {
                                                                setLocalColumnFilters((prev) => ({
                                                                    ...prev,
                                                                    [column.id]: value || undefined,
                                                                }));
                                                            }}
                                                        />
                                                    )}
                                                    {filterTypes[column.id] === "datetimeRange" && (
                                                        <Group
                                                            gap="xs"
                                                            grow
                                                        >
                                                            <DateTimePicker
                                                                size="xs"
                                                                clearable
                                                                placeholder="选择开始时间"
                                                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                                                withSeconds
                                                                value={
                                                                    (localColumnFilters[
                                                                        column.id
                                                                    ] || [])[0] || null
                                                                }
                                                                onChange={(value) =>
                                                                    setLocalColumnFilters(
                                                                        (prev) => ({
                                                                            ...prev,
                                                                            [column.id]: [
                                                                                value,
                                                                                (prev[column.id] ||
                                                                                    [])[1],
                                                                            ],
                                                                        })
                                                                    )
                                                                }
                                                            />
                                                            <DateTimePicker
                                                                size="xs"
                                                                clearable
                                                                placeholder="选择结束时间"
                                                                valueFormat="YYYY-MM-DD HH:mm:ss"
                                                                withSeconds
                                                                value={
                                                                    (localColumnFilters[
                                                                        column.id
                                                                    ] || [])[1] || null
                                                                }
                                                                onChange={(value) =>
                                                                    setLocalColumnFilters(
                                                                        (prev) => ({
                                                                            ...prev,
                                                                            [column.id]: [
                                                                                (prev[column.id] ||
                                                                                    [])[0],
                                                                                value,
                                                                            ],
                                                                        })
                                                                    )
                                                                }
                                                            />
                                                        </Group>
                                                    )}
                                                </Table.Th>
                                            )
                                    )}
                                </Table.Tr>
                            )}
                        </Table.Thead>
                    </DragDropContext>
                    <Table.Tbody>
                        {table.getRowModel().rows.map((row) => (
                            <Table.Tr key={row.id}>
                                {enableMultiSelect && (
                                    <Table.Td>
                                        <Checkbox
                                            checked={row.getIsSelected()}
                                            onChange={row.getToggleSelectedHandler()}
                                        />
                                    </Table.Td>
                                )}
                                {row.getVisibleCells().map((cell) =>
                                    cell.column.columnDef.meta?.enableTooltip ? (
                                        <Tooltip
                                            key={cell.id}
                                            label={cell.getValue()?.toString()}
                                            disabled={!cell.getValue()}
                                            multiline
                                            offset={-5}
                                        >
                                            <Table.Td className="tw-max-w-52 tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap">
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </Table.Td>
                                        </Tooltip>
                                    ) : (
                                        <Table.Td
                                            key={cell.id}
                                            className="tw-max-w-52 tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap"
                                        >
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Table.Td>
                                    )
                                )}
                            </Table.Tr>
                        ))}
                    </Table.Tbody>

                    {/* 合计行 */}
                    <Table.Tfoot>
                        <Table.Tr>
                            {enableMultiSelect && <Table.Th />}
                            {table.getAllLeafColumns().map((column) => (
                                <Table.Th key={column.id}>
                                    {column.columnDef.footer
                                        ? flexRender(
                                              column.columnDef.footer,
                                              table
                                                  .getHeaderGroups()[0]
                                                  .headers.find((h) => h.id === column.id)
                                                  ?.getContext() || {}
                                          )
                                        : null}
                                </Table.Th>
                            ))}
                        </Table.Tr>
                    </Table.Tfoot>
                </Table>
            </ScrollArea>

            {/* 分页控件 */}
            <Group justify="space-between">
                <Group>
                    <Text size="sm">总共 {totalCount} 条记录</Text>
                    <Select
                        size="sm"
                        w={100}
                        data={PAGE_SIZE_OPTIONS}
                        value={pageSize.toString()}
                        onChange={(value) => {
                            const newSize = parseInt(value || "10");
                            table.setPageSize(newSize);
                        }}
                    />
                </Group>
                <Pagination
                    total={Math.ceil(totalCount / pageSize)}
                    value={pageIndex + 1}
                    onChange={(page) => table.setPageIndex(page - 1)}
                />
            </Group>
        </Stack>
    );
});
