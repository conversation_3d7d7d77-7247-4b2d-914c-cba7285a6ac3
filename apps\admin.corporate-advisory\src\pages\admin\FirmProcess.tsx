import { <PERSON><PERSON>, Group, Badge } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { <PERSON><PERSON><PERSON>utton, PageHeader } from "@code.8cent/react/components";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import { DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import newFirm from "@/apis/newFirm";
import noty from "@code.8cent/react/noty";
import FirmProcessDetailModal from "@/components/modals/firm/process";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useDataStore from "@code.8cent/store/data";

const STATUS_MAP = {
    0: { label: "待审核", color: "orange" },
    10: { label: "审核通过", color: "green" },
    20: { label: "审核不通过", color: "red" },
};

const STEP_MAP = {
    1: { label: "第一步", color: "blue" },
    2: { label: "第二步", color: "cyan" },
    3: { label: "第三步", color: "indigo" },
};

const FIRM_LEVEL_MAP = {
    1: "省级",
    2: "市级",
    3: "区级",
    4: "镇级",
};

const columnHelper = createColumnHelper<TFirmProcess>();

const AdminFirmProcessPage = () => {
    const { lang } = useSettingStore();

    const tableRef = useRef<DataTableRef | null>(null);
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();
    const { areas } = useDataStore();

    const [data, setData] = useState<TFirmProcess[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const findAreaName = (areaId: number, areaList: AreaData[]): string => {
        for (const area of areaList) {
            if (area.id === areaId) {
                return area.name;
            }
            if (area.children) {
                const found = findAreaName(areaId, area.children);
                if (found) return found;
            }
        }
        return "";
    };

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize } = params;
            const result = await newFirm.getProcessList({
                page,
                per_page: pageSize,
            });
            if (result) {
                setData(result.data);
                setTotalCount(result.total);
            }
        } finally {
            setLoading(false);
        }
    };

    const refreshData = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    // 查看详情
    const handleViewDetail = (process: TFirmProcess) => {
        openModal("firmProcessDetailModal", {
            process,
        });
    };

    // 通过流程
    const handlePassProcess = async (process: TFirmProcess) => {
        openConfirm({
            title: "提示",
            message: "您确定通过该流程审核吗？",
            onConfirm: async () => {
                try {
                    const success = await newFirm.passProcess(process.id);
                    if (success) {
                        noty.success("流程审核通过成功");
                        refreshData();
                    }
                } catch (error) {
                    console.error("通过流程失败:", error);
                }
            },
        });
    };

    // 拒绝流程
    const handleRejectProcess = async (process: TFirmProcess) => {
        openConfirm({
            title: "提示",
            message: "您确定拒绝该流程审核吗？",
            onConfirm: async () => {
                try {
                    const success = await newFirm.rejectProcess(process.id);
                    if (success) {
                        noty.success("流程审核拒绝成功");
                        refreshData();
                    }
                } catch (error) {
                    console.error("拒绝流程失败:", error);
                }
            },
        });
    };

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "流程ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("affiliated_firm.profile.profileName", {
            header: "申请人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("affiliated_firm.level", {
            header: "事务所等级",
            enableSorting: false,
            cell: (info) => {
                const level = info.getValue();
                return FIRM_LEVEL_MAP[level] || "未知";
            },
        }),
        columnHelper.accessor("affiliated_firm.province", {
            header: "省市区",
            enableSorting: false,
            cell: (info) => {
                const province =
                    findAreaName(info.row.original.affiliated_firm?.province, areas) || "";
                const city = findAreaName(info.row.original.affiliated_firm?.city, areas) || "";
                const district =
                    findAreaName(info.row.original.affiliated_firm?.district, areas) || "";
                const town = findAreaName(info.row.original.affiliated_firm?.town, areas) || "";

                return [province, city, district, town].filter(Boolean).join("/");
            },
        }),
        columnHelper.accessor("step", {
            header: "流程步骤",
            enableSorting: false,
            cell: (info) => {
                const step = info.getValue();
                return (
                    <Badge color={STEP_MAP[step]?.color || "gray"}>
                        {STEP_MAP[step]?.label || `第${step}步`}
                    </Badge>
                );
            },
        }),
        columnHelper.accessor("process_status", {
            header: "流程状态",
            enableSorting: false,
            cell: (info) => {
                const status = info.getValue();
                return (
                    <Badge color={STATUS_MAP[status]?.color || "gray"}>
                        {STATUS_MAP[status]?.label || "未知状态"}
                    </Badge>
                );
            },
        }),
        columnHelper.accessor("reason", {
            header: "审核原因",
            enableSorting: false,
            cell: (info) => info.getValue() || "-",
        }),
        columnHelper.accessor("process_time", {
            header: "审核时间",
            enableSorting: false,
            cell: (info) =>
                info.getValue() ? dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss") : "-",
        }),
        columnHelper.accessor("created_at", {
            header: "申请时间",
            enableSorting: false,
            cell: (info) => dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                return (
                    <Group
                        gap="xs"
                        wrap="nowrap"
                    >
                        <CnaButton
                            variant="outline"
                            size="xs"
                            onClick={() => handleViewDetail(info.row.original)}
                        >
                            查看详情
                        </CnaButton>

                        {info.row.original.process_status === 0 && (
                            <TableRowDropActionMenu items={rowActions(info.row.original)} />
                        )}
                    </Group>
                );
            },
        }),
    ];

    // 处理表格行操作按钮
    const rowActions = (row) => [
        {
            key: "pass",
            label: "通过审核",
            onClick: () => handlePassProcess(row),
        },
        {
            key: "reject",
            label: "拒绝审核",
            onClick: () => handleRejectProcess(row),
        },
    ];

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="事务流程管理"
                desc="管理联号事务所申请流程审核"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
            />

            <FirmProcessDetailModal />
        </Stack>
    );
};

export default AdminFirmProcessPage;
