import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import { ActionIcon, Modal, Select, Stack, Textarea, Tooltip } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { ChecksIcon, XIcon, ImageIcon } from "@phosphor-icons/react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { t } from "@code.8cent/i18n";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import { useFileViewer } from "@code.8cent/react/FileViewer";

const schema = z
    .object({
        status: z.number().min(1, "请选择审核结果").max(2, "请选择审核结果"),
        reject_reason: z.string().optional(),
    })
    .refine(
        (data) => data.status !== 2 || (!!data.reject_reason && data.reject_reason.trim() !== ""),
        {
            message: "审核不通过时，请输入审核不通过原因",
            path: ["reject_reason"],
        }
    );

type FormValues = z.infer<typeof schema>;

const PaymentVoucherExamine = ({ onSubmitSuccess }: { onSubmitSuccess: () => void }) => {
    const { lang } = useSettingStore();

    const { openFileView } = useFileViewer();

    const paymentVoucherExamineParams = useModalStore(
        (state) => state.modalParams.gspPaymentVoucherExamineModal
    );
    const paymentVoucherData = paymentVoucherExamineParams?.paymentVoucherData;

    const statusOptions = [
        { label: "审核通过", value: "1" },
        { label: "审核不通过", value: "2" },
    ];

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.gspPaymentVoucherExamineModal,
            close: state.close,
        }))
    );

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
    } = useForm<FormValues>({
        defaultValues: {
            status: 0,
            reject_reason: "",
        },
        resolver: zodResolver(schema),
    });

    const closeModal = useMemoizedFn(() => {
        reset();
        close("gspPaymentVoucherExamineModal");
    });

    const handleViewFile = useMemoizedFn((file_path: string) => {
        openFileView(
            `${window.api_base_url}/api/v1/admin/file/resource?path=${file_path}&type=remote`,
            {
                title: paymentVoucherData?.file_name,
            }
        );
    });

    const onSubmit: SubmitHandler<FormValues> = async (data) => {
        try {
            const examineParams: TGspVoucherCheckParams = {
                id: paymentVoucherData?.id,
                status: data.status as 1 | 2,
                reject_reason: data.reject_reason,
            };

            const res = await api.gsp.paymentVoucherCheck(examineParams);

            if (res) {
                noty.success("审核成功");
                closeModal();
                onSubmitSuccess();
            }
        } catch (error) {
            console.error("审核失败:", error);
        }
    };

    const modalFooterButtons = [
        {
            key: "submit",
            label: "提交",
            leftSection: <ChecksIcon />,
            onClick: handleSubmit(onSubmit),
        },
        {
            key: "close",
            label: "关闭",
            leftSection: <XIcon />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            title="线下付款审核"
            size="md"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-mt-4">
                <div className="tw-mb-4">
                    <div className="tw-text-sm tw-text-gray-600 tw-mb-2">线下付款信息</div>
                    <div className="tw-bg-gray-50 tw-p-3 tw-rounded">
                        <div className="tw-text-sm">
                            <span className="tw-text-gray-500">公司名称：</span>
                            <span>{paymentVoucherData?.company_name}</span>
                        </div>
                        <div className="tw-text-sm">
                            <span className="tw-text-gray-500">文件名称：</span>
                            <span>{paymentVoucherData?.file_name}</span>

                            <Tooltip
                                label="点击查看文件"
                                className="tw-ml-8"
                            >
                                <ActionIcon
                                    size="xs"
                                    variant="transparent"
                                    onClick={() => handleViewFile(paymentVoucherData?.file_path)}
                                >
                                    <ImageIcon />
                                </ActionIcon>
                            </Tooltip>
                        </div>
                    </div>
                </div>

                <Select
                    label="审核结果"
                    placeholder="请选择审核结果"
                    data={statusOptions}
                    error={errors.status?.message}
                    {...register("status", { valueAsNumber: true })}
                    onChange={(value) => setValue("status", Number(value))}
                />

                <Textarea
                    label="审核不通过原因"
                    placeholder="请输入审核不通过原因"
                    minRows={3}
                    error={errors.reject_reason?.message}
                    {...register("reject_reason")}
                />
            </Stack>

            <ModalFooter buttons={modalFooterButtons} />
        </Modal>
    );
};

export default PaymentVoucherExamine;
