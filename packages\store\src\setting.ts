import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "./createSelectors";

type SettingState = {
    isIOS: boolean;
    isWechat: boolean;
    countryID: string;
    lang: LangCode;
    settingCurrency: string;
    settingTimeFormat: string;
    settingTimezone: string;
    settingDateFormat: string;
    settingLanguage: LangCode;
    settingNotifyEmergency: number;
    settingNotifyImportanceUpdate: number;
    settingNotifyJoinInvestigate: number;
    settingNotifyRecPrivateMsg: number;
    settingNotifySafeUpdated: number;
    settingNotifySuspiciousOperation: number;
    settingNotifySystemUpdate: number;
    settingNotifyType: number;
};

type SettingAction = {
    setLang: (lang: LangCode) => void;
    updateSetting: <K extends Exclude<keyof SettingState, "lang">>(
        key: K,
        value: SettingState[K]
    ) => void;
    setUpSetting: (setting: Partial<SettingState>) => void;
};

type SettingStateAndAction = SettingState & SettingAction;

const baseSettingStore = create<SettingStateAndAction>()(
    devtools(
        (set) => ({
            isIOS: false,
            isWechat: false,
            countryID: "",
            lang: window.app_prefer_lang,
            settingCurrency: "",
            settingTimeFormat: "",
            settingTimezone: "",
            settingDateFormat: "",
            settingLanguage: window.app_prefer_lang,
            settingNotifyEmergency: 0,
            settingNotifyImportanceUpdate: 0,
            settingNotifyJoinInvestigate: 0,
            settingNotifyRecPrivateMsg: 0,
            settingNotifySafeUpdated: 0,
            settingNotifySuspiciousOperation: 0,
            settingNotifySystemUpdate: 0,
            settingNotifyType: 0,
            setLang: (lang) => {
                let htmlLang = lang.toLowerCase();

                if (lang === "ZH") {
                    htmlLang = "zh-CN";
                }

                if (lang === "ZT") {
                    htmlLang = "zh-TW";
                }

                document.documentElement.setAttribute("lang", htmlLang);

                set({ lang });
            },

            updateSetting(key, value) {
                set((state) => ({ ...state, [key as string]: value }));
            },

            setUpSetting(setting) {
                set(setting);
            },
        }),
        {
            name: "setting-store",
        }
    )
);

const useSettingStore = createSelectors(baseSettingStore);

export default useSettingStore;
