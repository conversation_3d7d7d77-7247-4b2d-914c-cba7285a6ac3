import api from "@/apis";
import ProjectCreateCompanyModal from "@/components/modals/project/CreateCompanyModal";
import ProjectCompanyApplicationModal from "@/components/modals/project/CompanyApplicationModal";
import { t } from "@code.8cent/i18n";
import useModalStore from "@/store/modal";
import useSettingStore from "@code.8cent/store/setting";
import {
    Box,
    Button,
    Grid,
    Text,
    Group,
    Stack,
    Table,
    Title,
    UnstyledButton,
} from "@mantine/core";
import { useMount, useRequest, useUnmount } from "ahooks";
import ProjectCompanyDetailModal from "@/components/modals/project/CompanyDetailModal";
import { Plus } from "@phosphor-icons/react";
import { useState } from "react";
import { useEventBus } from "@/utils/eventBus";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { PageHeader } from "@code.8cent/react/components";

const MemberProjectPage = () => {
    const [selectCompanyID, setSelectCompanyID] = useState<number>(null);

    const bus = useEventBus();
    const lang = useSettingStore.use.lang();
    const openModal = useModalStore.use.open();

    const {
        data: projects,
        loading,
        run: getProjects,
    } = useRequest(async () => {
        let list = await api.project.getProjectList();
        return list;
    });

    useMount(() => {
        bus.on("project.list.refresh", getProjects);
    });

    useUnmount(() => {
        bus.off("project.list.refresh", getProjects);
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title={t("navigation.project", lang)}
                desc={t("navigation.project.system.quote", lang)}
            />

            <Group justify="end" className="tw-mb-5">
                <CnaButton
                    onClick={() => openModal("projectCompanyCreate")}
                    color="basic"
                    leftSection={<Plus />}
                >
                    {t("project.add", lang)}
                </CnaButton>
            </Group>

            <Stack>
                {projects?.map?.((project, idx) => (
                    <Table.ScrollContainer minWidth={500} key={idx}>
                        <Table withTableBorder verticalSpacing={"md"}>
                            <Table.Thead>
                                <Table.Tr className="tw-bg-gray-100">
                                    <Table.Th miw={200}>
                                        {project?.[`projectCategoriesName${lang}`] ?? ""}
                                    </Table.Th>
                                    <Table.Th
                                        miw={200}
                                        className="tw-font-normal"
                                    >
                                        {t("project.table.th.status", lang)}
                                    </Table.Th>
                                    <Table.Th
                                        miw={100}
                                        className="tw-font-normal tw-text-right"
                                    >
                                        {t("project.table.th.action", lang)}
                                    </Table.Th>
                                </Table.Tr>
                            </Table.Thead>
                            <Table.Tbody>
                                {!project.projectItem?.length && (
                                    <Table.Tr>
                                        <Table.Td
                                            colSpan={3}
                                            className="tw-text-center tw-py-16 tw-text-gray-400"
                                        >
                                            {t("project.empty", lang)}
                                        </Table.Td>
                                    </Table.Tr>
                                )}
                                {project.projectItem?.map?.((item, _idx) => (
                                    <Table.Tr key={_idx}>
                                        <Table.Td>{item.companyName}</Table.Td>
                                        <Table.Td className="tw-text-gray-500">
                                            {t(item.companyState, lang)}
                                        </Table.Td>
                                        <Table.Td className="tw-text-gray-500 tw-text-right">
                                            <UnstyledButton
                                                className="tw-text-[14px]"
                                                onClick={() => {
                                                    setSelectCompanyID(
                                                        item.companyID
                                                    );
                                                    if (
                                                        item.companyState !==
                                                        "project.edit.complete"
                                                    ) {
                                                        openModal(
                                                            "projectCompanyApplication"
                                                        );
                                                    } else {
                                                        openModal(
                                                            "projectCompanyDetail"
                                                        );
                                                    }
                                                }}
                                            >
                                                {t("project.more", lang)}{" "}
                                                {/* Use a valid translation key */}
                                            </UnstyledButton>
                                        </Table.Td>
                                    </Table.Tr>
                                ))}
                            </Table.Tbody>
                        </Table>
                    </Table.ScrollContainer>
                ))}
            </Stack>

            <ProjectCreateCompanyModal />
            <ProjectCompanyApplicationModal companyID={selectCompanyID} />
            <ProjectCompanyDetailModal />
        </Stack>
    );
};

export default MemberProjectPage;
