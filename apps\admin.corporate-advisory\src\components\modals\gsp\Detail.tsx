import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import {
    Divider,
    Group,
    Modal,
    MultiSelect,
    SimpleGrid,
    Stack,
    Text,
    Textarea,
    TextInput,
} from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { CnaButton } from "@code.8cent/react/components";
import { X } from "@phosphor-icons/react";
import { downloadFile } from "@/utils/files";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import { useState } from "react";

const GspDetail = () => {
    const { lang } = useSettingStore();
    const [downloadMaterialLoading, setDownloadMaterialLoading] = useState(false);

    const { openFileView } = useFileViewer();

    const openConfirm = useModalStore.use.openConfirm();

    const gspApplicationParams = useModalStore((state) => state.modalParams.gspApplicationModal);
    const gsp = gspApplicationParams?.gspApplication;
    const selectOptions = gspApplicationParams?.selectOptions;

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.gspApplicationModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        close("gspApplicationModal");
    });

    const modalFooterButtons = [
        {
            key: "close",
            label: "关闭",
            leftSection: <X />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    const handleDownload = useMemoizedFn((path: string, type: "local" | "remote") => {
        downloadFile(path, type);
    });

    const confirmDownload = useMemoizedFn((path: string, type: "local" | "remote") => {
        openConfirm({
            title: "确定下载此文件吗？",
            onConfirm: () => {
                handleDownload(path, type);
            },
        });
    });

    // 处理下载打包的 zip 文件
    const handleDownloadMaterialZip = useMemoizedFn(async (id) => {
        try {
            setDownloadMaterialLoading(true);
            // 获取服务端的 zip 文件路径
            const { result } = await cnaRequest("/api/v1/admin/gsp/download-material", "GET", {
                id,
            });

            if (result.data) {
                const fileBasename = result.data.split("/").pop();
                downloadFile(fileBasename, "local");
            } else {
                noty.error("暂无补充材料，下载失败");
            }
        } catch (error) {
            noty.error("下载失败，请稍后重试");
        } finally {
            setDownloadMaterialLoading(false);
        }
    });

    return (
        <Modal
            title="绿智地球申请信息"
            opened={show}
            onClose={closeModal}
            size="xl"
        >
            <Text className="tw-pl-2 tw-font-bold tw-bg-[#060d3d] tw-text-white">企业基础信息</Text>
            <Divider my={10} />
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="企业名称"
                    defaultValue={gsp?.name}
                    readOnly
                />
                <TextInput
                    label="统一社会信用代码"
                    defaultValue={gsp?.credit_code}
                    readOnly
                />
            </SimpleGrid>
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="注册资本（元）"
                    defaultValue={gsp?.register_capital}
                    readOnly
                />
                <TextInput
                    label="实缴资本（元）"
                    defaultValue={gsp?.paid_capital}
                    readOnly
                />
            </SimpleGrid>
            <Textarea
                label="企业简介"
                defaultValue={gsp?.desc}
                readOnly
            />
            <Textarea
                label="主要业务范围"
                defaultValue={gsp?.main_business}
                readOnly
            />
            <Textarea
                label="绿色智慧相关业务"
                defaultValue={gsp?.related_business}
                readOnly
            />
            <Textarea
                label="主要客户及供应商名称"
                defaultValue={gsp?.customer}
                readOnly
            />
            <Text className="tw-pl-2 tw-font-bold tw-mt-2 tw-bg-[#060d3d] tw-text-white">
                企业财务信息（以最近一期审计报告数据填写）
            </Text>
            <Divider my={10} />
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="年营业收入（人民币：元）"
                    defaultValue={gsp?.annual_revenue}
                    readOnly
                />
                <TextInput
                    label="年营业成本（人民币：元）"
                    defaultValue={gsp?.annual_cost}
                    readOnly
                />
            </SimpleGrid>
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="总资产（人民币：元）"
                    defaultValue={gsp?.total_assets}
                    readOnly
                />
                <TextInput
                    label="总负债（人民币：元）"
                    defaultValue={gsp?.total_liability}
                    readOnly
                />
            </SimpleGrid>
            <Text className="tw-pl-2 tw-font-bold tw-mt-2 tw-bg-[#060d3d] tw-text-white">
                企业联系人信息
            </Text>
            <Divider my={10} />
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="姓名"
                    defaultValue={gsp?.contact_name}
                    readOnly
                />
                <TextInput
                    label="职务"
                    defaultValue={gsp?.contact_position}
                    readOnly
                />
            </SimpleGrid>
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="联系电话"
                    defaultValue={gsp?.phone}
                    readOnly
                />
                <TextInput
                    label="电子邮箱"
                    defaultValue={gsp?.email}
                    readOnly
                />
            </SimpleGrid>
            <Text className="tw-pl-2 tw-font-bold tw-mt-2 tw-bg-[#060d3d] tw-text-white">
                入驻平台需求
            </Text>
            <Divider my={10} />
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <MultiSelect
                    label="拟参与的主要经济行为（可多选）"
                    data={selectOptions?.economics?.map((item) => ({
                        value: item.id.toString(),
                        label: item.title,
                    }))}
                    defaultValue={gsp?.economic_behavior ? gsp?.economic_behavior.split(",") : []}
                    readOnly
                />
                <MultiSelect
                    label="拟对接的主要行业群体（可多选）"
                    data={selectOptions?.industries?.map((item) => ({
                        value: item.id.toString(),
                        label: item.title,
                    }))}
                    defaultValue={gsp?.industry_group ? gsp?.industry_group.split(",") : []}
                    readOnly
                />
            </SimpleGrid>
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="在平台的预计年交易额（人民币：元）"
                    defaultValue={gsp?.annual_trade}
                    readOnly
                />
                <MultiSelect
                    label="拟开拓市场的目标国家（可多选）"
                    data={selectOptions?.countries?.map((item) => ({
                        value: item.countryID.toString(),
                        label: item.countryZH,
                    }))}
                    defaultValue={gsp?.target_country ? gsp?.target_country.split(",") : []}
                    readOnly
                />
            </SimpleGrid>
            <Text className="tw-pl-2 tw-font-bold tw-mt-2 tw-bg-[#060d3d] tw-text-white">
                绿智地球客户文件
            </Text>
            <Divider my={10} />
            <Stack gap={4}>
                {/* {gsp?.form && (
                    <Group justify="space-between">
                        <Text size="sm">资格预审表格</Text>
                        <Group>
                            <CnaButton
                                variant="outline"
                                size="xs"
                                onClick={() =>
                                    openFileView(
                                        `${window.api_base_url}/api/v1/admin/file/resource?path=${gsp?.form}&type=local`,
                                        { title: "资格预审表格" }
                                    )
                                }
                            >
                                查看
                            </CnaButton>
                            <CnaButton
                                variant="outline"
                                size="xs"
                                onClick={() => confirmDownload(gsp?.form, "local")}
                            >
                                下载
                            </CnaButton>
                        </Group>
                    </Group>
                )} */}
                {gsp?.form_complete != "" && gsp?.status >= 1 && (
                    <Group justify="space-between">
                        <Text size="sm">资格预审表格（已签署盖章）</Text>
                        <Group>
                            <CnaButton
                                variant="outline"
                                size="xs"
                                onClick={() =>
                                    openFileView(
                                        `${window.api_base_url}/api/v1/admin/file/resource?path=${gsp?.form_complete}&type=local`,
                                        { title: "资格预审表格（已签署盖章）" }
                                    )
                                }
                            >
                                查看
                            </CnaButton>
                            <CnaButton
                                variant="outline"
                                size="xs"
                                onClick={() => confirmDownload(gsp?.form_complete, "local")}
                            >
                                下载
                            </CnaButton>
                        </Group>
                    </Group>
                )}
                {/* 绿智地球用户合同 */}
                {gsp?.contract_url != "" && gsp?.status >= 4 && (
                    <Group justify="space-between">
                        <Text size="sm">绿智地球用户合同</Text>
                        <Group>
                            <CnaButton
                                variant="outline"
                                size="xs"
                                onClick={() =>
                                    openFileView(
                                        `${window.api_base_url}/api/v1/admin/file/resource?path=${gsp?.contract_url}&type=local`,
                                        { title: "绿智地球用户合同" }
                                    )
                                }
                            >
                                查看
                            </CnaButton>
                            <CnaButton
                                variant="outline"
                                size="xs"
                                onClick={() => confirmDownload(gsp?.contract_url, "local")}
                            >
                                下载
                            </CnaButton>
                        </Group>
                    </Group>
                )}
                {/* 补充材料 */}
                <Group justify="space-between">
                    <Text size="sm">补充材料</Text>
                    <Group>
                        <CnaButton
                            variant="outline"
                            size="xs"
                            onClick={() => handleDownloadMaterialZip(gsp?.id)}
                            loading={downloadMaterialLoading}
                        >
                            下载
                        </CnaButton>
                    </Group>
                </Group>
            </Stack>

            <div className="tw-mt-10">
                <ModalFooter buttons={modalFooterButtons} />
            </div>
        </Modal>
    );
};

export default GspDetail;
