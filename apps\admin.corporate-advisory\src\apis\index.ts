import cnaRequest from "@code.8cent/utils/cnaRequest";
import profile from "./profile";
import config from "./config";
import gernal from "./gernal";
import user from "./user";
import associate from "./associate";
import notice from "./notice";
import fieldType from './fieldType'
import client from "./client";
import bill from "./bill";
import account from "./account";
import document from "./document";
import team from "./team";
import activityLog from "./activityLog";
import benefit from "./benefit";
import remark from "./remark";
import emailTemplate from "./emailTemplate";
import web from "./web";
// fixed pnpm build ts type errors
import email from "./email";
import project from "./project";
import register from "./register";
import firm from "./firm";
import finance from "./finance";
import digitalHuman from "./digitalHuman";
import gsp from "./gsp";
import department from "./department";
import visitor from "./visitor";
import activity from "./activity";

const api = {
    test: async () => {
        //https://laravel.api.cna.greensmartplanet.cn/api/v1/test
        let res = await cnaRequest<TestResponse>("/api/v1/test", "GET");

        const { result, error } = res;

        if (!error) {
            return result;
        } else {
            return "";
        }
    },
    version: async () => {
        let res = await cnaRequest<string>("/version", "GET");

        const { result, error } = res;

        if (!error) {
            return result;
        } else {
            return "";
        }
    },
    user,
    profile,
    config,
    gernal,
    associate,
    notice,
    fieldType,
    client,
    bill,
    account,
    document,
    team,
    activityLog,
    benefit,
    remark,
    emailTemplate,
    web,
    email,
    project,
    register,
    firm,
    finance,
    digitalHuman,
    gsp,
    department,
    visitor,
    activity,
};

export default api;
