import { Group, Modal, Stack, TextInput } from "@mantine/core";
import React, { useEffect } from "react";
import useModalStore from "@/store/modal";
import { useShallow } from "zustand/react/shallow";
import { DateTimePicker } from "@mantine/dates";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { useSetState } from "ahooks";
import dayjs from "dayjs";

type TEvent = {
    start: string;
    end: string;
    title: string;
};

const initialValues = (defaultValue?: string) => ({
    start: dayjs(defaultValue ?? new Date()).format("YYYY-MM-DD HH:mm"),
    end: dayjs(defaultValue ?? new Date())
        .add(1, "hour")
        .format("YYYY-MM-DD HH:mm"),
    title: "",
});

const CalendarEventCreationModal: React.FC<{
    defaultValue?: string;
    onEventCreate?: (event: TEvent) => void;
}> = ({ defaultValue, onEventCreate }) => {
    const [eventForm, setEventForm] = useSetState(initialValues(defaultValue));

    const {
        show,
        close,
        openAlert: alert,
    } = useModalStore(
        useShallow((state) => ({
            show: state.calendarEventCreation,
            close: state.close,
            openAlert: state.openAlert,
        }))
    );

    const closeModal = () => {
        close("calendarEventCreation");
    };

    useEffect(() => {
        if (show === false) {
            setEventForm(initialValues());
        }
    }, [show]);

    useEffect(() => {
        setEventForm(initialValues(defaultValue));
    }, [defaultValue])

    return (
        <Modal opened={show} onClose={closeModal} size="lg" title={"添加事件"}>
            <Stack>
                <TextInput
                    label="事件标题"
                    value={eventForm.title}
                    onChange={(e) => {
                        setEventForm({ title: e.target.value });
                    }}
                />
                <DateTimePicker
                    label="开始时间"
                    placeholder="选择开始时间"
                    value={dayjs(eventForm.start).toDate()}
                    onChange={(val) =>
                        setEventForm({
                            start: dayjs(val).format("YYYY-MM-DD HH:mm"),
                        })
                    }
                />
                <DateTimePicker
                    label="结束时间"
                    placeholder="选择结束时间"
                    value={dayjs(eventForm.end).toDate()}
                    onChange={(val) =>
                        setEventForm({
                            end: dayjs(val).format("YYYY-MM-DD HH:mm"),
                        })
                    }
                />
                <Group justify="end">
                    <CnaButton
                        color="cna"
                        onClick={() => {
                            typeof onEventCreate === "function" &&
                                onEventCreate(eventForm);
                        }}
                    >
                        创建事件
                    </CnaButton>
                </Group>
            </Stack>
        </Modal>
    );
};

export default CalendarEventCreationModal;
