import { Table, Checkbox, Tooltip } from "@mantine/core";
import { flexRender } from "@tanstack/react-table";

export function TableBody({ table, enableMultiSelect }) {
    return (
        <Table.Tbody>
            {table.getRowModel().rows.map((row) => (
                <Table.Tr key={row.id}>
                    {enableMultiSelect && (
                        <Table.Td>
                            <Checkbox
                                checked={row.getIsSelected()}
                                onChange={row.getToggleSelectedHandler()}
                            />
                        </Table.Td>
                    )}
                    {row.getVisibleCells().map((cell) =>
                        cell.column.columnDef.meta?.enableTooltip ? (
                            <Tooltip
                                key={cell.id}
                                label={cell.getValue()?.toString()}
                                disabled={!cell.getValue()}
                                multiline
                                offset={-5}
                            >
                                <Table.Td className="tw-max-w-52 tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap">
                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                </Table.Td>
                            </Tooltip>
                        ) : (
                            <Table.Td
                                key={cell.id}
                                className="tw-max-w-52 tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap"
                            >
                                {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </Table.Td>
                        )
                    )}
                </Table.Tr>
            ))}
            {/* 没有数据时，显示空状态 */}
            {table.getRowModel().rows.length === 0 && (
                <Table.Tr>
                    <Table.Td
                        colSpan={table.getAllLeafColumns().length + (enableMultiSelect ? 1 : 0)}
                        className="tw-text-center"
                    >
                        暂无相关数据
                    </Table.Td>
                </Table.Tr>
            )}
        </Table.Tbody>
    );
}
