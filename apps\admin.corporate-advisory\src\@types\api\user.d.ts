declare global {
    type UserLoginResponse = {
        token: string;
        state?: number;
    };

    type UserProfileResponse = {
        profileID: number;
        mobilePrefixID: number;
        profileName: string;
        profileNRIC: string;
        profileGender: "M" | "F";
        profileNationality: string;
        profileBirthDate: string;
        profileRole: string;
        profileEmail: string;
        profileEmailValidate: "Y" | "N";
        profileContact: string;
        profileContactValidate: "Y" | "N";
        profileAddressUnit: string;
        profileAddressStreet: string;
        profileAddressCity: string;
        profileAddressState: string;
        profileAddressPostcode: string;
        profileAddressCountry: string;
        profileAddressDistrict: string;
        status: string;
        profilePartnerCode: string;
        profileAvatar: string;
        profileLastName: string;
        profileGivenName: string;
        settingCurrency: string;
        settingDateFormat: string;
        settingLanguage: string;
        settingTimeFormat: string;
        settingTimezone: string;
        userProfessional: {
            professionalID: number;
            professionalDescription: string;
        }[];
        userExperience: {
            experienceID: number;
            experienceDescription: string;
        }[];
        userSkill: {
            skillID: number;
            skillDescription: string;
        }[];
        created_at: string;
        registerState: number;
        profileNationalityID: number;
        professionals: {
            professionalID: number;
            pivot: {
                profileID: number;
                professionalID: number;
                professionalDescription: string;
            };
        }[];
        skills: {
            skillID: number;
            pivot: {
                profileID: number;
                skillID: number;
                skillDescription: string;
            };
        }[];
        nationality?: {};
        settingNotifyType: number;
        settingNotifyEmergency: number;
        settingNotifySuspiciousOperation: number;
        settingNotifySafeUpdated: number;
        settingNotifyRecPrivateMsg: number;
        settingNotifyImportanceUpdate: number;
        settingNotifySystemUpdate: number;
        settingNotifyJoinInvestigate: number;
        settingNotifyBill: number;
        permissions?: string[];
        new_experiences?: Array<{
            id: string;
            type: number;
            experience?: {
                id: number;
                title_zh: string;
            };
            country?: {
                countryID: number;
                countryZH: string;
            };
            industry?: {
                id: number;
                title_zh: string;
            };
            desc?: string;
            experience_name: string;
            time_range?: string;
            file?: string;
        }>;
        education?: string;
        certificate?: string;
        businessCase?: {
            name: string;
            content: string;
            result: string;
            address?: string;
            date?: string;
        }[];
        aiCases?: {
            name: string;
            content: string;
            result: string;
            address?: string;
            date?: string;
        }[];
        interviewResult?: {
            id: number;
            profileID: number;
            token: string;
            average_score?: number | null;
            status: number;
            interview_start_at?: string;
            interview_end_at?: string;
            created_at?: string;
            updated_at?: string;
            content?: {
                id: number;
                interview_id: number;
                module?: number;
                question?: string;
                answer?: string | null;
                answer_url?: string | null;
                score?: number | null;
                created_at?: string | null;
                updated_at?: string | null;
            }[];
        }[];
        admin_review_status?: {
            info_status: number;
            register_payment_status: number;
        };
        ai_station_interviews?: {
            comment_draft: string;
            comment: string;
            question_id: string;
            status: number;
        }[];
    } & BaseOperateResponse;

    type TAccountSearchParams = {
        keyword: string | null;
    } & TPageQueryParams;

    type TAccountsResponse = {
        items: UserProfileResponse[];
        paginate: BasePaginateResponse;
    };
}

export {};
