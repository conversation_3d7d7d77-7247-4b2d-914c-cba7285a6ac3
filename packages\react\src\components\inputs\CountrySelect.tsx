import { Group, Text, Select, SelectProps } from "@mantine/core";
import { useMemoizedFn, useMount } from "ahooks";
import { forwardRef, useEffect, useState } from "react";
import FlagComponent from "../common/FlagComponent";
import React from "react";

type CountrySelectProps<T = any> = {
    flagKey: keyof T;
    labelKey: keyof T;
    valueKey: keyof T;
    data: T[];
} & Omit<SelectProps, "data">;

const CountrySelectComp = <T,>({
    data,
    flagKey,
    labelKey,
    valueKey,
    ...selectProps
}: CountrySelectProps<T>) => {
    const [flagName, setFlagName] = useState<string>("");

    const setFlagItem = useMemoizedFn(() => {
        if (selectProps?.value?.length > 0) {
            const value = String(selectProps?.value);

            const flag_item = data.find((item) => {
                return `${item[valueKey]}` === value;
            });

            if (flag_item) {
                setFlagName(flag_item[flagKey] as string);
            }
        }
    });

    useEffect(() => {
        setFlagItem();
    }, [selectProps?.value, data]);

    useMount(() => {
        setFlagItem();
    });

    return (
        <Select
            {...selectProps}
            data={data.map((item, _idx) => ({
                ...item,
                label: `${item[labelKey]}`,
                value: String(item[valueKey]),
            }))}
            classNames={{
                ...selectProps?.classNames,
                option: `tw-p-0 ${
                    (selectProps?.classNames as any)?.option ?? ""
                }`,
            }}
            renderOption={({
                option,
                checked,
            }: {
                option: T & { label: string; value: string };
                checked: boolean;
            }) => {
                return (
                    <Group
                        className={`${
                            checked && "tw-bg-neutral-100"
                        } tw-w-full tw-p-2`}
                    >
                        <FlagComponent countryCode={`${option[flagKey]}`} />
                        <Text>{option.label}</Text>
                    </Group>
                );
            }}
            leftSection={
                flagName.length > 0 && <FlagComponent countryCode={flagName} />
            }
        />
    );
};

const CountrySelect = forwardRef<HTMLDivElement, CountrySelectProps<any>>(
    (props, ref) => <CountrySelectComp {...props} />
) as <T>(
    props: CountrySelectProps<T> & { ref?: React.Ref<HTMLDivElement> }
) => React.ReactElement | null;

export default CountrySelect;
