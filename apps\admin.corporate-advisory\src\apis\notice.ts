import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const notice = {
    list: async (params: TNoticeSearchParams) => {
        const { error, result } = await cnaRequest<TNoticesResponse>(
            "/api/v1/admin/notifications/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (params: TStoreNoticeParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/notifications/create",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (params: TUpdateNoticeParams, id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/notifications/edit/${id}`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    }
};

export default notice;
