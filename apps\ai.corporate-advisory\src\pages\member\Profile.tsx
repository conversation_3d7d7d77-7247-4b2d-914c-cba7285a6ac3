import api from "@/apis";
import ProfileValidateModal from "@/components/modals/profile/ProfileValidateModal";
import ProfileUserData from "@/components/profile/ProfileUserData";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import useModalStore from "@/store/modal";
import useProfileStore from "@/store/profile";
import useSettingStore from "@code.8cent/store/setting";
import {
    Grid,
    Group,
    Input,
    Stack,
    Text,
    TextInput,
    Title,
    Button,
    DefaultMantineColor,
} from "@mantine/core";
import { Check, CheckCircle, Circle, Warning } from "@phosphor-icons/react";
import { useMemoizedFn, useTitle } from "ahooks";
import React, { useEffect, useState } from "react";
import { getData } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";
import dayjs from "dayjs";
import {
    FlagComponent,
    PhoneInput,
    AddressInput,
    ProfileAvatar,
    CnaButton,
    AddressSelect,
} from "@code.8cent/react/components";
import { useResetPasswordModal } from "@code.8cent/react/hooks";
import { useShallow } from "zustand/react/shallow";
import { useVerifyModal } from "@code.8cent/react/VerifyModal";

const ValidateStatusSection: React.FC<{
    status: "Y" | "N";
    openValidate: () => void;
}> = ({ status, openValidate }) => {
    const lang = useSettingStore.use.lang();

    const resolveValidationStatus = useMemoizedFn((value: "Y" | "N") => {
        if (value === "Y") {
            return {
                icon: (
                    <CheckCircle
                        size={18}
                        weight="fill"
                    />
                ),
                color: "green" as DefaultMantineColor,
                text: t("introduction.label.verify", lang),
            };
        } else {
            return {
                icon: (
                    <Warning
                        size={18}
                        weight="fill"
                    />
                ),
                color: "yellow" as DefaultMantineColor,
                text: t("introduction.label.unverify", lang),
            };
        }
    });

    const { icon, color, text } = resolveValidationStatus(status);

    return (
        <Button
            onClick={() => {
                if (status !== "Y") {
                    openValidate();
                }
            }}
            variant="transparent"
            color={color}
            leftSection={icon}
        >
            {text}
        </Button>
    );
};

const MemberProfilePage: React.FC = () => {
    const profile = useProfileStore();

    const { lang, countryID } = useSettingStore();

    const [originalProfile, setOriginalProfile] = useState<Partial<typeof profile>>({});

    const [isUpdateEnabled, setIsUpdateEnabled] = useState(false);

    const { countryDatas, filteredCountryDatas } = useDataStore(
        useShallow((state) => {
            return {
                countryDatas: state.countryDatas,
                filteredCountryDatas: state.filteredCountryDatas,
            };
        })
    );

    useEffect(() => {
        if (profile.inited === true) {
            setOriginalProfile(profile);
        }
    }, [profile.inited]);

    useEffect(() => {
        if (profile.inited === true) {
            const isProfileModified = JSON.stringify(profile) !== JSON.stringify(originalProfile);

            const isProfileUserDataValid =
                profile.userProfessional.every((item) => {
                    return item.professionalID && item.professionalDescription;
                }) &&
                profile.userSkill.every((item) => {
                    return item.skillID && item.skillDescription;
                });

            setIsUpdateEnabled(isProfileModified && isProfileUserDataValid);
        }
    }, [profile, originalProfile]);

    useEffect(() => {
        profile.setProfileValue("profileAddressCountry", countryID);
    }, []);

    const [updating, setUpdating] = useState(false);

    const openModal = useModalStore.use.open();

    const { open: openResetPassword } = useResetPasswordModal();

    const { open: openEmailUpdate } = useVerifyModal({
        id: "updateEmail",
        type: "email",
        isUpdate: true,
        getCodeUrl: "/api/v1/user/changeEmail",
        verifyUrl: "/api/v1/user/changeEmailVerify",
        onVerifySuccess({ data }) {
            profile.setProfileValue("profileEmail", data.email);
            profile.setProfileValue("profileEmailValidate", "Y");
        },
    });

    const { open: openPhoneUpdate } = useVerifyModal({
        id: "updatePhone",
        type: "phone",
        isUpdate: true,
        getCodeUrl: "/api/v1/user/changePhone",
        verifyUrl: "/api/v1/user/changePhoneVerify",
        onVerifySuccess({ data }) {
            profile.setProfileValue("profileContact", data.phone);
            profile.setProfileValue("mobilePrefixID", data.prefixID);
            profile.setProfileValue("profileContactValidate", "Y");
        },
    });

    const [validateType, setValidateType] = useState<"contact" | "email">("email");

    useTitle(`${t("profile.title", lang)} | ${window.app_title}`, {
        restoreOnUnmount: true,
    });

    const updateProfile = useMemoizedFn(async () => {
        if (updating === true) {
            return;
        }

        setUpdating(true);

        let updateRes = await api.user.updateUserProfile({
            profileEmail: profile.profileEmail,
            profileContact: profile.profileContact,
            mobilePrefixID: profile.mobilePrefixID,
            // profileAddressCity: profile.profileAddressCity,
            // profileAddressState: profile.profileAddressState,
            profileAddressStreet: profile.profileAddressStreet,
            profileAddressPostcode: profile.profileAddressPostcode,
            profileAddressCountry: profile.profileAddressCountry,
            profileAddressUnit: profile.profileAddressUnit,
            // profileAddressDistrict: profile.profileAddressDistrict,
            // 改成上传省市区ID
            profileAddressStateId: profile.profileAddressStateId,
            profileAddressCityId: profile.profileAddressCityId,
            profileAddressDistrictId: profile.profileAddressDistrictId,
            experience: profile.userExperience,
            skill: profile.userSkill,
            professional: profile.userProfessional,
        });

        if (updateRes === true) {
            noty.success(
                t("profile.modified.success", lang),
                t("profile.modified.success.detail", lang)
            );

            setOriginalProfile(profile);
        } else {
            noty.error(t("profile.modified.fail", lang), t("profile.modified.fail.detail", lang));
        }

        setUpdating(false);
    });

    return (
        <>
            <div className="tw-bg-white tw-p-6 tw-flex-1 tw-overflow-y-auto tw-h-full tw-w-full tw-py-10">
                <Grid>
                    <Grid.Col
                        className="tw-px-8"
                        span={{ md: 6, base: 12 }}
                    >
                        <Grid classNames={{ inner: "tw-items-center" }}>
                            <Grid.Col span={{ base: 12, md: 5, lg: 4 }}>
                                <ProfileAvatar
                                    className="tw-max-w-[150px] tw-mx-auto"
                                    src={`${window.api_base_url}${profile.profileAvatar}`}
                                    onUpload={(path) => {
                                        profile.setProfileValue("profileAvatar", path);
                                    }}
                                    allowUpload
                                />
                            </Grid.Col>
                            <Grid.Col span={{ base: 12, md: 7, lg: 8 }}>
                                <Group className="tw-gap-0 tw-text-neutral-600">
                                    <Text className="tw-mr-2">
                                        {t("introduction.state", lang)}:
                                    </Text>
                                    <Circle
                                        weight="fill"
                                        className="tw-text-green-600"
                                    />
                                    <Text className="tw-ml-2">{t("profile.status.3", lang)}</Text>
                                </Group>
                                <Group className="tw-gap-0 tw-text-neutral-600">
                                    <Text className="tw-mr-2">
                                        {t("introduction.apply_date", lang)}:{" "}
                                    </Text>
                                    <Text>
                                        {dayjs(profile.created_at).format(
                                            profile.settingDateFormat || "YYYY-MM-DD"
                                        )}
                                    </Text>
                                </Group>
                                <Group className="tw-gap-0 tw-text-neutral-600">
                                    <Text className="tw-mr-2">
                                        {t("introduction.partner_code", lang)}:{" "}
                                    </Text>
                                    <Text>{profile.profilePartnerCode}</Text>
                                </Group>
                                <Group className="tw-gap-0 tw-text-neutral-600">
                                    <Text className="tw-mr-2">
                                        {t("introduction.nric", lang)}:{" "}
                                    </Text>
                                    <Text>{profile.profileNRIC}</Text>
                                </Group>
                                <Group className="tw-gap-0 tw-text-neutral-600">
                                    <Text className="tw-mr-2">
                                        {t("introduction.nationality", lang)}:{" "}
                                    </Text>
                                    <Group gap={5}>
                                        <FlagComponent
                                            countryCode={
                                                getData.getCountryItemById(
                                                    profile.profileNationalityID
                                                )?.countryISOCode2 || "ZH"
                                            }
                                        />
                                        <Text>
                                            {getData.getCountryItemById(
                                                profile.profileNationalityID
                                            )?.[`country${lang}`] ?? ""}
                                        </Text>
                                    </Group>
                                </Group>
                            </Grid.Col>
                        </Grid>
                        <div className="tw-my-10">
                            <Title
                                className="tw-text-basic-5"
                                order={3}
                            >
                                {t("introduction.title", lang)}
                            </Title>
                            <Text>{t("introduction.tip", lang)}</Text>
                        </div>
                        <Stack>
                            <TextInput
                                label={t("introduction.label.given_name", lang)}
                                labelProps={{ className: "profile-form-label" }}
                                value={profile.profileName ?? ""}
                                readOnly
                            />
                            <TextInput
                                label={t("introduction.label.email", lang)}
                                labelProps={{ className: "profile-form-label" }}
                                value={profile.profileEmail}
                                rightSectionWidth={120}
                                rightSection={
                                    <ValidateStatusSection
                                        status={profile.profileEmailValidate}
                                        openValidate={() => {
                                            setValidateType("email");
                                            openModal("profileValidate");
                                        }}
                                    />
                                }
                                onFocus={(e) => {
                                    openEmailUpdate();
                                    e.target.blur();
                                }}
                                readOnly
                            />
                            <PhoneInput<CountryDataItem>
                                data={countryDatas}
                                label={t("introduction.label.phone", lang)}
                                prefixFlagKey="countryISOCode2"
                                prefixValueKey="countryID"
                                prefixLabelKey="countryCode"
                                wrapperProps={{
                                    labelProps: {
                                        className: "profile-form-label",
                                    },
                                }}
                                inputProps={{
                                    value: profile.profileContact ?? "",
                                    rightSectionWidth: 120,
                                    rightSection: (
                                        <ValidateStatusSection
                                            status={profile.profileContactValidate}
                                            openValidate={() => {
                                                setValidateType("contact");
                                                // openModal("profileValidate");
                                            }}
                                        />
                                    ),
                                    onFocus: (e) => {
                                        openPhoneUpdate({
                                            initialPrefixID: countryID,
                                        });
                                        e.target.blur();
                                    },
                                }}
                                prefixProps={{
                                    w: 120,
                                    value: String(profile.mobilePrefixID ?? ""),
                                }}
                                readOnly
                            />
                            <Input.Wrapper
                                label={t("introduction.label.password", lang)}
                                labelProps={{ className: "profile-form-label" }}
                            >
                                <Input
                                    component={"div"}
                                    classNames={{
                                        input: "tw-text-center tw-border-0 tw-p-0",
                                    }}
                                >
                                    <Button
                                        fullWidth
                                        className="profile-update-password-btn"
                                        onClick={() => {
                                            openResetPassword();
                                        }}
                                    >
                                        {t("introduction.label.update_password", lang)}
                                    </Button>
                                </Input>
                            </Input.Wrapper>
                        </Stack>
                    </Grid.Col>
                    <Grid.Col
                        className="tw-px-8"
                        span={{ md: 6, base: 12 }}
                    >
                        <Stack>
                            <AddressSelect<UserProfileResponse, CountryDataItem>
                                label={t("introduction.label.address", lang)}
                                wrapperProps={{
                                    className: "tw-mb-2",
                                    labelProps: {
                                        className: "profile-form-label",
                                    },
                                }}
                                addressFieldMap={{
                                    unit: {
                                        key: "profileAddressUnit",
                                        placeholder: t("introduction.label.unit", lang),
                                    },
                                    street: {
                                        key: "profileAddressStreet",
                                        placeholder: t("introduction.label.street", lang),
                                    },
                                    district: {
                                        key: "profileAddressDistrictId",
                                        placeholder: t("introduction.label.district", lang),
                                    },
                                    city: {
                                        key: "profileAddressCityId",
                                        placeholder: t("introduction.label.city", lang),
                                    },
                                    state: {
                                        key: "profileAddressStateId",
                                        placeholder: t("introduction.label.state", lang),
                                    },
                                    postcode: {
                                        key: "profileAddressPostcode",
                                        placeholder: t("introduction.label.postcode", lang),
                                    },
                                    country: {
                                        key: "profileAddressCountry",
                                        placeholder: t("introduction.label.country", lang),
                                    },
                                }}
                                countrySelectProps={{
                                    data: filteredCountryDatas(),
                                    flagKey: "countryISOCode2",
                                    labelKey: `country${lang}` as keyof CountryDataItem,
                                    valueKey: "countryID",
                                    readOnly: true,
                                }}
                                addressData={profile as UserProfileResponse}
                                onAddressDataChange={(addressData) => {
                                    profile.setProfile(addressData);
                                }}
                            />
                            <ProfileUserData
                                dataKey="professional"
                                value={profile.userProfessional}
                                onChange={(value) => {
                                    profile.setProfileValue("userProfessional", value);
                                }}
                            />
                            <ProfileUserData
                                dataKey="skill"
                                value={profile.userSkill}
                                onChange={(value) => {
                                    profile.setProfileValue("userSkill", value);
                                }}
                            />
                            <Group>
                                <CnaButton
                                    color="basic"
                                    onClick={updateProfile}
                                    className="tw-ml-auto"
                                    loading={updating}
                                    leftSection={<Check weight="bold" />}
                                    disabled={!isUpdateEnabled}
                                >
                                    {t("common.save", lang)}
                                </CnaButton>
                            </Group>
                        </Stack>
                    </Grid.Col>
                </Grid>
            </div>
            <ProfileValidateModal type={validateType} />
        </>
    );
};

export default MemberProfilePage;
