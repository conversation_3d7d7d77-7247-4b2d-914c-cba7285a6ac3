import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from "react";
import { Table, Pagination, Select, Group, ScrollArea } from "@mantine/core";
import { CaretUp, CaretDown } from "@phosphor-icons/react";
import { useMount } from "ahooks";

type Column = {
    header: string;
    accessor: string;
    sortable?: boolean;
    valueFormat?: (value: any) => string | React.ReactNode;
};

type Item = {
    [key: string]: any;
};

type SortField = {
    column: string;
    direction?: "asc" | "desc";
};

type TableProps = {
    columns: Column[];
    fetchData: (
        page: number,
        pageSize: number,
        sortFields?: SortField[],
        ...args: any
    ) => Promise<{ data: Item[]; totalPage: number }>;
    rowActions?: (row: Item) => React.ReactNode;
    defaultPageSize?: number;
    height?: string | number;
};

export type CnaAdminTableRef = {
    refreshData: () => void;
};

const CnaAdminTable = forwardRef<CnaAdminTableRef, TableProps>(
    ({ columns, fetchData, rowActions, defaultPageSize = 10, height }, ref) => {
        const [items, setItems] = useState<Item[]>([]);
        const [totalPage, setTotalPage] = useState<number>(0);
        const [currentPage, setCurrentPage] = useState(1);
        const [pageSize, setPageSize] = useState(defaultPageSize);
        const [loading, setLoading] = useState<boolean>(false);
        const [sortFields, setSortFields] = useState<SortField[]>([]);

        // Fetch data from API
        const fetchTableData = useCallback(async () => {
            setLoading(true);
            try {
                const response = await fetchData(currentPage, pageSize, sortFields);
                setItems(response.data);
                setTotalPage(response.totalPage);
            } catch (error) {
                console.error("Failed to fetch data", error);
            } finally {
                setLoading(false);
            }
        }, [currentPage, pageSize, sortFields, fetchData]);

        // Expose refreshData method via ref
        useImperativeHandle(ref, () => ({
            refreshData: fetchTableData,
        }));

        // 监听 fetchData 变化，重置 currentPage
        useEffect(() => {
            setCurrentPage(1);
        }, [fetchData]);

        // Trigger data fetch on page, page size, sort changes
        useEffect(() => {
            fetchTableData();
        }, [fetchTableData]);

        const handleSort = (column: string, direction: "asc" | "desc") => {
            setSortFields((prev) => {
                const newSortFields = prev.map((field) => {
                    if (field.column === column) {
                        if (field.direction === direction) {
                            return {
                                ...field,
                                direction: undefined,
                            };
                        } else {
                            return {
                                ...field,
                                direction: direction,
                            };
                        }
                    }
                    return field;
                });
                return newSortFields;
            });
        };

        const handlePageChange = (page: number) => {
            setCurrentPage(page);
        };

        const handlePageSizeChange = (size: number) => {
            setPageSize(size);
            setCurrentPage(1);
        };

        useMount(() => {
            // 初始化排序字段
            let initialSortFields = columns
                .filter((col) => col.sortable)
                .map((col) => ({
                    column: col.accessor,
                }));
            setSortFields(initialSortFields);
        });

        return (
            <>
                <ScrollArea
                    className="tw-border tw-flex-1"
                    style={{ height: height || "auto" }}
                >
                    <Table verticalSpacing="md">
                        <Table.Thead className="tw-sticky tw-bg-white tw-shadow-sm">
                            <Table.Tr className="tw-w-full">
                                {columns.map((col) => (
                                    <Table.Th key={col.accessor}>
                                        <Group>
                                            {col.header}
                                            {/* 处理是否渲染排序icon */}
                                            {sortFields.map(
                                                (sort) =>
                                                    sort.column === col.accessor && (
                                                        <Group
                                                            className="tw-flex tw-flex-col tw-items-center tw-gap-1"
                                                            key={sort.column}
                                                        >
                                                            <CaretUp
                                                                className="tw-cursor-pointer"
                                                                size={10}
                                                                weight={
                                                                    sortFields.find(
                                                                        (field) =>
                                                                            field.column ===
                                                                            col.accessor
                                                                    )?.direction === "asc"
                                                                        ? "fill"
                                                                        : "bold"
                                                                }
                                                                onClick={() =>
                                                                    handleSort(col.accessor, "asc")
                                                                }
                                                            />
                                                            <CaretDown
                                                                className="tw-cursor-pointer"
                                                                size={10}
                                                                weight={
                                                                    sortFields.find(
                                                                        (field) =>
                                                                            field.column ===
                                                                            col.accessor
                                                                    )?.direction === "desc"
                                                                        ? "fill"
                                                                        : "bold"
                                                                }
                                                                onClick={() =>
                                                                    handleSort(col.accessor, "desc")
                                                                }
                                                            />
                                                        </Group>
                                                    )
                                            )}
                                        </Group>
                                    </Table.Th>
                                ))}
                                {rowActions && <Table.Th>操作</Table.Th>}
                            </Table.Tr>
                        </Table.Thead>

                        <Table.Tbody className="tw-flex-1">
                            {loading ? (
                                <Table.Tr>
                                    <Table.Td colSpan={columns.length + 1}>
                                        <Group
                                            justify="center"
                                            className="tw-mt-20"
                                        >
                                            Loading...
                                        </Group>
                                    </Table.Td>
                                </Table.Tr>
                            ) : !items.length ? (
                                <Table.Tr>
                                    <Table.Td colSpan={columns.length + 1}>
                                        <Group
                                            justify="center"
                                            className="tw-mt-20"
                                        >
                                            暂无数据
                                        </Group>
                                    </Table.Td>
                                </Table.Tr>
                            ) : (
                                items.map((row, rowIndex) => (
                                    <Table.Tr key={rowIndex}>
                                        {columns.map((col) => (
                                            <Table.Td key={col.accessor}>
                                                {col.valueFormat
                                                    ? col.valueFormat(row)
                                                    : row[col.accessor]}
                                            </Table.Td>
                                        ))}
                                        {rowActions && <Table.Td>{rowActions(row)}</Table.Td>}
                                    </Table.Tr>
                                ))
                            )}
                        </Table.Tbody>
                    </Table>
                </ScrollArea>

                <Group
                    justify="space-between"
                    mt="md"
                    className="tw-flex tw-justify-end tw-mt-4"
                >
                    <Select
                        className="tw-w-[15%]"
                        value={pageSize.toString()}
                        onChange={(value) => handlePageSizeChange(Number(value))}
                        data={[5, 10, 20].map((size) => ({
                            value: size.toString(),
                            label: `${size} rows`,
                        }))}
                    />
                    <Pagination
                        value={currentPage}
                        onChange={handlePageChange}
                        total={totalPage}
                    />
                </Group>
            </>
        );
    }
);

export default CnaAdminTable;
