import { useEventBus } from "@/utils/eventBus";
import { useDeepCompareEffect, useUnmount } from "ahooks";
import { useState } from "react";

/**
 * A hook to control the enabled state of a company application form.
 *
 * This function determines whether the application form should be editable based on the company information and application state.
 * It dynamically sets the form's enabled state. If the conditions are not met, the form will be disabled.
 * The form is also disabled when the component unmounts.
 *
 * @param companyInfo Partial company application information, including company state and progress state.
 * @param applicationState Application state used for comparison to determine if the form should be enabled.
 * @returns The current enabled state of the form.
 */
const useCompanyApplicationFormEnabled = (
    companyInfo: Partial<CompanyApplictaionInfo>,
    applicationState: number
) => {
    // Initialize the form enabled state as true
    const [formEnabled, setFormEnabled] = useState<boolean>(true);

    // Use the global event bus to notify changes in the form enabled state
    const eventBus = useEventBus();

    // Use a deep comparison effect to respond to changes in company information and application state
    useDeepCompareEffect(() => {
        // Determine if the form should be enabled based on the conditions
        if (
            companyInfo.companyState === 1 ||
            companyInfo.companyState === 2 ||
            companyInfo.companyProgressState > applicationState
        ) {
            // Disable the form and notify external components
            console.log("disable the form and notify external components");
            eventBus.emit("project.company.application.submit.enabled", false);
            setFormEnabled(false);
        } else {
            // Enable the form and notify external components
            console.log("enable the form and notify external components");
            eventBus.emit("project.company.application.submit.enabled", true);
            setFormEnabled(true);
        }

    }, [companyInfo, applicationState]);

    // Ensure the form is disabled when the component unmounts
    useUnmount(() => {
        setFormEnabled(false);
    });

    // Return the current enabled state of the form
    return formEnabled;
};

export default useCompanyApplicationFormEnabled;
