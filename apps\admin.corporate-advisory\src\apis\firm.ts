import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const firm = {
    list: async (params: TFirmSearchParams) => {
        const { error, result } = await cnaRequest<TFirmsResponse>(
            "/api/v1/admin/firm/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    review: async (params: TFirmReviewParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/firm/check",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default firm;
