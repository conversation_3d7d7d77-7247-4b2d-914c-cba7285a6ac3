import { Table } from "@mantine/core";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import api from "@/apis";
import { useMount, useRequest } from "ahooks";
import { useContext, useState } from "react";
import { CompanyApplicationContext } from "@/contexts/project";

const CompanyAccountSetting = () => {
    const lang = useSettingStore.use.lang();

    const { companyInfo } = useContext(CompanyApplicationContext);

    const [inited, setInited] = useState(false);

    const { data: accountInfo } = useRequest(
        async () => {
            let info = await api.project.getCompanyAccountInfo(
                companyInfo.companyID
            );

            return info;
        },
        {
            ready: inited,
        }
    );

    useMount(() => {
        setInited(true);
    });

    return (
        <Table
            withTableBorder
            classNames={{ td: "tw-text-xs tw-text-neutral-700" }}
        >
            <Table.Tbody>
                <Table.Tr>
                    <Table.Td>
                        {t(
                            "project.edit.step3.form.label.login_username",
                            lang
                        )}
                        : {accountInfo?.username}
                    </Table.Td>
                </Table.Tr>
                <Table.Tr>
                    <Table.Td>
                        {t(
                            "project.edit.step3.form.label.login_password",
                            lang
                        )}
                        : {accountInfo?.password}
                    </Table.Td>
                </Table.Tr>
                <Table.Tr>
                    <Table.Td>
                        {t("project.edit.step3.form.label.login_website", lang)}
                        : {accountInfo?.url}
                    </Table.Td>
                </Table.Tr>
                <Table.Tr>
                    <Table.Td>
                        {t("project.edit.step3.form.label.login_state", lang)}:{" "}
                        {accountInfo?.loginState}
                    </Table.Td>
                </Table.Tr>
            </Table.Tbody>
        </Table>
    );
};

export default CompanyAccountSetting;
