declare global {
    type BaseOperateResponse = {
        createUser: number;
        createRole: number;
        createTime: string;
        editUser: number;
        editRole: number;
        editTime: string;
    };

    type BasePaginateResponse = {
        currentPage: number;
        perPage: number;
        totalRecord: number;
        totalPage: number;
    };

    type BaseApiResponse<T = any> = {
        code: number;
        data: T;
        message: string;
        status: boolean;
    };

    type TestResponse = {
        external: {
            "X-Api-Key": {
                value: string;
                status: boolean;
            };
            whitelist: {
                value: string;
                status: boolean;
            };
        };
        internal?: [
            {
                status: string;
                user?: {
                    userId: number;
                    username: string;
                };
                token: string;
            }
        ];
    };

    // 列表分页参数
    type TPageQueryParams = {
        page?: number;
        page_size?: number;
        pageSize?: number;
    };
}

export {};
