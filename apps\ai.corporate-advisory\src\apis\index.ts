import cnaRequest from "@code.8cent/utils/cnaRequest";
import email from "./email";
import profile from "./profile";
import project from "./project";
import config from "./config";
import user from "./user";
import gernal from "./gernal";
import register from "./register";
import community from "./community";
import schedule from "./schedule";
import team from "./team";

const api = {
    test: async () => {
        //https://laravel.api.cna.greensmartplanet.cn/api/v1/test
        let res = await cnaRequest<TestResponse>("/api/v1/test", "GET");

        const { result, error } = res;

        if (!error) {
            return result;
        } else {
            return "";
        }
    },
    version: async () => {
        let res = await cnaRequest<string>("/version", "GET");

        const { result, error } = res;

        if (!error) {
            return result;
        } else {
            return "";
        }
    },
    user,
    email,
    profile,
    config,
    project,
    gernal,
    register,
    community,
    schedule,
    team
};

export default api;
