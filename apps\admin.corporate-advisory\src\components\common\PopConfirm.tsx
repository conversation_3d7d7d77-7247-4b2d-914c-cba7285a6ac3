import React, { useState } from "react";
import { Popover, Text, Group } from "@mantine/core";
import { CnaButton } from "@code.8cent/react/components";
import { Info } from "@phosphor-icons/react";

interface PopconfirmProps {
    title?: string;
    content: string;
    onConfirm?: () => void;
    onCancel?: () => void;
    confirmText?: string;
    cancelText?: string;
    children: React.ReactNode;
}

const Popconfirm: React.FC<PopconfirmProps> = ({
    title,
    content,
    onConfirm,
    onCancel,
    confirmText = "Confirm",
    cancelText = "Cancel",
    children,
}) => {
    const [opened, setOpened] = useState(false);

    const handleConfirm = () => {
        setOpened(false);
        if (onConfirm) onConfirm();
    };

    const handleCancel = () => {
        setOpened(false);
        if (onCancel) onCancel();
    };

    return (
        <Popover
            opened={opened}
            onChange={setOpened}
            position="top"
            withArrow
            shadow="md"
            zIndex={370}
        >
            <Popover.Target>
                <div onClick={() => setOpened((o) => !o)}>{children}</div>
            </Popover.Target>
            <Popover.Dropdown>
                <Group gap={1}>
                    <Info
                        size={20}
                        color="#e7970d"
                    />
                    <Text
                        fw={600}
                        c="#e7970d"
                    >
                        {title}
                    </Text>
                </Group>
                <Text
                    size="sm"
                    mb="md"
                >
                    {content}
                </Text>
                <div className="tw-flex tw-justify-end">
                    <CnaButton
                        variant="default"
                        size="xs"
                        onClick={handleCancel}
                        className="tw-mr-2"
                    >
                        {cancelText}
                    </CnaButton>
                    <CnaButton
                        color="basic"
                        size="xs"
                        onClick={handleConfirm}
                    >
                        {confirmText}
                    </CnaButton>
                </div>
            </Popover.Dropdown>
        </Popover>
    );
};

export default Popconfirm;
