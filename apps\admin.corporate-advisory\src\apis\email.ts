import cnaRequest from "@code.8cent/utils/cnaRequest";

const email = {
    sendVerifyEmail: async (email: string) => {
        let sendRes = await cnaRequest<string>(
            "/api/v1/user/sendEmail",
            "POST",
            { email }
        );

        if (sendRes.error) {
            return false;
        } else {
            return true;
        }
    },
    verifyEmail: async (email: string, code: string) => {
        let verifyRes = await cnaRequest<string>(
            "/api/v1/user/verificationEmail",
            "POST",
            {
                email,
                code,
            }
        );

        if (verifyRes.error) {
            return false;
        } else {
            return true;
        }
    },
    sendResetPasswordEmail: async (email: string) => {
        let res = await cnaRequest(
            "/api/v1/password/forgotPsw/sendEmail",
            "POST",
            {
                email,
            }
        );

        const { error, result } = res;

        if (error) {
            return false;
        } else {
            return true;
        }
    },
};

export default email;
