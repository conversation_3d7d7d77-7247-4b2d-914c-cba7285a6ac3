declare global {
    type TAssociatePaidDetailSearchParams = {
        start_date: string;
        end_date: string;
        profileID: string;
        profilePartnerCode: string;
        groupId: number;
        income_type: string;
        divide_type: number;
        check: number;
    } & TPageQueryParams;

    type TAssociatePaidDetail = {
        id: number;
        profileID: number;
        companyID: number;
        type: number;
        projectId: number;
        detailId: number;
        fee: number;
        paytime: string;
        remark: string;
        createtime: string;
        created_at: string;
        deleted_at: string;
        son_id: number;
        project_name: string;
        detail_name: string;
        main: number;
        commission: number;
        divide_type: number;
        divide_percent: number;
        divide_amount: number;
        income_type: string;
        proxy_fax: string;
        divide_profit: number;
        check: number;
        status: number;
        profileName: string;
        profilePartnerCode: string;
        profileNRIC: string;
        groupId: number;
        teamRankName: string;
        sonName: string;
        divideTypeName: string;
        paymentNumber: string;
        put_amount: number;
        put_time: string;
        nopay: number;
        put_total: number;
    };

    type TAssociatePaidDetailResponse = {
        items: TAssociatePaidDetail[];
        paginate: BasePaginateResponse;
        total: {
            totalFee: string;
            totalDividePercent: string;
            totalDivideAmount: string;
            totalProxyFax: string;
            totalDivideProfit: string;
        };
    };

    type TAssociatePaidTotalSearchParams = {
        start_date: string;
        end_date: string;
    } & TPageQueryParams;

    type TAssociatePaidTotal = {
        profileID: number;
        sum_divide_profit: string;
        sum_put_amount: string;
        put_total: string;
        start_balance: string;
        end_balance: string;
        status: number;
        profileName: string;
        profilePartnerCode: string;
        profileNRIC: string;
        groupId: number;
        teamRankName: string;
        sonName: string;
        divideTypeName: string;
    };

    type TAssociatePaidTotalResponse = {
        items: TAssociatePaidTotal[];
        paginate: BasePaginateResponse;
        total: {
            total_divide_profit: string;
            total_put_amount: string;
            total_put_total: string;
            total_start_balance: string;
            total_end_balance: string;
        };
    };

    type TAssociateReceivedDetail = TAssociatePaidDetail;

    type TAssociateReceivedDetailSearchParams = TAssociatePaidTotalSearchParams;

    type TAssociateReceivedDetailResponse = {
        items: TAssociateReceivedDetail[];
        paginate: BasePaginateResponse;
        total: {
            total_fee: string;
            total_put_amount: string;
        };
    };

    type TAssociateReceivedTotalSearchParams = {
        start_date: string;
        end_date: string;
    } & TPageQueryParams;

    type TAssociateReceivedTotal = {
        profileID: number;
        sum_fee: string;
        sum_put_amount: string;
        sum_fee_count: string;
        end_balance: string;
        status: number;
        profileName: string;
        profilePartnerCode: string;
        profileNRIC: string;
        groupId: number;
        teamRankName: string;
        sonName: string;
        divideTypeName: string;
    };

    type TAssociateReceivedTotalResponse = {
        items: TAssociateReceivedTotal[];
        paginate: BasePaginateResponse;
        total: {
            total_fee: string;
            total_put_amount: string;
            total_fee_total: string;
            total_start_balance: string;
            total_end_balance: string;
        };
    };

    type TUploadInvoiceParams = {
        id: number;
        file: File;
    };

    type TCompanyReceivedDetailSearchParams = {
        type?: 1 | 2 | 3;
        profile_name?: string;
        profile_code?: string;
        start_time: string;
        end_time: string;
    } & TPageQueryParams;

    type TCompanyReceivedDetail = TStationFinance & {
        company_info?: {
            name?: string;
            credit_code?: string;
            contact_name?: string;
            contact_position?: string;
        };
    };

    type TCompanyReceivedDetailResponse = {
        data: TCompanyReceivedDetail[];
        last_page: number;
        total: number;
    };
}

export {};
