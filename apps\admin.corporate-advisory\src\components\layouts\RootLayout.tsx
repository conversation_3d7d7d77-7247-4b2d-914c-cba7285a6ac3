import { Outlet, useLocation, useNavigate } from "react-router-dom";
import AlertModal from "../modals/AlertModal";
import ConfirmModal from "../modals/ConfirmModal";
import { useLockFn, useMemoizedFn, useMount } from "ahooks";
import { useEffect, useState } from "react";
import api from "@/apis";
import useDataStore from "@code.8cent/store/data";
import { Center, Group, Stack, Text, Image } from "@mantine/core";
import { Spinner } from "@phosphor-icons/react";

const RootLayout = () => {
    const [inited, setInited] = useState<boolean>(false);

    const { pathname } = useLocation();

    const navigate = useNavigate();

    const checkToken = useMemoizedFn(async () => {
        let token = await window.localForage.getItem("cna-token");

        if (token) {
            navigate("/admin/desk-associates", { replace: true });
            // navigate("/admin/review", { replace: true });
        } else {
            navigate("/account/login", { replace: true });
        }
    });

    useEffect(() => {
        if (pathname === "/") {
            // navigate("/member/profile", { replace: true });
            checkToken();
        }
    }, [pathname]);

    const {
        setLanguages,
        setCurrencies,
        setTimezones,
        setTimeFormats,
        setDateFormats,
        setLanguageStructures,
        setCountryDatas,
        setAreas,
    } = useDataStore();

    const loadFromCache = useLockFn(async () => {
        const cachedOptions =
            await window.localForage.getItem<SettingConfigOptionResponse>(
                "configOptions"
            );

        const cachedDicts = await window.localForage.getItem<
            LanguageStructure[]
        >("languageStructure");

        const cachedCountryDatas = await window.localForage.getItem<
            CountryDataItem[]
        >("countryDatas");

        const cachedAreas = await window.localForage.getItem<AreaData[]>(
            "areas"
        );

        if (cachedOptions && cachedDicts && cachedCountryDatas) {
            console.log("has cached");
            // 使用缓存数据并立即设置inited为true
            setLanguageStructures(cachedDicts ?? []);
            setLanguages(cachedOptions.languageList ?? []);
            setCurrencies(cachedOptions.currencyList ?? []);
            setTimezones(cachedOptions.timezoneList ?? []);
            setTimeFormats(cachedOptions.timeFormatList ?? []);
            setDateFormats(cachedOptions.dateFormatList ?? []);
            setCountryDatas(cachedCountryDatas ?? []);
            setAreas(cachedAreas ?? []);
            setInited(true); // 缓存数据存在，立即初始化
        }
    });

    const fetchDataAndUpdateCache = useLockFn(async () => {
        try {
            // 从API获取最新数据
            const options = await api.config.getConfigOptions();
            const dicts = await api.config.getLanguageStructure();
            const countries = await api.config.getCountryDatas();
            const areas = await api.gernal.getAreaData();

            // 更新store和缓存
            if (options) {
                setLanguages(options.languageList ?? []);
                setCurrencies(options.currencyList ?? []);
                setTimezones(options.timezoneList ?? []);
                setTimeFormats(options.timeFormatList ?? []);
                setDateFormats(options.dateFormatList ?? []);
                await window.localForage.setItem("configOptions", options); // 缓存更新
            }

            if (dicts) {
                setLanguageStructures(dicts ?? []);
                await window.localForage.setItem("languageStructure", dicts); // 缓存语言结构数据
            }

            if (countries) {
                setCountryDatas(countries ?? []);
                await window.localForage.setItem("countryDatas", countries);
            }

            if (areas.length) {
                setAreas(areas ?? []);
                await window.localForage.setItem("areas", areas);
            }

            setInited(true);
        } catch (error) {
            console.error("Error fetching config:", error);
            // 处理错误情况，如提示用户网络问题等
        }
    });

    useMount(() => {
        // api.test();

        loadFromCache().finally(() => {
            fetchDataAndUpdateCache();
        });
    });

    return inited === true ? (
        <>
            <Outlet />
            <AlertModal />
            <ConfirmModal />
        </>
    ) : (
        <Center className="tw-h-screen tw-w-screen">
            <Stack>
                <Image
                    className="tw-mx-auto"
                    src="/images/C&A-logo-icon-blue.svg"
                    alt=""
                    w={120}
                />
                <Text className="tw-text-center tw-my-6 tw-text-xl">
                    陈玮伦合伙人事务所
                </Text>
                <Group>
                    <Spinner className="tw-animate-spin" size={32} />
                    <Text>正在初始化配置...</Text>
                </Group>
            </Stack>
        </Center>
    );
};

export default RootLayout;
