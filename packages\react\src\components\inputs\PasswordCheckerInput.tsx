import { Group, PasswordInput, Stack, Text } from "@mantine/core";
import { Check, X } from "@phosphor-icons/react";
import { useState } from "react";
import { Control, Path, useController, UseFormTrigger } from "react-hook-form";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

type TRuleItem = {
    text: string;
    validate: (password: string) => boolean;
};

type PasswordCheckerInputProps<T = any> = {
    label: string;
    name: Path<T>;
    control: Control<T>;
    trigger?: UseFormTrigger<T>;
    inputProps?: React.ComponentProps<typeof PasswordInput>;
    rulerProps?: React.ComponentProps<typeof Stack>;
    className?: string;
    placeholder?: string;
    rules: TRuleItem[];
};

const PasswordCheckerInput = <T,>({
    label,
    name,
    control,
    trigger,
    inputProps,
    rulerProps,
    className,
    placeholder,
    rules,
}: PasswordCheckerInputProps<T>) => {
    const lang = useSettingStore.use.lang();

    const [isFocused, setIsFocused] = useState(false); // 控制 checklist 显示状态

    // 使用 useController 控制字段状态和数据
    const {
        field: { value: password, onChange, onBlur, ref },
        fieldState: { error },
    } = useController({
        name, // 传入表单字段名
        control,
    });

    const onInput = (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e);
        trigger(name);
    };

    const handleFocus = () => {
        trigger(name);
        setIsFocused(true);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        trigger(name);
        onBlur();
    };

    return (
        <Stack
            gap={0}
            className={typeof className === "string" ? className : ""}
        >
            <PasswordInput
                error={error ? true : false}
                {...inputProps}
                placeholder={placeholder}
                label={label}
                value={String(password) || ""}
                onChange={onChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onInput={onInput}
                ref={ref}
            />

            {isFocused === true && (
                <Stack gap={1} {...rulerProps}>
                    {rules?.map?.((rule, index) => {
                        const isValid = rule.validate(String(password) || "");
                        return (
                            <Group key={index} gap={2}>
                                {isValid ? (
                                    <Check
                                        weight="bold"
                                        size={12}
                                        className="tw-text-green-600"
                                    />
                                ) : (
                                    <X
                                        weight="bold"
                                        size={12}
                                        className="tw-text-[var(--mantine-color-error)]"
                                    />
                                )}
                                <Text
                                    size="xs"
                                    className={
                                        isValid
                                            ? "tw-text-green-600"
                                            : "tw-text-[var(--mantine-color-error)]"
                                    }
                                >
                                    {t(rule.text, lang)}
                                </Text>
                            </Group>
                        );
                    })}
                </Stack>
            )}
        </Stack>
    );
};

export default PasswordCheckerInput;
