import useModalStore from "@/store/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { Modal, Textarea, TextInput } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { Controller, useForm } from "react-hook-form";
import { useShallow } from "zustand/react/shallow";
import { z } from "zod";
import ModalFooter from "@/components/common/ModalFooter";
import { useEffect } from "react";
import api from "@/apis";
import noty from "@code.8cent/react/noty";

const activityInfoSchema = z.object({
    active_name: z.string().min(1, "请输入活动名称"),
    active_time: z.string().min(1, "请输入活动时间"),
    active_place: z.string().min(1, "请输入活动场地"),
});

type ActivityInfoProps = {
    onUpdateSuccess: () => void;
};

const Info = ({ onUpdateSuccess }: ActivityInfoProps) => {
    const activityParams = useModalStore((state) => state.modalParams.activityInfoModal);
    const activity = activityParams?.activity;

    const {
        control,
        handleSubmit,
        formState: { errors },
        reset,
    } = useForm<TActivity>({
        defaultValues: {
            active_name: "",
            active_time: "",
            active_place: "",
        },
        resolver: zodResolver(activityInfoSchema),
    });

    useEffect(() => {
        if (activity) {
            reset({
                active_name: activity.active_name || "",
                active_time: activity.active_time || "",
                active_place: activity.active_place || "",
            });
        }
    }, [activity, reset]);

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.activityInfoModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        reset({
            active_name: "",
            active_time: "",
            active_place: "",
        });
        close("activityInfoModal");
    });

    const onSubmit = useMemoizedFn(async (data) => {
        try {
            if (activity) {
                const payload = {
                    ...data,
                    id: activity.id,
                };
                await api.activity.update(payload);
            } else {
                await api.activity.store(data);
            }
            onUpdateSuccess();
            closeModal();
        } catch (error) {
            noty.error("操作失败");
            console.error(error);
        }
    });

    return (
        <Modal
            title={activity ? "编辑活动" : "新增活动"}
            opened={show}
            size="lg"
            onClose={closeModal}
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Controller
                    control={control}
                    name="active_name"
                    render={({ field }) => (
                        <TextInput
                            label="活动名称"
                            {...field}
                            error={errors.active_name?.message}
                        />
                    )}
                />
                <Controller
                    control={control}
                    name="active_time"
                    render={({ field }) => (
                        <TextInput
                            label="活动时间"
                            {...field}
                            error={errors.active_time?.message}
                        />
                    )}
                />
                <Controller
                    control={control}
                    name="active_place"
                    render={({ field }) => (
                        <Textarea
                            label="活动场地"
                            {...field}
                            error={errors.active_place?.message}
                        />
                    )}
                />
                <ModalFooter
                    buttons={[
                        {
                            key: "submit",
                            label: "提交",
                            type: "submit",
                        },
                    ]}
                />
            </form>
        </Modal>
    );
};

export default Info;
