import api from "@/apis";
import useProfileStore from "@/store/profile";
import { useMemoizedFn, useMount } from "ahooks";
import React, { cloneElement, ReactElement, useState } from "react";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import { ActionIcon, Badge, Drawer, Group, Image, Text, ScrollArea } from "@mantine/core";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import {
    Bell,
    Coin,
    Gear,
    UsersThree,
    IconProps,
    Question,
    SignOut,
    List,
    UserList,
    SquaresFour,
    ClipboardText,
    User,
    Files,
    Briefcase,
    CaretDown,
    Invoice,
    AlignLeft,
    AlignRight,
    HeadCircuit,
    IntersectThree,
    GlobeHemisphereWest,
    FileText,
    UsersFour,
    UserCircleCheck,
    Brandy,
    Handshake,
    Calendar,
    Presentation,
    Exam,
    Lectern,
    UserSwitch,
    ArrowBendUpRight,
    AnchorSimple,
} from "@phosphor-icons/react";
import useModalStore from "@/store/modal";
import PermissionCheck from "../common/PermissionCheck";

const SideBarLinks: {
    name: string;
    path: string;
    icon?: ReactElement<IconProps>;
    permission?: string;
    children?: {
        name: string;
        path: string;
        permission?: string;
        icon?: ReactElement<IconProps>;
        children?: {
            name: string;
            path: string;
            permission?: string;
            icon?: ReactElement<IconProps>;
        }[];
    }[];
}[] = [
        {
            name: "合伙人",
            path: "/admin/desk-associates",
            icon: <User />,
            permission: "partner.index",
            // children: [
            //     {
            //         name: "AI 办公室",
            //         path: "/admin/associates",
            //         icon: <Briefcase />,
            //         permission: "partner.index",
            //     },
            //     {
            //         name: "工作站",
            //         path: "/admin/desk-associates",
            //         icon: <Presentation />,
            //         permission: "partner.index",
            //     },
            // ],
        },
        // {
        //     name: "三三制",
        //     path: "/admin/teams",
        //     icon: <UsersThree />,
        //     permission: "team.index",
        // },
        {
            name: "委员会行业类型",
            path: "/admin/fieldType",
            icon: <AnchorSimple />,
            permission: "documents.index",
        },
        {
            name: "客户",
            path: "/admin/clients",
            icon: <UserList />,
            permission: "companys.index",
        },
        {
            name: "行政",
            path: "/admin/accounts",
            icon: <Briefcase />,
            permission: "partner.set",
        },
        // {
        //     name: "审核",
        //     path: "/admin/review",
        //     icon: <ClipboardText />,
        //     permission: "partner.edit",
        // },
        // {
        //     name: "账单",
        //     path: "/admin/bills",
        //     icon: <Coin />,
        //     permission: "bills.index",
        // },
        {
            name: "福利",
            path: "/admin/benefits",
            icon: <SquaresFour />,
            permission: "benefitInfo.index",
        },
        {
            name: "通知",
            path: "/admin/notices",
            icon: <Bell />,
            permission: "notifications.index",
        },
        {
            name: "资料",
            path: "/admin/documents",
            icon: <Files />,
            permission: "documents.index",
        },

        {
            name: "财务管理",
            path: "/admin/finance",
            icon: <Invoice />,
            children: [
                {
                    name: "合伙人应付明细",
                    path: "/admin/finance/associate-paid-detail",
                    icon: <AlignLeft />,
                    permission: "payment_out.index",
                },
                {
                    name: "合伙人应付汇总",
                    path: "/admin/finance/associate-paid-total",
                    icon: <AlignLeft />,
                    permission: "payment_totalOut.index",
                },
                // {
                //     name: "合伙人应收明细",
                //     path: "/admin/finance/associate-received-detail",
                //     icon: <AlignRight />,
                //     permission: "payment_joinDetail.index",
                // },
                {
                    name: "合伙人应收明细",
                    path: "/admin/station/finance",
                    icon: <Invoice />,
                    permission: "payment_joinDetail.index",
                },
                {
                    name: "合伙人应收汇总",
                    path: "/admin/finance/associate-received-total",
                    icon: <AlignRight />,
                    permission: "payment_joinTotal.index",
                },
                {
                    name: "企业应收明细",
                    path: "/admin/finance/company-received-detail",
                    icon: <AlignRight />,
                    permission: "payment_joinDetail.index",
                },
                {
                    name: "货币管理",
                    path: "/admin/station/currency",
                    icon: <Coin />,
                    permission: "currency.index",
                },
                {
                    name: "合伙人退款",
                    path: "/admin/station/refund",
                    icon: <UserSwitch />,
                    permission: "profile_refund.index",
                },
                {
                    name: "企业退款",
                    path: "/admin/projects/green-earth/refunds",
                    icon: <ArrowBendUpRight />,
                    permission: "gsp_refund.index",
                },
            ],
        },
        {
            name: "项目",
            path: "/admin/projects",
            icon: <IntersectThree />,
            children: [
                {
                    name: "绿智地球",
                    path: "/admin/projects/green-earth",
                    icon: <GlobeHemisphereWest />,
                    children: [
                        {
                            name: "申请列表",
                            path: "/admin/projects/green-earth/applications",
                            icon: <FileText />,
                            permission: "gsp.index",
                        },
                        {
                            name: "线下付款",
                            path: "/admin/projects/green-earth/payment-vouchers",
                            icon: <Invoice />,
                            permission: "gsp.index",
                        },
                    ],
                },
            ],
        },
        {
            name: "数字人",
            path: "/admin/digital-human",
            icon: <HeadCircuit />,
            permission: "digital-human.index",
        },
        {
            name: "联号事务所",
            path: "/admin/firms",
            icon: <UsersFour />,
            permission: "firms.index",
            children: [
                {
                    name: "申请列表",
                    path: "/admin/firms/app",
                    icon: <FileText />,
                    permission: "firms.index",
                },
                {
                    name: "流程管理",
                    path: "/admin/firms/process",
                    icon: <List />,
                    permission: "firms.index",
                },
            ],
        },
        {
            name: "来访登记",
            path: "/admin/visitors",
            icon: <UserCircleCheck />,
            children: [
                {
                    name: "到访申请",
                    path: "/admin/visitors/apps",
                    icon: <Handshake />,
                    permission: "visitor_apply.index",
                },
                {
                    name: "贵宾登记",
                    path: "/admin/visitors/vips",
                    icon: <Brandy />,
                    permission: "visitor_sigin.index",
                },
                {
                    name: "会谈报告",
                    path: "/admin/visitors/reports",
                    icon: <FileText />,
                    permission: "visitor_report.index",
                },
            ],
        },
        {
            name: "活动报名",
            path: "/admin/activities",
            icon: <Calendar />,
            permission: "activity.index",
        },
        {
            name: "面试考试问题",
            path: "/admin/questions",
            icon: <Question />,
            children: [
                {
                    name: "面试问题",
                    path: "/admin/questions/interview",
                    icon: <FileText />,
                    permission: "desk-interview-questions.index",
                },
                {
                    name: "考试问题",
                    path: "/admin/questions/exam",
                    icon: <Exam />,
                    permission: "exam-questions.index",
                },
            ],
        },
        {
            name: "设置",
            path: "/admin/settings",
            icon: <Gear />,
            permission: "setting.index",
        },
    ];

const DashboardLayout: React.FC = () => {
    const setProfile = useProfileStore.use.setProfile();

    const [minNavOpened, setMinNavOpened] = useState<boolean>(false);

    const openConfirm = useModalStore.use.openConfirm();

    // const setLang = useSettingStore.use.setLang();
    // const lang = useSettingStore.use.lang();
    const { lang, setUpSetting } = useSettingStore();

    const { pathname } = useLocation();

    const navigate = useNavigate();

    const logout = useMemoizedFn(async () => {
        openConfirm({
            title: "提示",
            message: "您确定要退出登录吗？",
            onConfirm: async () => {
                await window.localForage.removeItem("cna-token");
                navigate("/account/login");
            },
        });
    });

    useMount(async () => {
        let token = (await window.localForage.getItem("cna-token")) as string;

        if (!token || !token.length) {
            navigate("/account/login");
            return;
        }

        // 获取当前登录的行政用户信息
        let userProfile = await api.user.getAuthUser();

        if (userProfile) {
            setProfile(userProfile);
            // setLang(userProfile.settingLanguage as LangCode);
            setUpSetting({
                lang: userProfile.settingLanguage as LangCode,
                settingCurrency: userProfile.settingCurrency,
                settingTimeFormat: userProfile.settingTimeFormat,
                settingTimezone: userProfile.settingTimezone,
                settingDateFormat: userProfile.settingDateFormat,
                settingLanguage: userProfile.settingLanguage as LangCode,
                settingNotifyEmergency: userProfile.settingNotifyEmergency,
                settingNotifyImportanceUpdate: userProfile.settingNotifyImportanceUpdate,
                settingNotifyJoinInvestigate: userProfile.settingNotifyJoinInvestigate,
                settingNotifyRecPrivateMsg: userProfile.settingNotifyRecPrivateMsg,
                settingNotifySafeUpdated: userProfile.settingNotifySafeUpdated,
                settingNotifySuspiciousOperation: userProfile.settingNotifySuspiciousOperation,
                settingNotifySystemUpdate: userProfile.settingNotifySystemUpdate,
            });
        } else {
            navigate("/account/login");
        }
    });

    const NavLinks = () => {
        const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>(() => {
            const initialState: { [key: string]: boolean } = {};
            SideBarLinks.forEach((link) => {
                if (link.children) {
                    // 检查一级子菜单
                    const shouldExpand = link.children.some((child) => {
                        const firstLevelMatch =
                            pathname === child.path || pathname.startsWith(child.path + "/");
                        // 检查二级子菜单
                        const secondLevelMatch = child.children?.some(
                            (subChild) =>
                                pathname === subChild.path ||
                                pathname.startsWith(subChild.path + "/")
                        );
                        return firstLevelMatch || secondLevelMatch;
                    });
                    if (shouldExpand) {
                        initialState[link.path] = true;
                        // 如果一级菜单展开，同时检查其子菜单是否需要展开
                        link.children.forEach((child) => {
                            if (
                                child.children?.some(
                                    (subChild) =>
                                        pathname === subChild.path ||
                                        pathname.startsWith(subChild.path + "/")
                                )
                            ) {
                                initialState[child.path] = true;
                            }
                        });
                    }
                }
            });
            return initialState;
        });

        const toggleExpand = (path: string) => {
            setExpandedItems((prev) => ({
                ...prev,
                [path]: !prev[path],
            }));
        };

        return (
            <>
                <ScrollArea
                    classNames={{
                        scrollbar: "tw-bg-basic-5",
                        thumb: "tw-bg-basic-3",
                    }}
                    type="hover"
                    scrollbars="y"
                    scrollHideDelay={500}
                    scrollbarSize={6}
                    className="tw-flex-1 tw-py-3 tw-overflow-y-auto tw-overflow-x-hidden tw-w-full"
                >
                    {SideBarLinks.map((link, index) => {
                        const icon = cloneElement(link.icon, {
                            size: 32,
                            weight: "thin",
                        } as IconProps);

                        return (
                            <PermissionCheck
                                permission={link.permission}
                                key={index}
                            >
                                <>
                                    <div
                                        className={`tw-flex tw-px-8 tw-py-2 tw-items-center tw-justify-start hover:tw-bg-basic-8 hover:tw-scale-110 tw-transition-all tw-ease-linear tw-cursor-pointer ${pathname === link.path ||
                                            pathname.startsWith(link.path + "/")
                                            ? "tw-text-gray-50 tw-font-bold tw-bg-basic-7"
                                            : "tw-text-gray-300"
                                            }`}
                                        onClick={() =>
                                            link.children
                                                ? toggleExpand(link.path)
                                                : navigate(link.path)
                                        }
                                    >
                                        {icon}
                                        <Text className="tw-flex-1 tw-text-left tw-ml-6 tw-text-sm">
                                            {t(link.name, lang)}
                                        </Text>
                                        {link.children && (
                                            <CaretDown
                                                size={16}
                                                className={`tw-transition-transform ${expandedItems[link.path] ? "tw-rotate-180" : ""
                                                    }`}
                                            />
                                        )}
                                    </div>
                                    {link.children && expandedItems[link.path] && (
                                        <div>
                                            {link.children.map((child, childIndex) => (
                                                <PermissionCheck
                                                    permission={child.permission}
                                                    key={childIndex}
                                                >
                                                    <>
                                                        <div
                                                            className={`tw-flex tw-px-12 tw-py-2 tw-items-center tw-justify-start hover:tw-bg-basic-8 hover:tw-scale-110 tw-transition-all tw-ease-linear tw-cursor-pointer ${pathname === child.path ||
                                                                pathname.startsWith(
                                                                    child.path + "/"
                                                                )
                                                                ? "tw-text-gray-50 tw-font-bold tw-bg-basic-7"
                                                                : "tw-text-gray-300"
                                                                }`}
                                                            onClick={() => {
                                                                child.children
                                                                    ? toggleExpand(child.path)
                                                                    : navigate(child.path);
                                                                !child.children &&
                                                                    setMinNavOpened(false);
                                                            }}
                                                        >
                                                            {child.icon}
                                                            <Text className="tw-flex-1 tw-text-left tw-ml-4 tw-text-sm">
                                                                {t(child.name, lang)}
                                                            </Text>
                                                            {child.children && (
                                                                <CaretDown
                                                                    size={16}
                                                                    className={`tw-transition-transform ${expandedItems[child.path]
                                                                        ? "tw-rotate-180"
                                                                        : ""
                                                                        }`}
                                                                />
                                                            )}
                                                        </div>
                                                        {child.children &&
                                                            expandedItems[child.path] && (
                                                                <div>
                                                                    {child.children.map(
                                                                        (
                                                                            subChild,
                                                                            subChildIndex
                                                                        ) => (
                                                                            <PermissionCheck
                                                                                permission={
                                                                                    subChild.permission
                                                                                }
                                                                                key={subChildIndex}
                                                                            >
                                                                                <div
                                                                                    className={`tw-flex tw-px-16 tw-py-2 tw-items-center tw-justify-start hover:tw-bg-basic-8 hover:tw-scale-110 tw-transition-all tw-ease-linear tw-cursor-pointer ${pathname ===
                                                                                        subChild.path
                                                                                        ? "tw-text-gray-50 tw-font-bold tw-bg-basic-7"
                                                                                        : "tw-text-gray-300"
                                                                                        }`}
                                                                                    onClick={() => {
                                                                                        navigate(
                                                                                            subChild.path
                                                                                        );
                                                                                        setMinNavOpened(
                                                                                            false
                                                                                        );
                                                                                    }}
                                                                                >
                                                                                    {subChild.icon}
                                                                                    <Text className="tw-flex-1 tw-text-left tw-ml-4 tw-text-sm">
                                                                                        {t(
                                                                                            subChild.name,
                                                                                            lang
                                                                                        )}
                                                                                    </Text>
                                                                                </div>
                                                                            </PermissionCheck>
                                                                        )
                                                                    )}
                                                                </div>
                                                            )}
                                                    </>
                                                </PermissionCheck>
                                            ))}
                                        </div>
                                    )}
                                </>
                            </PermissionCheck>
                        );
                    })}
                </ScrollArea>
                <div className="tw-pt-3 tw-pb-4">
                    {/* <div
                        className={`tw-flex tw-px-4 tw-items-center tw-text-gray-500`}
                    >
                        <Question size={20} />
                        <Text
                            className="tw-text-sm tw-ml-4"
                            component={Link}
                            to={{ pathname: "/member/ai-assist" }}
                        >
                            {t("navigation.ai_assistant", lang)}
                        </Text>
                    </div> */}
                    <div
                        className={`tw-mt-2 tw-flex tw-px-4 tw-items-center tw-text-gray-200 tw-cursor-pointer`}
                        onClick={logout}
                    >
                        <SignOut size={20} />
                        <Text className="tw-text-sm tw-ml-4">{t("navigation.log_out", lang)}</Text>
                    </div>
                </div>
            </>
        );
    };

    return (
        <div className="tw-flex tw-h-[100vh] tw-w-[100vw] tw-flex-col md:tw-flex-row">
            {/*
                This is the side navigation bar on the left side of the screen.
                It is hidden on small screens and shows up on larger screens.
                It contains a logo, a heading, and a list of links.
            */}
            <div className="md:tw-w-[200px] tw-transition-all md:tw-flex lg:tw-w-[240px] tw-border-r tw-bg-basic-5 tw-hidden tw-flex-col tw-w-0">
                <div>
                    <Image
                        src="/images/navbar-logo.png"
                        w={"100%"}
                        className="tw-mx-auto"
                    />
                    {/* <Text className="tw-text-center tw-mt-3 tw-text-xl">
                        {t("dashboard.title", lang)}
                    </Text> */}
                </div>
                <NavLinks />
            </div>
            {/*
                This is the top navigation bar that shows up on small screens.
                It is hidden on larger screens and shows up on smaller screens.
                It contains a logo and a button to open or close the side navigation bar.
            */}
            <Group
                className="md:tw-hidden tw-py-2 tw-border-b tw-px-6 tw-bg-basic-5"
                justify="space-between"
            >
                <Image
                    src="/images/navbar-logo-sm.png"
                    w={"60%"}
                />

                <ActionIcon
                    variant="transparent"
                    color="white"
                    onClick={() => {
                        setMinNavOpened(true);
                    }}
                >
                    <List size={36} />
                </ActionIcon>
            </Group>
            {/*
                This is the main content area of the page. It will display
                the content of the currently selected page. It takes up
                the majority of the screen, and is scrollable if the content
                is too large to fit on the screen.
            */}
            <div className="tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden">
                <Outlet />
            </div>
            {/*
                This is the Drawer component from Mantine.
                It is only visible on small screens, and is used
                to display the side navigation bar when the user
                clicks on the hamburger button on the top right
                of the screen.
            */}
            <Drawer
                opened={minNavOpened}
                onClose={() => {
                    setMinNavOpened(false);
                }}
                size={"80%"}
                className="md:tw-hidden"
                classNames={{
                    content: "tw-bg-basic-5 tw-flex tw-flex-col",
                    header: "tw-py-0 tw-bg-basic-5",
                    body: "tw-flex-1 tw-flex tw-flex-col",
                    close: "tw-text-gray-50 hover:tw-bg-basic-6",
                }}
                title={
                    <Group className="tw-py-2">
                        <Image
                            src="/images/navbar-logo-sm.png"
                            w={"80%"}
                        />
                    </Group>
                }
            >
                <NavLinks />
            </Drawer>
        </div>
    );
};

export default DashboardLayout;
