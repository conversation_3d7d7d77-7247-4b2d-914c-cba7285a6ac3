import CnaButton from "@code.8cent/react/components/CnaButton";
import ScrollArea from "@code.8cent/react/components/ScrollArea";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import useSettingStore from "@code.8cent/store/setting";
import CountrySelect from "@code.8cent/react/components/CountrySelect";
import {
    Avatar,
    Center,
    Group,
    Radio,
    Stack,
    Text,
    TextInput,
    Title,
    Highlight,
} from "@mantine/core";
import { useGetState, useRequest, useSetState } from "ahooks";
import api from "@/apis";
import { getData, sleepMs } from "@code.8cent/utils";
import { CaretCircleRight } from "@phosphor-icons/react";
import { z } from "zod";
import { set, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import ProfileInfoModal from "@/components/modals/community/ProfileInfoModal";
import { <PERSON><PERSON><PERSON><PERSON>, ProfileAvatar } from "@code.8cent/react/components";

const searchMemberSchema = z.object({
    search_type: z.string().min(1),
    nationality_id: z.string().min(1),
    keyword: z.string().min(1),
});

type SearchParamInput = Omit<TCommunitySearchParams, "page_size" | "page">;

const initialSearchParams: SearchParamInput = {
    search_type: "0",
    nationality_id: "44",
    keyword: "",
};

const MemberCommunityPage = () => {
    const lang = useSettingStore.use.lang();

    const filteredCountryDatas = useDataStore.use.filteredCountryDatas();

    const [page, setPage, getPage] = useGetState<number>(1);

    const [hasMore, setHasMore, getHasMore] = useGetState<boolean>(false);

    const [profileDetail, setProfileDetail] = useSetState<{
        visible: boolean;
        profile: Partial<UserProfileResponse>;
    }>({
        visible: false,
        profile: {},
    });

    const {
        handleSubmit,
        register,
        formState: { errors },
        getValues,
        setValue,
    } = useForm<SearchParamInput>({
        resolver: zodResolver(searchMemberSchema),
        defaultValues: initialSearchParams,
    });

    const [result, setResult, getResult] = useGetState<MemberListResponse["items"]>([]);

    const { run: searchMember, loading } = useRequest(
        async (params: Omit<TCommunitySearchParams, "page_size">) => {
            let members = await api.community.search({
                ...params,
                page_size: 6,
            });

            if (params.page === 1) {
                setPage(1);
                setResult(members?.items || []);
            } else {
                setResult((prev) => [...prev, ...(members?.items || [])]);
            }

            setHasMore(false);

            await sleepMs(300);

            const totalRecord = members?.paginate?.totalRecord ?? 0;

            const resultCount = getResult().length;

            console.log(resultCount, totalRecord);

            if (resultCount >= totalRecord) {
                console.log("has no more");
                setHasMore(false);
            } else {
                console.log("has more");
                setHasMore(true);
            }
        },
        { manual: true }
    );

    const onReachBottom = () => {
        if (result.length > 0 && getHasMore() === true) {
            let page = getPage();

            page += 1;

            setPage(page);

            searchMember({
                ...getValues(),
                page,
            });
        }
    };

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title={t("community.title", lang)}
                desc={t("navigation.community.system.quote", lang)}
            />

            <Stack
                gap={0}
                className="tw-border tw-p-6 tw-rounded-lg"
            >
                <Group>
                    <TextInput
                        placeholder={t("community.placeholder.keyword", lang)}
                        className="tw-flex-1"
                        {...register("keyword")}
                        error={errors.keyword ? true : false}
                    />
                    <CountrySelect<CountryDataItem>
                        className="tw-flex-1"
                        data={filteredCountryDatas()}
                        flagKey="countryISOCode2"
                        labelKey={`country${lang}` as keyof CountryDataItem}
                        valueKey="countryID"
                        value={getValues("nationality_id") as string}
                        allowDeselect={false}
                        searchable
                        onChange={(value) => {
                            setValue("nationality_id", value, {
                                shouldValidate: true,
                            });
                        }}
                    />
                    <CnaButton
                        color="basic"
                        w={180}
                        onClick={handleSubmit(
                            (data) => {
                                searchMember({
                                    ...data,
                                    page: 1,
                                });
                            },
                            (error) => {
                                console.log(error);
                            }
                        )}
                    >
                        {t("community.btn.search", lang)}
                    </CnaButton>
                </Group>
                <Radio.Group
                    className="tw-mt-8"
                    value={getValues("search_type")}
                    onChange={(value) => setValue("search_type", value, { shouldValidate: true })}
                >
                    <Stack gap={18}>
                        <Radio
                            color="basic"
                            value="0"
                            label={t("community.radio.client_resource", lang)}
                        />
                        <Radio
                            color="basic"
                            value="1"
                            label={t("community.radio.professional_resource", lang)}
                        />
                        <Radio
                            color="basic"
                            value="2"
                            label={t("community.radio.skill_resource", lang)}
                        />
                    </Stack>
                </Radio.Group>
            </Stack>

            <ScrollArea
                className="tw-mt-3 tw-flex-1"
                onScrollBottom={onReachBottom}
            >
                {result?.length > 0 && (
                    <Stack>
                        {result.map((item, _idx) => {
                            return (
                                <Group
                                    key={_idx}
                                    align="center"
                                    justify="space-between"
                                    className="tw-border tw-rounded-md tw-p-5 tw-cursor-pointer"
                                    onClick={() => {
                                        setProfileDetail({
                                            visible: true,
                                            profile: item,
                                        });
                                    }}
                                >
                                    <Group gap={"xl"}>
                                        <ProfileAvatar
                                            className="tw-max-w-[80px]"
                                            src={`${window.api_base_url}/${item.profileAvatar}`}
                                        />
                                        <Stack gap={1}>
                                            <Title
                                                order={5}
                                                fw={"normal"}
                                            >
                                                {item.profileName}
                                            </Title>
                                            <Text size="sm">{item.profileEmail}</Text>
                                            <Highlight
                                                highlight={getValues("keyword")}
                                                size="sm"
                                                className="tw-text-gray-400"
                                            >
                                                {item.match_word}
                                            </Highlight>
                                        </Stack>
                                    </Group>
                                    <CaretCircleRight
                                        className="tw-text-gray-400 tw-cursor-pointer"
                                        size={30}
                                    />
                                </Group>
                            );
                        })}
                    </Stack>
                )}
                {!result.length && (
                    <Text
                        ta="center"
                        className="tw-mt-32"
                    >
                        暂无资料
                    </Text>
                )}
            </ScrollArea>
            <ProfileInfoModal
                opened={profileDetail.visible}
                profile={profileDetail.profile}
                onClose={() => setProfileDetail({ visible: false, profile: {} })}
            />
        </Stack>
    );
};

export default MemberCommunityPage;
