import api from "@/apis";
import CnaButton from "@code.8cent/react/components/CnaButton";
import useModalStore from "@/store/modal";
import useRegisterStore from "@/store/register";
import { useEventBus } from "@/utils/eventBus";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    Group,
    Input,
    Select,
    TextInput,
    Title,
    Text,
    Stack,
} from "@mantine/core";
import { useRequest } from "ahooks";
import { useContext } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { Link } from "react-router-dom";
import { z } from "zod";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import PhoneInput from "@code.8cent/react/components/PhoneInput";
import useDataStore from "@code.8cent/store/data";
import CountrySelect from "@code.8cent/react/components/CountrySelect";

type RegisterFormInput = {
    email: string;
    phone: string;
    prefixID: string;
    nationalityID: string;
};

const registerSchema = z.object({
    email: z.string().email("form.email.incorrect"),
    prefixID: z.string().min(1, "form.country.code"),
    phone: z.string().regex(/^\d+$/g, "form.phone.number.incorrect"),
    nationalityID: z.string().min(1, "form.country.code"),
});

const initalRegisterFormValues = {
    email: "",
    phone: "",
    prefixID: "",
    nationalityID: "",
};

const RegisterEmailPhoneForm = () => {
    const lang = useSettingStore.use.lang();

    const {countryDatas, filteredCountryDatas} = useDataStore()

    const bus = useEventBus();

    const alert = useModalStore.use.openAlert();

    const setRegisterInfo = useRegisterStore.use.setRegisterInfoValue();

    const {
        register,
        setValue,
        formState: { errors, isValid },
        handleSubmit,
        getValues,
    } = useForm<RegisterFormInput>({
        defaultValues: initalRegisterFormValues,
        resolver: zodResolver(registerSchema),
    });

    const { run: submit, loading: submitting } = useRequest(
        async (data: RegisterFormInput) => {
            let account = await api.register.register(data);

            if ("error" in account) {
                alert(t("form.registeration", lang), account.message, "danger");
                return;
            }

            if ("token" in account) {
                setRegisterInfo("token", account.token);
                bus.emit("account.register.step", 3);
            } else {
                setRegisterInfo("prefixID", account.prefixID);
                setRegisterInfo("email", account.email);
                setRegisterInfo("phone", account.phone);
                setRegisterInfo("nationalityID", account.nationalityID);
                bus.emit("account.register.step", 2);
            }
        },
        {
            manual: true,
        }
    );

    return (
        <Stack>
            <form onSubmit={handleSubmit(submit)}>
                <TextInput
                    label={t("form.email.address", lang)}
                    placeholder="请输入邮箱地址"
                    className="tw-mb-3"
                    required
                    {...register("email")}
                    error={
                        errors.email ? t("form.email.incorrect", lang) : false
                    }
                />
                <PhoneInput<CountryDataItem>
                    label={t("introduction.label.phone", lang)}
                    data={filteredCountryDatas()}
                    prefixFlagKey="countryISOCode2"
                    prefixValueKey="countryID"
                    prefixLabelKey="countryCode"
                    wrapperProps={{
                        className: "tw-mb-3"
                    }}
                    prefixProps={{
                        searchable: true,
                        allowDeselect: false,
                        w: 120,
                        ...register("prefixID"),
                        onChange: (value) => {
                            setValue("prefixID", value, {
                                shouldValidate: true,
                            });
                        },
                        value: getValues("prefixID"),
                        error: errors.prefixID ? true : false,
                    }}
                    inputProps={{
                        ...register("phone"),
                        placeholder: "请输入电话号码",
                        error: errors.phone ? true : false,
                    }}
                />
                <CountrySelect
                    flagKey="countryISOCode2"
                    labelKey={`country${lang}` as keyof CountryDataItem}
                    valueKey="countryID"
                    data={countryDatas}
                    label={t("introduction.nationality", lang)}
                    placeholder="请选择国籍"
                    {...register("nationalityID")}
                    value={getValues("nationalityID")}
                    onChange={(value) => {
                        setValue("nationalityID", value, {
                            shouldValidate: true,
                        });
                    }}
                    error={errors.nationalityID ? true : false}
                    searchable
                    allowDeselect={false}
                />
                <CnaButton
                    color="basic"
                    className="tw-mt-5"
                    fullWidth
                    type="submit"
                    loading={submitting}
                    // disabled={!isValid}
                >
                    {t("form.sign.up", lang)}
                </CnaButton>
            </form>
            <Group justify="end">
                <Text
                    component={Link}
                    to={{ pathname: "/account/login" }}
                    size="sm"
                    className="!tw-text-neutral-600 hover:!tw-text-neutral-950"
                >
                    {t("form.account.login", lang)}
                </Text>
            </Group>
        </Stack>
    );
};

export default RegisterEmailPhoneForm;
