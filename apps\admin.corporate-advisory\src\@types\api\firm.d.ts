declare global {
    type TFirmSearchParams = {
        status?: string;
        keyword: string | null;
    } & TPageQueryParams;

    type TFirm = {
        id: number;
        profile_id: number;
        profileName?: string;
        profileEmail?: string;
        pre_id: number;
        code: string;
        file1: string;
        file2: string;
        file3: string;
        file4: string;
        file5: string;
        file6: string;
        file7: string;
        file8?: string;
        file9?: string;
        status: number;
        created_at: string;
        updated_at: string;
        company_name: string;
        credit_code: string;
        registered_capital: string;
        paid_capital: string;
        office_type: number;
        office_address: string;
        intro: string;
        range: string;
        main_business: string;
        leader_intro: string;
        honors: string;
        customer_name: string;
        lawsuit: string;
        income: string;
        cost: string;
        total_assets: string;
        total_liabilities: string;
    }

    type TFirmReviewParams = {
        id: number;
        status: number; // 1: 通过，2: 不通过
    }

    type TFirmsResponse = {
        items: TFirm[];
        paginate: BasePaginateResponse;
    };
}

export {};
