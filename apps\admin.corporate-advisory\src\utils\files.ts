import noty from "@code.8cent/react/noty";
import { cnaRequest } from "@code.8cent/utils";

/**
 * 下载文件
 * @param path 文件路径
 * @param type 文件类型
 */
export async function downloadFile(path: string, type: "local" | "remote", api_url: string = window.api_base_url) {
    try {
        const response = await cnaRequest(
            `${api_url}/api/v1/admin/file/download`,
            "POST",
            { file_path: path, type },
            // 指定响应类型为 Blob
            { responseType: "blob" }
        );

        // 获取文件内容（Blob）
        const blob = new Blob([response.result?.data]);

        // 创建一个临时链接
        const url = window.URL.createObjectURL(blob);

        // 创建一个 <a> 标签并触发下载
        const a = document.createElement("a");
        a.href = url;
        // 下载的文件名
        const fileName = path.split("/").pop() || "download";
        a.download = fileName;
        document.body.appendChild(a);
        a.click();

        // 清理临时链接
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        console.error("下载失败:", error);
        noty.error("下载失败，请稍后重试");
    }
}

export async function downloadBlob(blobRes: Blob, fileName: string) {
    try {
        // 获取文件内容（Blob）
        const blob = new Blob([blobRes], {type: blobRes.type});

        // 创建一个临时链接
        const url = window.URL.createObjectURL(blob);

        // 创建一个 <a> 标签并触发下载
        const a = document.createElement("a");
        a.href = url;
        // 下载的文件名
        a.download = fileName;
        document.body.appendChild(a);
        a.click();

        // 清理临时链接
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        console.error("下载失败:", error);
        noty.error("下载失败，请稍后重试");
    }
}
