import useModalStore from "@/store/modal";
import React, { useState, useEffect, useMemo } from "react";
import { Modal, Stack, TextInput, SimpleGrid, Textarea, Select } from "@mantine/core";
import { DateTimePicker } from "@mantine/dates";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Check, X, CalendarBlank } from "@phosphor-icons/react";
import { CnaButton } from "@code.8cent/react/components";
import { useShallow } from "zustand/react/shallow";
import ModalFooter from "@/components/common/ModalFooter";
import api from "@/apis";
import noty from "@code.8cent/react/noty";
import dayjs from "dayjs";

interface NoticeInfoProps {
    notice: TNotice | null;
    onClose: () => void;
    onUpdateSuccess: () => void;
}

const LanguageButton = ({ title, active, onClick }) => (
    <CnaButton
        size="sm"
        variant={active ? "filled" : "default"}
        onClick={onClick}
    >
        {title}
    </CnaButton>
);

const Info = React.memo(({ notice, onClose, onUpdateSuccess }: NoticeInfoProps) => {
    const lang = useSettingStore.use.lang();
    const openConfirm = useModalStore.use.openConfirm();
    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.noticeInfoModal,
            close: state.close,
        }))
    );

    // 初始化数据
    const initialNoticeData = useMemo(
        () => ({
            ZH: {
                title: notice?.notificationTitleZH || "",
                content: notice?.notificationDescriptionZH || "",
            },
            ZT: {
                title: notice?.notificationTitleZT || "",
                content: notice?.notificationDescriptionZT || "",
            },
            EN: {
                title: notice?.notificationTitleEN || "",
                content: notice?.notificationDescriptionEN || "",
            },
            MS: {
                title: notice?.notificationTitleMS || "",
                content: notice?.notificationDescriptionMS || "",
            },
        }),
        [notice]
    );

    const [noticeData, setNoticeData] = useState(initialNoticeData);
    const [activeLanguage, setActiveLanguage] = useState("ZH");
    const [validDatetime, setValidDatetime] = useState(dayjs().add(7, "day").format("YYYY-MM-DD HH:mm:ss"));
    const [noticeType, setNoticeType] = useState("0");
    const [publishDatetime, setPublishDatetime] = useState(dayjs().format("YYYY-MM-DD HH:mm:ss"));

    useEffect(() => {
        setNoticeData(initialNoticeData);
        // 初始化数据
        setActiveLanguage("ZH");
        setNoticeType(notice?.notificationDepartment || "0");
        setValidDatetime(notice?.notificationValidity || dayjs().add(7, "day").format("YYYY-MM-DD HH:mm:ss"));
        setPublishDatetime(notice?.createTime || dayjs().format("YYYY-MM-DD HH:mm:ss"));
    }, [notice, initialNoticeData]);

    const updateNoticeData = (key, field, value) => {
        setNoticeData((prev) => ({
            ...prev,
            [key]: {
                ...prev[key],
                [field]: value,
            },
        }));
    };

    const noticeTitle = noticeData[activeLanguage]?.title || "";
    const noticeDescription = noticeData[activeLanguage]?.content || "";

    const handleTitleChange = (e) => updateNoticeData(activeLanguage, "title", e.target.value);
    const handleDescriptionChange = (e) =>
        updateNoticeData(activeLanguage, "content", e.target.value);
    const handleLanguageButtonClick = (key) => setActiveLanguage(key);

    const handleConfirmSave = async () => {
        try {
            const payload = {
                notificationTitleZH: noticeData.ZH.title,
                notificationTitleZT: noticeData.ZT.title,
                notificationTitleEN: noticeData.EN.title,
                notificationTitleMS: noticeData.MS.title,
                notificationDescriptionZH: noticeData.ZH.content,
                notificationDescriptionZT: noticeData.ZT.content,
                notificationDescriptionEN: noticeData.EN.content,
                notificationDescriptionMS: noticeData.MS.content,
                type: noticeType,
            };

            let res = false;
            if (notice) {
                res = await api.notice.update(payload, notice.notificationID);
            } else {
                res = await api.notice.store({
                    ...payload,
                    notificationValidity: validDatetime,
                    createTime: publishDatetime,
                });
            }

            if (res) {
                noty.success("保存成功");
                onUpdateSuccess();
                close("noticeInfoModal");
            }
        } catch (error) {
            noty.error("保存失败");
            console.error("保存失败:", error);
        }
    };

    const handleSave = () => {
        openConfirm({
            title: "更新邮件模版内容",
            message: "您确定此操作么？",
            onConfirm: handleConfirmSave,
        });
    };

    const closeModal = () => {
        onClose();
        close("noticeInfoModal");
    };

    const modalFooterButtons = [
        {
            key: "save",
            label: "保存",
            leftSection: <Check size={16} />,
            onClick: handleSave,
        },
        {
            key: "close",
            label: "关闭",
            style: "outline",
            leftSection: <X size={16} />,
            onClick: () => closeModal(),
        },
    ];

    const languageButtons = [
        { key: "ZH", title: "中文 (简体)" },
        { key: "ZT", title: "中文 (繁体)" },
        { key: "EN", title: "英文" },
        { key: "MS", title: "马来文" },
    ].map((item) => (
        <LanguageButton
            key={item.key}
            title={item.title}
            active={item.key === activeLanguage}
            onClick={() => handleLanguageButtonClick(item.key)}
        />
    ));

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            title="创建通知"
            size="xl"
        >
            <Stack gap="md">
                <SimpleGrid cols={4}>{languageButtons}</SimpleGrid>

                <TextInput
                    label="通知标题"
                    labelProps={{
                        className: "profile-form-label",
                    }}
                    placeholder="请输入通知标题"
                    value={noticeTitle}
                    onChange={handleTitleChange}
                />

                <Textarea
                    label="通知详情"
                    labelProps={{
                        className: "profile-form-label",
                    }}
                    placeholder="输入通知详情"
                    autosize
                    minRows={4}
                    value={noticeDescription}
                    onChange={handleDescriptionChange}
                />

                <SimpleGrid cols={2}>
                    <DateTimePicker
                        label="有效期"
                        labelProps={{
                            className: "profile-form-label",
                        }}
                        leftSection={<CalendarBlank size={16} />}
                        placeholder="请选择有效期"
                        withSeconds
                        valueFormat="YYYY-MM-DD HH:mm:ss"
                        value={dayjs(validDatetime).toDate()}
                        onChange={(value) =>
                            setValidDatetime(dayjs(value).format("YYYY-MM-DD HH:mm:ss"))
                        }
                    />
                    <Select
                        label="针对群体"
                        labelProps={{
                            className: "profile-form-label",
                        }}
                        placeholder="请选择针对群体"
                        value={noticeType}
                        allowDeselect={false}
                        data={[
                            { value: "0", label: "所有用户" },
                            { value: "1", label: "合伙人" },
                            { value: "3", label: "律师" },
                        ]}
                        onChange={(value) => setNoticeType(value)}
                    />
                </SimpleGrid>

                <DateTimePicker
                    label="发布日期"
                    labelProps={{
                        className: "profile-form-label",
                    }}
                    leftSection={<CalendarBlank size={16} />}
                    placeholder="请选择发布日期"
                    withSeconds
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    value={dayjs(publishDatetime).toDate()}
                    onChange={(value) =>
                        setPublishDatetime(dayjs(value).format("YYYY-MM-DD HH:mm:ss"))
                    }
                />

                <ModalFooter
                    // timelineContent="最近修改: Amos Wu (2024-11-18 12:00:00)"
                    buttons={modalFooterButtons}
                />
            </Stack>
        </Modal>
    );
});

export default Info;
