import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "@mantine/core/styles.css";
import "./index.css";
import { createTheme, MantineProvider } from "@mantine/core";
import { RouterProvider } from "react-router-dom";
import router from "./router";
import localForage from "localforage";
import "@mantine/dropzone/styles.css";
import "@mantine/dates/styles.css";
import "@mantine/notifications/styles.css";
import { ModalsProvider } from "@mantine/modals";
import { EventBusProvider } from "./utils/eventBus";
import { Notifications } from "@mantine/notifications";
import colors from "../theme";
import "dayjs/locale/ms";
import "dayjs/locale/en";
import "dayjs/locale/zh";
import "dayjs/locale/zh-tw";
import useSettingStore from "@code.8cent/store/setting";
import { DatesProvider } from "@mantine/dates";

window.localForage = localForage;

const theme = createTheme({
    primaryColor: "basic",
    primaryShade: 5,
    breakpoints: {
        sm: "640px",
        "2xl": "1536px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
    },
    colors: {
        ...colors,
    },
});

const App = () => {
    const { lang } = useSettingStore();
    return (
        <StrictMode>
            <EventBusProvider>
                <DatesProvider
                    settings={{
                        locale: lang === "ZT" ? "zh-tw" : lang.toLowerCase(),
                    }}
                >
                    <MantineProvider theme={theme}>
                        <ModalsProvider>
                            <Notifications />
                            <RouterProvider router={router} />
                        </ModalsProvider>
                    </MantineProvider>
                </DatesProvider>
            </EventBusProvider>
        </StrictMode>
    );
};

createRoot(document.getElementById("root")!).render(<App />);
