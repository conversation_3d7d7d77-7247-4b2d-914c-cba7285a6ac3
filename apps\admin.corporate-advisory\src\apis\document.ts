import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const document = {
    three: {
        list: async (params: { keyword: string; } & TPageQueryParams) => {
            const { error, result } = await cnaRequest<ThreeListData>(
                `/api/v1/admin/desk/three/list`,
                "GET",
                params
            );

            if (!error) {
                return result.data;
            } else {
                noty.error(error.message);
                return null;
            }
        },
        store: async (params: ThreeStoreParams) => {
            const formData = new FormData();
            formData.append("level", params.level);
            formData.append("file_version", params.file_version);
            if (params.file) {
                formData.append("file", params.file);
            }

            const { error } = await cnaRequest(`/api/v1/admin/desk/three/add`, "POST", formData);

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        update: async (id: number, params: ThreeStoreParams) => {
            const formData = new FormData();
            formData.append("level", params.level);
            formData.append("file_version", params.file_version);
            if (params.file) {
                formData.append("file", params.file);
            }

            const { error } = await cnaRequest(`/api/v1/admin/desk/three/update/${id}`, "POST", formData);

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
    },
    list: async (params: TDocumentSearchParams) => {
        const { error, result } = await cnaRequest<TDocumentsResponse>(
            `/api/v1/admin/documents/index`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (params: TDocumentStoreParams) => {
        const formData = new FormData();
        formData.append("documentTitle", params.documentTitle);
        formData.append("documentDescription", params.documentTitle);
        formData.append("documentLanguage", params.documentLanguage);
        formData.append("documentValidity", params.documentValidity);
        formData.append("documentFile", params.documentFile);
        formData.append("folderId", params.folderId.toString());
        formData.append("order", params.order.toString());
        formData.append("documentVersion", params.documentVersion);

        const { error } = await cnaRequest(`/api/v1/admin/documents/create`, "POST", formData);

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (id: number, params: TDocumentStoreParams) => {
        const formData = new FormData();
        formData.append("documentTitle", params.documentTitle);
        formData.append("documentDescription", params.documentTitle);
        formData.append("documentLanguage", params.documentLanguage);
        formData.append("documentValidity", params.documentValidity);
        formData.append("folderId", params.folderId.toString());
        formData.append("order", params.order.toString());
        formData.append("documentVersion", params.documentVersion);

        // 文档文件
        if (params.documentFile) {
            formData.append("documentFile", params.documentFile);
        }
        // 文档版本
        if (params.versionid) {
            formData.append("versionid", `${params.versionid}`);
        }

        const { error } = await cnaRequest(`/api/v1/admin/documents/edit/${id}`, "POST", formData);

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    delete: async (id: number) => {
        const { error } = await cnaRequest(`/api/v1/admin/documents/delete/${id}`, "DELETE");

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 文件夹部分接口
    folders: {
        list: async (params: TDocumentFolderSearchParams) => {
            const { error, result } = await cnaRequest<TDocumentFoldersResponse>(`/api/v1/admin/documents/folders/index`, "GET", params);

            if (!error) {
                return result.data;
            } else {
                noty.error(error.message);
                return null;
            }
        },
        store: async (params: TDocumentFolderStoreParams) => {
            const { error } = await cnaRequest(`/api/v1/admin/documents/folders/create`, "POST", params);

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        update: async (id: number, params: TDocumentFolderStoreParams) => {
            const { error } = await cnaRequest(`/api/v1/admin/documents/folders/edit/${id}`, "POST", params);

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        delete: async (id: number) => {
            const { error } = await cnaRequest(`/api/v1/admin/documents/folders/delete/${id}`, "DELETE");

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        }
    }
};

export default document;
