import { CnaButton } from "@code.8cent/react/components";
import { Menu, Badge } from "@mantine/core";
import { CaretDown } from "@phosphor-icons/react";
import useProfileStore from "@/store/profile";

interface ItemProps {
    key: string;
    label: string;
    badge?: {
        color?: string;
        text: string;
    };
    onClick: () => void;
    disabled?: boolean;
    needPermission?: string;
    noPermissionHidden?: boolean;
}

const TableRowDropActionMenu: React.FC<{
    items: ItemProps[];
    className?: string;
}> = ({ items = [] }) => {
    const { permissions } = useProfileStore();

    const checkPermission = (item: ItemProps) => {
        if (item.disabled) return true; // 如果本身就是disabled，直接返回true
        if (!item.needPermission) return false; // 不需要权限检查
        return !permissions.includes(item.needPermission); // 检查是否有权限
    };

    const filteredItems = items.filter(
        (item) =>
            !(item.needPermission && !permissions.includes(item.needPermission) && item.noPermissionHidden)
    );

    return (
        <Menu>
            <Menu.Target>
                <CnaButton
                    size="xs"
                    variant="outline"
                    rightSection={<CaretDown size={18} />}
                >
                    更多
                </CnaButton>
            </Menu.Target>

            <Menu.Dropdown>
                {filteredItems.map((item) => (
                    <Menu.Item
                        disabled={checkPermission(item)}
                        key={item.key}
                        onClick={item.onClick}
                        rightSection={
                            item.badge && (
                                <Badge
                                    color={item.badge.color || "dark.3"}
                                    circle
                                    size="lg"
                                >
                                    {item.badge.text}
                                </Badge>
                            )
                        }
                    >
                        {item.label}
                    </Menu.Item>
                ))}
            </Menu.Dropdown>
        </Menu>
    );
};

export default TableRowDropActionMenu;
