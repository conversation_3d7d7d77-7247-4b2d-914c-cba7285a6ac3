import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import {
    ActionIcon,
    Avatar,
    Group,
    Modal,
    SimpleGrid,
    Stack,
    Text,
    Textarea,
    TextInput,
    Tooltip,
} from "@mantine/core";
import { useState, useEffect } from "react";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Download, Eye, Trash } from "@phosphor-icons/react";
import api from "@/apis";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { downloadFile } from "@/utils/files";

const YES_OR_NO_MAP = {
    1: "否",
    2: "是",
};

const PLACE_MAP = {
    1: "官方会议",
    2: "会所参观",
    3: "商务沟通",
    4: "其他",
};

const VipInfo = () => {
    const { lang } = useSettingStore();

    const [info, setInfo] = useState<TVisitorVip>(null);
    const { openFileView } = useFileViewer();

    const visitorVipInfoParams = useModalStore((state) => state.modalParams.visitorVipInfoModal);
    const visitor = visitorVipInfoParams?.visitorInfo;

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.visitorVipInfoModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        setInfo(null);
        close("visitorVipInfoModal");
    });

    const modalFooterButtons = [
        {
            key: "close",
            label: "关闭",
            leftSection: <Trash />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    // 获取申请详情
    const getVisitorVipInfo = useMemoizedFn(async (visitorId: number) => {
        const res = await api.visitor.vipDetail(visitorId);
        if (res) {
            setInfo(res);
        }
    });

    useEffect(() => {
        if (visitor?.id) {
            getVisitorVipInfo(visitor.id);
        }
    }, [visitor?.id]);

    const previewFile = useMemoizedFn((url: string) => {
        // 把 url 中的域名给去除
        const urlWithoutDomain = url.replace(/^(https?:\/\/)?([^\/]+)\//, "");
        openFileView(
            `${window.api_base_url}/api/v1/admin/file/resource?path=${urlWithoutDomain}&type=remote`,
            {
                title: "到访附件",
            }
        );
    });

    const handleDownloadFile = useMemoizedFn((url: string) => {
        // 把 url 中的域名给去除
        const urlWithoutDomain = url.replace(/^(https?:\/\/)?([^\/]+)\//, "");
        downloadFile(urlWithoutDomain, "remote");
    });

    return (
        <Modal
            title="C&A贵宾到访信息"
            opened={show}
            onClose={closeModal}
            size="xl"
        >
            <Stack>
                <Group justify="center">
                    {info?.avatar && info?.avatar !== "" && (
                        <Avatar
                            alt="客户照片"
                            radius="md"
                            size={100}
                            className="tw-mx-auto tw-cursor-pointer tw-border"
                            src={info?.avatar}
                            onClick={() => previewFile(info?.avatar)}
                        />
                    )}
                </Group>
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                    <TextInput
                        label="申请人"
                        defaultValue={info?.name}
                        readOnly
                    />
                    <TextInput
                        label="联系电话"
                        defaultValue={info?.phone}
                        readOnly
                    />
                </SimpleGrid>
                <SimpleGrid cols={{ base: 1, md: 3 }}>
                    <TextInput
                        label="邀请人"
                        defaultValue={info?.invite_people}
                        readOnly
                    />
                    <TextInput
                        label="邀约人手机号"
                        defaultValue={info?.invite_phone}
                        readOnly
                    />
                    <TextInput
                        label="地区"
                        defaultValue={info?.location}
                        readOnly
                    />
                </SimpleGrid>
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                    <TextInput
                        label="到访日期"
                        defaultValue={info?.visitor_date}
                        readOnly
                    />
                    <TextInput
                        label="到访时间"
                        defaultValue={info?.visitor_time}
                        readOnly
                    />
                </SimpleGrid>
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                    <TextInput
                        label="离开时间"
                        defaultValue={info?.leave_time}
                        readOnly
                    />
                    <TextInput
                        label="到访人数"
                        defaultValue={info?.numbers}
                        readOnly
                    />
                </SimpleGrid>
                <SimpleGrid cols={{ base: 1, md: 3 }}>
                    <TextInput
                        label="拜访对象"
                        defaultValue={info?.visitor}
                        readOnly
                    />
                    <TextInput
                        label="是否已了解C&A"
                        defaultValue={YES_OR_NO_MAP[info?.is_read_cna]}
                        readOnly
                    />
                    <TextInput
                        label="到访目的"
                        defaultValue={PLACE_MAP[info?.place]}
                        readOnly
                    />
                </SimpleGrid>
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                    <TextInput
                        label="是否用餐"
                        defaultValue={YES_OR_NO_MAP[info?.is_food]}
                        readOnly
                    />
                    <TextInput
                        label="是否需要预订会议室/预订任何设施"
                        defaultValue={YES_OR_NO_MAP[info?.is_book_meeting]}
                        readOnly
                    />
                </SimpleGrid>
                <Textarea
                    label="饮食偏好/忌口"
                    defaultValue={info?.food_preference}
                    readOnly
                />
                <Textarea
                    label="人员名单"
                    defaultValue={info?.visitor_name_list}
                    readOnly
                />
                <Textarea
                    label="企业资料"
                    defaultValue={info?.company_information}
                    readOnly
                />
                <Textarea
                    label="洽谈事项"
                    defaultValue={info?.matters}
                    readOnly
                />
                <Textarea
                    label="您的期待是什么"
                    defaultValue={info?.expect}
                    readOnly
                />
                <Textarea
                    label="预订详细请求"
                    defaultValue={info?.book_detail}
                    readOnly
                />
                <Textarea
                    label="备注"
                    defaultValue={info?.remark}
                    readOnly
                />
                {info?.attach && info?.attach.length > 0 && (
                    <>
                        <Text className="tw-pl-2 tw-font-bold tw-bg-[#060d3d] tw-text-white">
                            附件
                        </Text>
                        {info?.attach.map((item, index) => (
                            <Group
                                justify="space-between"
                                key={index}
                            >
                                <Text>{`附件${index + 1}`}</Text>
                                <Group>
                                    <Tooltip label="查看">
                                        <ActionIcon onClick={() => previewFile(item)}>
                                            <Eye />
                                        </ActionIcon>
                                    </Tooltip>
                                    <Tooltip label="下载">
                                        <ActionIcon onClick={() => handleDownloadFile(item)}>
                                            <Download />
                                        </ActionIcon>
                                    </Tooltip>
                                </Group>
                            </Group>
                        ))}
                    </>
                )}
            </Stack>
            <div className="tw-mt-10">
                <ModalFooter buttons={modalFooterButtons} />
            </div>
        </Modal>
    );
};

export default VipInfo;
