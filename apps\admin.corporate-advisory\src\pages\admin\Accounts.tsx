import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn, useRequest } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Plus, GearSix } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import Permission from "@/components/modals/account/Permission";
import Create from "@/components/modals/account/Create";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import Remark from "@/components/modals/Remark";
import { createColumnHelper } from "@tanstack/react-table";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";

const columnHelper = createColumnHelper<UserProfileResponse & { department_name?: string }>();

const AdminAccountsPage = () => {
    const lang = useSettingStore.use.lang();

    const openModal = useModalStore.use.open();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const pageButtons = [
        {
            key: "add",
            leftSection: (
                <Plus
                    weight="bold"
                    size={14}
                />
            ),
            label: "创建",
            onClick: () => openModal("accountCreateUpdateModal"),
        },
        {
            key: "setting",
            leftSection: (
                <GearSix
                    weight="bold"
                    size={14}
                />
            ),
            label: "设置",
            onClick: () => {},
        },
    ];

    const tableColumns = [
        columnHelper.accessor("profileID", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileName", {
            header: "名字",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("department_name", {
            header: "部门",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileContact", {
            header: "手机号",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileEmail", {
            header: "邮件地址",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        }),
    ];

    const rowActions = (row) => [
        {
            key: "info",
            label: "简介信息",
            onClick: () => openModal("accountCreateUpdateModal", { account: row }),
        },
        {
            key: "remark",
            label: "备注信息",
            onClick: () => openModal("remarkModal", { objID: row.profileID, type: 2 }),
        },
        {
            key: "permission",
            label: "权限设置",
            onClick: () => openModal("accountPermissionModal", { profileID: row.profileID }),
        },
    ];

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.account.list(requestParams, "2");
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    // 获取部门列表
    const { data: departments = [] } = useRequest(api.department.all);

    // 刷新表格
    const refreshTable = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="行政"
                desc="查询行政"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
            />

            {/* 加载渲染权限弹窗 */}
            <Permission />
            {/* 渲染创建/更新弹窗 */}
            <Create
                departments={departments as TDepartment[]}
                onUpdateSuccess={refreshTable}
            />
            {/* 渲染备注信息弹窗 */}
            <Remark />
        </Stack>
    );
};

export default AdminAccountsPage;
