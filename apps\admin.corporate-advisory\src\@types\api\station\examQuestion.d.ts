declare global {
    type TQuestionOption = {
        option_name: string;
        is_answer: 0 | 1;
        is_checked?: 0 | 1;
    };

    type TQuestion = {
        id: number;
        exam_id: number;
        exam?: {
            id: number;
            name: string;
        };
        type: number;
        question: string;
        option: TQuestionOption[];
        score?: number;
        created_at: string;
        updated_at: string;
    };

    type TExamQuestionStoreOrUpdateParams = Omit<TQuestion, "id" | "created_at" | "updated_at">;

    type TExamQuestionListReponse = {
        list: {
            data: TQuestion[];
            total: number;
        };
    };

    type TExam = {
        id: number;
        project_id: number;
        name: string;
        status?: 0 | 1;
        created_at: string;
        updated_at: string;
        deleted_at?: string;
    };

    type TExamStoreParams = Pick<TExam, "name" | "project_id">;
    type TExamUpdateParams = Omit<TExam, "created_at" | "updated_at" | "deleted_at">;

    type TExamQuestionTypeNumConfig = {
        id: number;
        question_type: number;
        exam_id: number;
        num: number;
        created_at: string;
        updated_at: string;
        deleted_at?: string;
    };

    type TExamQuestionTypeNumConfigStoreOrUpdateParams = {
        exam_id: number;
        config: {
            num: number;
            question_type: 0 | 1 | 2 | 3;
        }[];
    };

    type TCourseIntro = {
        id: number;
        service_data_id: number;
        intro: string;
        outline: string;
        created_at: string;
        updated_at: string;
        deleted_at?: string;
        service_data?: {
            id: number;
            title_zh: string;
        };
    };

    type TCourseIntroStoreParams = {
        service_data_id: number;
        intro: string;
        outline: File; // 课程大纲文件
    };

    type TCourseIntroUpdateParams = TCourseIntroStoreParams & "id";

    type TCourse = {
        id: number;
        service_data_id: number;
        name: string;
        type: 1 | 2; // 课件类型 1ppt 2视频
        url: string;
        sort: number;
        total: number;
        created_at: string;
        updated_at: string;
        deleted_at?: string;
        service_data?: {
            id: number;
            title_zh: string;
        };
    };

    type TCourseStoreParams = {
        service_data_id: number;
        name: string;
        type: 1 | 2;
        file: File;
        total: number;
        sort?: number;
    };

    type TCourseUpdateParams = TCourseStoreParams & "id";
}

export {};
