import { t } from "@code.8cent/i18n";

const PROFILE_STATUS_MAP = [
    { value: "1", color: "blue", label: "待付款" },
    { value: "2", color: "yellow", label: "待审核" },
    { value: "3", color: "green", label: "活跃中" },
    { value: "4", color: "red", label: "已驳回" },
    { value: "5", color: "gray", label: "已失效" },
];

export function getProfileStatus(lang: string) {
    return PROFILE_STATUS_MAP.map((item) => {
        return {
            ...item,
            label: t(`${item.label}`, lang),
        };
    });
}

const NOTICE_STATUS_MAP = [
    { value: "0", color: "yellow", label: "等待发布" },
    { value: "1", color: "green", label: "已发布" },
    { value: "2", color: "green", label: "已失效" },
    { value: "3", color: "blue", label: "特别状态" },
];

export function getNoticeStatus(lang: string) {
    return NOTICE_STATUS_MAP.map((item) => {
        return {
            ...item,
            label: t(`${item.label}`, lang),
        };
    });
}
