import jsPDF from 'jspdf';
import dayjs from 'dayjs';

interface ExportPDFOptions {
    title: string;
    data: any[];
    fields: {
        key: string;
        label: string;
        format?: (value: any) => string;
    }[];
    fileName: string;
}

export const exportToPDF = async (options: ExportPDFOptions) => {
    const { title, data, fields, fileName } = options;

    // 创建 PDF 文档
    const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'pt',
        format: 'a4'
    });

    // 添加字体
    pdf.addFont('/fonts/NotoSansSC-Regular.ttf', 'NotoSansSC', 'normal');
    pdf.setFont('NotoSansSC');

    // 设置页面边距
    const margin = 40;
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const contentWidth = pageWidth - (margin * 2);

    // 设置标题样式
    pdf.setFontSize(20);
    const titleWidth = pdf.getTextWidth(title);
    pdf.text(title, (pageWidth - titleWidth) / 2, margin + 20);

    // 设置内容样式
    pdf.setFontSize(12);
    let y = margin + 60; // 标题后的起始位置

    // 处理每条记录
    data.forEach((row, index) => {
        // 如果不是第一条记录，添加新页面
        if (index > 0) {
            pdf.addPage();
            y = margin;
        }

        // 添加记录标题
        pdf.setFontSize(14);
        const recordTitle = "记录" + (index + 1);
        pdf.text(recordTitle, margin, y);
        y += 20;

        // 添加字段数据
        pdf.setFontSize(12);
        pdf.setFont('NotoSansSC', 'normal');
        fields.forEach(field => {
            const value = field.format ? field.format(row[field.key]) : row[field.key];
            const text = `${field.label}：${value || ''}`;

            // 检查是否需要分页
            const textLines = pdf.splitTextToSize(text, contentWidth);
            textLines.forEach(line => {
                // 检查是否需要新页面
                if (y + 20 > pageHeight - margin) {
                    pdf.addPage();
                    y = margin;
                }
                pdf.text(line, margin, y);
                y += 20;
            });
        });
    });

    // 保存 PDF
    pdf.save(`${fileName}_${dayjs().format('YYYYMMDD')}.pdf`);
};
