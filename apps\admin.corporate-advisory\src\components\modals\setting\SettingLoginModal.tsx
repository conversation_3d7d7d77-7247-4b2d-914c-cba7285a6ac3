import api from "@/apis";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import useModalStore from "@/store/modal";
import { Group, Modal, Stack, Button } from "@mantine/core";
import { useMemoizedFn, useRequest } from "ahooks";
import dayjs from "dayjs";
import { useState } from "react";
import { useShallow } from "zustand/react/shallow";
import bowser from "bowser";
import useSettingStore from "@code.8cent/store/setting";
import { SignOut, X } from "@phosphor-icons/react";

const LoginLogModal = () => {
    const lang = useSettingStore.use.lang();

    const device = bowser.parse(window.navigator.userAgent);

    const LoginLog = [
        {
            date: dayjs().format("YYYY-MM-DD HH:mm:ss"),
            device: `${device.os.name}, ${device.browser.name}`,
            ipAddress: "***************",
            isCurrent: true,
        },
    ];

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.settingLoginHistory,
            close: state.close,
        }))
    );

    const { data: myIp } = useRequest(
        async () => {
            const ip = await api.gernal.getIPAddress();
            return ip;
        },
        {
            ready: show,
        }
    );

    return (
        <Modal
            opened={show}
            onClose={() => close("settingLoginHistory")}
            title={t("setting.login_history.title", lang)}
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-6 tw-overflow-y-auto tw-max-h-[60vh] tw-px-3">
                <p className="tw-text-gray-500 tw-mb-6">
                    {t("system.log.history.details", lang)}
                </p>
                {LoginLog.map((log, index) => (
                    <Group
                        key={index}
                        className={`tw-text-sm tw-border tw-rounded-lg tw-p-3 tw-justify-between tw-items-center tw-mb-0 -tw-mt-3 ${
                            log.isCurrent ? "tw-bg-gray-100" : ""
                        }`}
                    >
                        <div>
                            <div className="tw-text-gray-700 tw-font-medium">
                                {dayjs(log.date).format("YYYY-MM-DD HH:mm:ss")}
                                {log.isCurrent && (
                                    <span className="tw-font-bold tw-ml-2">
                                        (Current)
                                    </span>
                                )}
                            </div>
                            <div className="tw-text-gray-500">
                                {log.device} | {myIp}
                            </div>
                        </div>
                        {!log.isCurrent && (
                            <Button
                                color="gray.6"
                                variant="transparent"
                                className="tw-px-2 tw-py-1"
                            >
                                <SignOut weight="bold" size={18}/>
                            </Button>
                        )}
                    </Group>
                ))}
            </Stack>
            <Group justify="end" className="tw-border-t tw-pt-4 tw-mt-4">
                <CnaButton
                    color="cna"
                    variant="outline"
                    onClick={() => console.log("Logout all devices")}
                    leftSection={<SignOut weight="bold" />}
                >
                    {t("setting.login_history.all_log_out", lang)}
                </CnaButton>
                <CnaButton
                    color="cna"
                    variant="outline"
                    onClick={() => close("settingLoginHistory")}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default LoginLogModal;
