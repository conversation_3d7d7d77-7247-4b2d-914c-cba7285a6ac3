import React, { useEffect, useRef, useState } from "react";
import { ActionIcon, Group, Image, Stack, Text } from "@mantine/core";
import {
    MagnifyingGlassPlus,
    MagnifyingGlassMinus,
    ArrowCounterClockwise,
    Arrow<PERSON>lockwise,
    ClockClockwise,
} from "@phosphor-icons/react";

type ImageViewerProps = {
    file: File;
};

const ImageViewer: React.FC<ImageViewerProps> = ({ file }) => {
    const [imageZoom, setImageZoom] = useState<number>(1);
    const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [rotation, setRotation] = useState<number>(0);
    const imageRef = useRef<HTMLDivElement>(null);

    const handleMouseDown = (e: React.MouseEvent) => {
        if (imageZoom > 1) {
            setIsDragging(true);
            e.preventDefault();
        }
    };

    const handleMouseMove = (e: React.MouseEvent) => {
        if (isDragging && imageZoom > 1) {
            setDragPosition((prev) => ({
                x: prev.x + e.movementX / imageZoom,
                y: prev.y + e.movementY / imageZoom,
            }));
        }
    };

    const handleMouseUp = () => {
        setIsDragging(false);
    };

    const resetPosition = () => {
        setDragPosition({ x: 0, y: 0 });
    };

    const rotateLeft = () => {
        setRotation((prev) => (prev - 90) % 360);
    };

    const rotateRight = () => {
        setRotation((prev) => (prev + 90) % 360);
    };

    useEffect(() => {
        resetPosition();
    }, [imageZoom]);

    return (
        <Stack
            justify="center"
            className="tw-h-full tw-w-full tw-relative tw-overflow-hidden"
        >
            <div
                ref={imageRef}
                className="tw-cursor-move tw-h-full tw-w-full tw-flex tw-items-center tw-justify-center"
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
            >
                <Image
                    fit="contain"
                    src={URL.createObjectURL(file)}
                    style={{
                        transform: `scale(${imageZoom}) translate(${dragPosition.x}px, ${dragPosition.y}px) rotate(${rotation}deg)`,
                        transition: imageZoom === 1 ? "transform 0.2s" : "none",
                    }}
                />
            </div>
            <Group
                p="center"
                gap="xs"
                className="tw-absolute tw-top-1/2 tw-right-4 tw-transform tw-translate-y-[-50%] tw-bg-gray-100 tw-bg-opacity-70 tw-p-2 tw-rounded tw-flex tw-flex-col"
            >
                <Text size="xs">{Math.round(imageZoom * 100)}%</Text>
                <ActionIcon onClick={() => setImageZoom((prev) => Math.min(5, prev + 0.2))}>
                    <MagnifyingGlassPlus />
                </ActionIcon>
                <ActionIcon onClick={() => setImageZoom((prev) => Math.max(0.2, prev - 0.2))}>
                    <MagnifyingGlassMinus />
                </ActionIcon>
                <ActionIcon onClick={rotateLeft}>
                    <ArrowCounterClockwise />
                </ActionIcon>
                <ActionIcon onClick={rotateRight}>
                    <ArrowClockwise />
                </ActionIcon>
                <ActionIcon
                    onClick={() => {
                        setImageZoom(1);
                        resetPosition();
                        setRotation(0);
                    }}
                >
                    <ClockClockwise />
                </ActionIcon>
            </Group>
        </Stack>
    );
};

export default ImageViewer;
