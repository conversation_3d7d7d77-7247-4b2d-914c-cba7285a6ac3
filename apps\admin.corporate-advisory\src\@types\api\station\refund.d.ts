declare global {
    type TRefundSearchParams = {
        profile_name?: string;
    } & TPageQueryParams;

    type TRefund = {
        id: number;
        icbc_order_id: number; // 支付单id
        payment_id: number; // 收支流水id
        profile_id: number;
        refund_price: number; // 退款金额
        remark: string; // 备注
        status: number; // 状态 0 取消 1申请中 2 审核通过 3 审核不通过
        review_comments?: string; // 审核备注
        refund_status: number; // 退款状态 0 未退款  1已退款 2退款失败
        created_at?: string; // 创建时间
        updated_at?: string; // 更新时间
        profile: {
            profileID: number;
            profileName: string; // 合伙人姓名
        };
    };

    type TRefundResponse = {
        data: TRefund[];
        last_page: number;
        total: number;
    };

    type TRefundExamineParams = {
        id: number;
        status: 2 | 3; // 2 审核通过 3 审核不通过
        review_comments: string;
    };
}

export {};
