declare global {
    type TGspApplicationSearchParams = {
        keyword: string | null;
        status: string | null;
    } & TPageQueryParams;

    type TGspApplication = {
        id: number;
        profile_id: string;
        profile: {
            profileID: number;
            profileName: string;
        };
        name: string;
        credit_code: string;
        register_capital: string;
        paid_captital: string;
        category_id: number;
        desc: string;
        main_business: string;
        related_business: string;
        customer: string;
        annual_revenue: string;
        annual_cost: string;
        total_assets: string;
        total_liability: string;
        contact_name: string;
        contact_position: string;
        phone: string;
        email: string;
        economic_behavior: string;
        industry_group: string;
        annual_trade: string;
        target_company: string;
        form: string;
        form_complete: string;
        status: number;
        form_reject_reason: string;
        created_at: string;
        updated_at: string;
        report_reject_reason: string;
        is_print_contract: number;
        contract_url: string;
        contract_reject_reason: string;
        pay_one: number;
        pay_two: number;
        pay_three: number;
        pay_one_return: number;
        form_check_date: string | null;
        contract_check_date: string | null;
        report_check_date: string | null;
    };

    type TReviewGspApplicationParams = {
        id: number;
        status?: number;
        form_reject_reason?: string;
        report_reject_reason?: string;
        contract_reject_reason?: string;
    };

    type TGspApplicationResponse = {
        items: TGspApplication[];
        paginate: BasePaginateResponse;
    };

    type TGspRefundSearchParams = {
        keyword?: string;
    } & TPageQueryParams;

    type TGspRefund = TRefund & {
        company_name: string;
    };

    type TGspRefundListResponse = {
        items: TGspRefund[];
        paginate: BasePaginateResponse;
    };

    // 线下付款
    type TGspPaymentVoucher = {
        id: number; // 主键
        gsp_id: number; // 关联的gsp_id
        step: number; // 步骤
        file_path: string; // 文件路径
        file_name: string; // 文件名
        company_name: string; // 公司名称
        status: number; // 状态
        reject_reason: string; // 拒绝原因
        created_at: string; // 创建时间
        updated_at: string; // 更新时间
    };

    type TGspPaymentVoucherListResponse = {
        items: TGspPaymentVoucher[];
        paginate: BasePaginateResponse;
    };

    // 线下付款审核参数
    type TGspVoucherCheckParams = {
        id: number; // 主键
        status: 1 | 2; // 1审核通过;2审核失败
        reject_reason?: string; // 拒绝原因
    };
}

export {};
