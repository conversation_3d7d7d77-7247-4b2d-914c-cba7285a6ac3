import api from "@/apis";
import useRegisterStore from "@/store/register";
import { t } from "@code.8cent/i18n";
import { AuthForm<PERSON>ard, CnaButton } from "@code.8cent/react/components";
import { AuthenticationLayout } from "@code.8cent/react/layouts";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import { Stack, Text, Image, Table, Title } from "@mantine/core";
import { useAsyncEffect, useRequest } from "ahooks";
import { useNavigate } from "react-router-dom";
import WechatQRPay from "./components/WechatQRPay";
import WechatMPPay from "./components/WechatMPPay";
import { sleepMs } from "@code.8cent/utils";

const AccountRegisterPayment = () => {
    const { lang, isWechat } = useSettingStore();

    const { token } = useRegisterStore();

    const { setRegisterSetting, setState } = useWizardStore();

    const navigate = useNavigate();

    const { data: payStatus = false, cancel } = useRequest(
        async () => {
            let payStatus = await api.register.checkPayStatus(token);

            if (payStatus.payment_state === true) {
                await window.localForage.setItem("cna-token", payStatus.token);

                setRegisterSetting(payStatus.register_setting ?? []);

                setState(0);
            }

            return payStatus.payment_state;
        },
        {
            pollingInterval: 2000,
            pollingWhenHidden: false,
        }
    );

    useAsyncEffect(async() => {
        if(payStatus === true){
            await sleepMs(1000);

            cancel();
        }
    }, [
        payStatus
    ])

    return (
        <AuthenticationLayout>
            <AuthFormCard>
                {payStatus === true ? (
                    <Stack
                        align="center"
                        gap={"xl"}
                        // className="tw-pb-16"
                    >
                        <Image
                            w={100}
                            h={100}
                            src={`/images/icons/icon-alert-success.svg?t=${new Date().getTime()}`}
                        />

                        <Title order={2}>{t("payment.result", lang)}</Title>

                        <Text>{t("payment.result.details", lang)}</Text>

                        <CnaButton
                            onClick={() => navigate("/account/wizard")}
                            size="lg"
                        >
                            {t("forget_password.link.to_login", lang)}
                        </CnaButton>
                    </Stack>
                ) : (
                    <Stack>
                        <Table
                            withTableBorder
                            classNames={{
                                th: "tw-bg-neutral-200 tw-text-neutral-700",
                                td: "tw-text-neutral-700",
                            }}
                            verticalSpacing={"sm"}
                        >
                            <Table.Thead>
                                <Table.Tr>
                                    <Table.Th>{t("payment.information", lang)}</Table.Th>
                                </Table.Tr>
                            </Table.Thead>
                            <Table.Tbody>
                                <Table.Tr>
                                    <Table.Td>{t("payment.status", lang)}</Table.Td>
                                </Table.Tr>
                                <Table.Tr>
                                    <Table.Td>{t("payment.details", lang)}</Table.Td>
                                </Table.Tr>
                                <Table.Tr>
                                    <Table.Td>{t("payment.amount", lang)}</Table.Td>
                                </Table.Tr>
                                <Table.Tr>
                                    <Table.Td>{t("payment.method", lang)}</Table.Td>
                                </Table.Tr>
                            </Table.Tbody>
                        </Table>

                        {isWechat === true ? (
                            <WechatMPPay token={token} />
                        ) : (
                            <WechatQRPay token={token} />
                        )}
                    </Stack>
                )}
            </AuthFormCard>
        </AuthenticationLayout>
    );
};

export default AccountRegisterPayment;
