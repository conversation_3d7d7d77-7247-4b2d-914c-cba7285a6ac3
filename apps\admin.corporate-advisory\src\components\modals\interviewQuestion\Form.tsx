import { Button, Group, Modal, Stack, Select, Textarea } from "@mantine/core";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { t } from "@code.8cent/i18n";
import { useMemoizedFn, useRequest } from "ahooks";
import useModalStore from "@/store/modal";
import interviewQuestion from "@/apis/interviewQuestion";
import { useEffect, useState } from "react";
import noty from "@code.8cent/react/noty";

// 面试问题表单验证模式
const formSchema = z.object({
    module: z.number({
        required_error: "请选择模块",
        invalid_type_error: "请选择模块",
    }),
    question: z.string().min(1, "请输入问题内容"),
});

type FormValues = z.infer<typeof formSchema>;

const InterviewQuestionForm = ({ refreshTable }: { refreshTable: () => void }) => {
    const [loading, setLoading] = useState(false);
    const [moduleOptions, setModuleOptions] = useState<{ value: string; label: string }[]>([]);

    const {
        interviewQuestionForm: opened,
        modalParams,
        close,
    } = useModalStore((state) => ({
        interviewQuestionForm: state.interviewQuestionForm,
        modalParams: state.modalParams,
        close: state.close,
    }));

    // 当前编辑的问题
    const currentQuestion = modalParams?.interviewQuestionForm as TInterviewQuestion | undefined;
    const isEdit = !!currentQuestion?.id;

    // 表单
    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        setValue,
    } = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            module: 0,
            question: "",
        },
    });

    // 获取模块选项
    const { loading: loadingModules } = useRequest(
        async () => {
            if (opened) {
                const modules = await interviewQuestion.moduleOptions();
                if (modules) {
                    const options = Object.values(modules).map((item: any) => ({
                        value: item.value.toString(),
                        label: item.label,
                    }));
                    setModuleOptions(options);
                }
            }
        },
        {
            refreshDeps: [opened],
        }
    );

    // 初始化表单数据
    useEffect(() => {
        if (opened && currentQuestion) {
            setValue("module", currentQuestion.module);
            setValue("question", currentQuestion.question);
        } else {
            reset();
        }
    }, [opened, currentQuestion, setValue, reset]);

    // 关闭弹窗
    const handleClose = useMemoizedFn(() => {
        close("interviewQuestionForm");
        reset();
    });

    // 提交表单
    const onSubmit = useMemoizedFn(async (data: FormValues) => {
        setLoading(true);
        try {
            let success;

            if (isEdit) {
                success = await interviewQuestion.update({
                    id: currentQuestion!.id,
                    module: data.module,
                    question: data.question,
                });
            } else {
                success = await interviewQuestion.store({
                    module: data.module,
                    question: data.question,
                });
            }

            if (success) {
                noty.success(isEdit ? "更新成功" : "创建成功");
                handleClose();
                refreshTable();
            }
        } finally {
            setLoading(false);
        }
    });

    return (
        <Modal
            opened={opened}
            onClose={handleClose}
            title={isEdit ? "编辑面试问题" : "添加面试问题"}
            size="lg"
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack gap="md">
                    <Select
                        label="模块"
                        placeholder="请选择模块"
                        data={moduleOptions}
                        error={errors.module?.message}
                        disabled={loadingModules}
                        required
                        value={String(
                            moduleOptions.find(
                                (opt) => opt.value === String(currentQuestion?.module)
                            )?.value || ""
                        )}
                        onChange={(value) => setValue("module", Number(value))}
                    />

                    <Textarea
                        label="问题内容"
                        placeholder="请输入问题内容"
                        error={errors.question?.message}
                        required
                        {...register("question")}
                    />

                    <Group
                        justify="flex-end"
                        mt="md"
                    >
                        <Button
                            variant="default"
                            onClick={handleClose}
                        >
                            取消
                        </Button>
                        <Button
                            type="submit"
                            loading={loading}
                        >
                            {isEdit ? "更新" : "保存"}
                        </Button>
                    </Group>
                </Stack>
            </form>
        </Modal>
    );
};

export default InterviewQuestionForm;
