import {
    <PERSON>ge,
    Button,
    FileButton,
    Group,
    ScrollArea,
    Stack,
    Text,
    Tooltip,
} from "@mantine/core";
import {
    CheckCircle,
    XCircle,
    Plus,
    DownloadSimple,
    Trash,
    DotsThreeCircle,
    Asterisk,
    Eye,
    WarningCircle,
    Question,
} from "@phosphor-icons/react";
import { useContext, useEffect, useState } from "react";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { useGetState, useMount, useRequest, useUnmount } from "ahooks";
import api from "@/apis";
import { CompanyApplicationContext } from "@/contexts/project";
import useModalStore from "@/store/modal";
import useCompanyApplicationFormEnabled from "@/hooks/project/useCompanyApplicationFormEnabled";
import { useEventBus } from "@/utils/eventBus";
import noty from "@code.8cent/react/noty";

const CompanyDocumentSubmission = () => {
    const lang = useSettingStore.use.lang();

    const { companyInfo } = useContext(CompanyApplicationContext);

    const [inited, setInited] = useState(false);

    const [canSubmit, setCanSubmit, getCanSubmit] = useGetState(false);

    const bus = useEventBus();

    const formEnabled = useCompanyApplicationFormEnabled(companyInfo, 4);

    const {
        run: get_docs,
        loading,
        data: documents = [],
    } = useRequest(
        async () => {
            let docs = await api.project.getApplicationDocumentsInfo(
                companyInfo.companyID
            );

            return docs;
        },
        {
            ready: inited,
        }
    );

    useEffect(() => {
        console.log(documents);

        if (!documents.length) {
            setCanSubmit(false);
        } else {
            let canSubmit = documents.every((doc) => {
                console.log("doc: ", doc);

                let status = false;

                if (doc.isRequired === 0) {
                    status = true;
                } else if (doc.isRequired === 1) {
                    status = doc.isUpload === 1;
                } else {
                    status === false;
                }

                console.log("status: ", status);

                return status;
            });

            setCanSubmit(canSubmit);
        }
    }, [documents]);

    useMount(() => {
        setInited(true);

        bus.on("project.company.application.submit.click", () => {
            if (getCanSubmit()) {
                console.log("should call api");
            } else {
                noty.error("请先上传所有必要文件");
            }
        });
    });

    useUnmount(() => {
        bus.off("project.company.application.submit.click");
    });

    return (
        <ScrollArea
            style={{ width: "100%", height: 320, zIndex: 301 }}
            type="always"
        >
            <Stack gap={0}>
                {documents.map((doc, idx) => {
                    const needUploadDocument =
                        !doc.isUpload ||
                        (doc.isUpload === 1 && doc.state === 3);

                    return (
                        <Group
                            justify="space-between"
                            className="tw-border first:tw-border-t tw-border-t-0 tw-p-2 tw-text-gray-500"
                            align="center"
                            key={idx}
                        >
                            <Group align="center" gap={0}>
                                {!doc.isUpload && (
                                    <WarningCircle className="tw-text-yellow-500 tw-text-2xl" />
                                )}
                                {doc.state === 3 && (
                                    <XCircle className="tw-text-red-700 tw-text-2xl" />
                                )}
                                {doc.state === 2 && (
                                    <CheckCircle className="tw-text-green-700 tw-text-2xl" />
                                )}
                                {doc.state === 1 && (
                                    <DotsThreeCircle className="tw-text-gray-400 tw-text-2xl" />
                                )}
                                <Text
                                    size="sm"
                                    className="tw-ml-4 tw-leading-none"
                                >
                                    {doc.fileName}
                                </Text>
                                {doc.isRequired === 1 &&
                                    Array.from({ length: 2 }).map((_, _i) => (
                                        <Asterisk
                                            key={_i}
                                            className={`${
                                                _i === 0 && "tw-ml-2"
                                            } tw-text-red-700 tw-text-xs`}
                                        />
                                    ))}

                                {doc.state === 3 && (
                                    <Tooltip
                                        label={doc.reason}
                                        events={{
                                            hover: true,
                                            focus: true,
                                            touch: true,
                                        }}
                                        withArrow
                                        withinPortal
                                        multiline
                                        w={220}
                                    >
                                        <Badge
                                            color="red.1"
                                            className="tw-ml-2"
                                            c="inherit"
                                        >
                                            <Group gap={4}>
                                                审核不通过{" "}
                                                <Question size={14} />
                                            </Group>
                                        </Badge>
                                    </Tooltip>
                                )}
                            </Group>
                            <Group align="center" gap={1}>
                                {!doc.isUpload && (
                                    <FileButton
                                        onChange={async (file) => {
                                            let upload_status =
                                                await api.project.uploadDocument(
                                                    {
                                                        companyID:
                                                            companyInfo.companyID,
                                                        file: file,
                                                        fileID: doc.fileID,
                                                    }
                                                );

                                            if (upload_status) {
                                                get_docs();
                                            } else {
                                                noty.error("文件上传失败");
                                            }
                                        }}
                                        accept="image/png,image/jpeg,application/pdf"
                                    >
                                        {(props) => (
                                            <Button
                                                {...props}
                                                leftSection={
                                                    <Plus
                                                        weight="light"
                                                        size={16}
                                                    />
                                                }
                                                className="!tw-bg-neutral-100 tw-font-normal !tw-text-gray-500"
                                                size="sm"
                                            >
                                                {t(
                                                    "project.edit.step4.form.btn.add_doc",
                                                    lang
                                                )}
                                            </Button>
                                        )}
                                    </FileButton>
                                )}
                                {doc.isUpload && (
                                    <>
                                        {doc.state === 3 ? (
                                            <FileButton
                                                onChange={async (file) => {
                                                    let upload_status =
                                                        await api.project.uploadDocument(
                                                            {
                                                                companyID:
                                                                    companyInfo.companyID,
                                                                file: file,
                                                                fileID: doc.fileID,
                                                            }
                                                        );

                                                    if (upload_status) {
                                                        get_docs();
                                                    } else {
                                                        noty.error(
                                                            "文件上传失败"
                                                        );
                                                    }
                                                }}
                                                accept="image/png,image/jpeg,application/pdf"
                                            >
                                                {(props) => (
                                                    <Button
                                                        {...props}
                                                        variant="subtle"
                                                        size="sm"
                                                        color="dark.3"
                                                        leftSection={
                                                            <Plus
                                                                weight="light"
                                                                size={16}
                                                            />
                                                        }
                                                        className="tw-font-normal hover:!tw-bg-neutral-100 !tw-text-gray-500"
                                                    >
                                                        重新上传
                                                    </Button>
                                                )}
                                            </FileButton>
                                        ) : (
                                            <Button
                                                variant="subtle"
                                                size="sm"
                                                color="dark.3"
                                                className="tw-font-normal hover:!tw-bg-neutral-100 !tw-text-gray-500"
                                                leftSection={
                                                    <DownloadSimple size={18} />
                                                }
                                                onClick={() => {
                                                    api.project.getUploadedDocument(
                                                        {
                                                            companyID:
                                                                companyInfo.companyID,
                                                            fileID: doc.fileID,
                                                            mode: "download",
                                                        }
                                                    );
                                                }}
                                            >
                                                {t("common.download", lang)}
                                            </Button>
                                        )}

                                        <Button
                                            variant="subtle"
                                            size="sm"
                                            color="dark.3"
                                            className="tw-font-normal hover:!tw-bg-neutral-100 !tw-text-gray-500"
                                            leftSection={<Eye size={18} />}
                                            onClick={() => {
                                                api.project.getUploadedDocument(
                                                    {
                                                        companyID:
                                                            companyInfo.companyID,
                                                        fileID: doc.fileID,
                                                        mode: "view",
                                                    }
                                                );
                                            }}
                                        >
                                            {t("common.view", lang)}
                                        </Button>
                                    </>
                                )}
                            </Group>
                        </Group>
                    );
                })}
            </Stack>
        </ScrollArea>
    );
};

export default CompanyDocumentSubmission;
