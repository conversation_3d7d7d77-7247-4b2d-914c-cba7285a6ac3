import { Box, Image, Stack, Text } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useNavigate } from "react-router-dom";

const TeamWelcomePage = () => {
    const navigate = useNavigate();

    const toDocuments = useMemoizedFn(() => {
        navigate("/account/documents");
    });

    return (
        <div className="tw-relative boot-bg">
            <Image
                className="sm:tw-absolute tw-hidden sm:tw-block tw-z-10 tw-left-10 tw-top-6 tw-w-5"
                src="/images/C&A-Logo-Icon-White.svg"
                alt=""
                w={80}
            />
            <Image
                className="sm:tw-absolute sm:tw-hidden  tw-z-10 tw-left-10 tw-top-6 tw-w-5 tw-mx-auto"
                src="/images/C&A-Logo-Full-White.svg"
                alt=""
                w={220}
            />
            <Box className="team-page-container tw-relative tw-flex tw-items-center tw-px-2 !tw-overflow-y-auto md:tw-px-0 tw-py-2 md:tw-py-0 ">
                <div className="team-page-footer"></div>
                <Image
                    src="/images/team/ai.jpg"
                    alt="Person"
                    className="tw-relative -tw-bottom-[12px] tw-self-end 
                    tw-left-0 tw-w-auto tw-h-[600px] tw-z-20 
                    -tw-ml-40 -tw-mr-10 tw-hidden custom:tw-block"
                />
                <Stack
                    className="tw-tracking-[3px] tw-text-justify tw-relative tw-z-10
                 tw-p-8 tw-bg-basic-8 tw-bg-opacity-60 tw-flex-1 
                   max-custom:tw-mx-auto custom:tw-mr-10 tw-border tw-min-h-[500px] tw-max-w-[800px] md:tw-gap-6 tw-gap-3"
                >
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        恭贺您受推荐加入C&A全球合伙人大联盟，申请成为C&A中国区域联盟合伙人。
                    </Text>
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        合伙人可以借助C&A强大的品牌IP和全球资源，扩展自身业务范围，为客户提供更全面的国际咨询和跨国商业服务，并获得全球合伙人大联盟AI系统平台推送潜在客户和对接国际业务的新机会。
                    </Text>
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        C&A为合伙人提供“AI办公室”，配置“AI小助理”协助提高工作效率，提供“AI国际咨询资源库”赋能合伙人于国际咨询和跨国商业服务的业务能力。搭建“全球资源共享AI平台”促进世界各地合伙人之间的交流和合作，相互支援落地业务，共享全球客户人脉资源，开拓海外市场新契机，撮合彼此间企业客户的生意对接。
                    </Text>
                    <Text className="tw-text-lg md:tw-text-lg tw-text-white">
                        C&A独家代理的“绿智地球”业务和独创的“战略沙盘”工具，赋能合伙人协助中国企业用最短的时间和最低的成本跳出内卷，运用AI领跑绿色智慧新赛道，抢占海外无人区国际市场，辅导他们以“知识经济”借力“资本市场”的创新模式开展全球战略布局，抓住时代风口成为“绿色智慧城市”特定行业或细分领域的霸主。
                    </Text>
                    {/* <Text className="tw-text-right tw-mt-auto tw-text-md md:tw-text-lg tw-text-white">
                        <Text
                            span
                            className="tw-cursor-pointer tw-font-bold tw-text-xl hover:tw-text-secondary-5"
                            onClick={toDocuments}
                        >
                           继续 {">>"}
                        </Text>
                    </Text> */}
                    <div className="tw-flex tw-justify-end">
                        <div
                            className="dynamicButton tw-px-8"
                            onClick={toDocuments}
                        >
                            <div>继续 {">>"}</div>
                        </div>
                    </div>
                </Stack>
            </Box>
            <div className="sm:tw-fixed tw-bottom-5 tw-w-full tw-text-center tw-text-white tw-py-2 tw-text-sm">
                陈玮伦合伙人事务所 版权所有 © 2009 - 2024
            </div>
        </div>
    );
};

export default TeamWelcomePage;
