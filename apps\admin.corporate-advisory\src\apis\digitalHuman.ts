import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const digitalHuman = {
    list: async (params: TDigitalHumanSearchParams) => {
        const { error, result } = await cnaRequest<TDigitalHumanResponse>(
            "/api/v1/admin/digital-humans",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default digitalHuman;
