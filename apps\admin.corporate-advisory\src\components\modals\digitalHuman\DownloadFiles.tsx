import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import { Group, Modal, Stack, Text } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn, useRequest } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { CnaButton } from "@code.8cent/react/components";
import { X, Download } from "@phosphor-icons/react";
import api from "@/apis";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";

const DownloadFiles = () => {
    const { lang } = useSettingStore();

    const openConfirm = useModalStore.use.openConfirm();

    const digitalHumanFilesParams = useModalStore(
        (state) => state.modalParams.digitalHumanFilesModal
    );

    const digitalHuman = digitalHumanFilesParams?.digitalHuman;
    const files = [
        {
            name: "图片",
            path: digitalHuman?.pic,
        },
        {
            name: "图片1",
            path: digitalHuman?.pic1,
        },
        {
            name: "图片2",
            path: digitalHuman?.pic2,
        },
        {
            name: "音频",
            path: digitalHuman?.audio,
        },
        {
            name: "视频",
            path: digitalHuman?.video,
        },
    ];

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.digitalHumanFilesModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        close("digitalHumanFilesModal");
    });

    const modalFooterButtons = [
        {
            key: "close",
            label: "关闭",
            leftSection: <X />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    const handleDownload = useMemoizedFn(async (path: string) => {
        try {
            const response = await cnaRequest(
                "/api/v1/admin/digital-humans/download-file",
                "POST",
                { file_path: path, type: "oss" },
                // 指定响应类型为 Blob
                { responseType: "blob" }
            );

            // 获取文件内容（Blob）
            const blob = new Blob([response.result?.data]);

            // 创建一个临时链接
            const url = window.URL.createObjectURL(blob);

            // 创建一个 <a> 标签并触发下载
            const a = document.createElement("a");
            a.href = url;
            // 使用文件路径的最后一部分作为文件名
            a.download = path.split("/").pop() || "download";
            document.body.appendChild(a);
            a.click();

            // 清理临时链接
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error("下载失败:", error);
            noty.error("下载失败，请稍后重试");
        }
    });

    const confirmDownload = useMemoizedFn((path: string) => {
        openConfirm({
            title: "确定下载此文件吗？",
            onConfirm: () => handleDownload(path),
        });
    });

    return (
        <Modal
            title="下载数字人文件"
            opened={show}
            onClose={closeModal}
        >
            <Stack gap="xs">
                {files.map((file) => (
                    <Group
                        key={file.name}
                        justify="space-between"
                        p="xs"
                        className="tw-border tw-border-solid tw-border-gray-500 tw-rounded"
                    >
                        <Text>{file.name}</Text>
                        <CnaButton
                            leftSection={<Download />}
                            onClick={() => confirmDownload(file.path)}
                        >
                            下载
                        </CnaButton>
                    </Group>
                ))}
            </Stack>

            <ModalFooter buttons={modalFooterButtons} />
        </Modal>
    );
};

export default DownloadFiles;
