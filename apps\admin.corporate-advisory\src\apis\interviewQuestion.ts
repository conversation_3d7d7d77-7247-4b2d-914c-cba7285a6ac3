import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const interviewQuestion = {
    list: async (params: TInterviewQuestionSearchParams) => {
        const { error, result } = await cnaRequest<TInterviewQuestionResponse>(
            "/api/v1/admin/desk-interview-questions",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    moduleOptions: async () => {
        const { error, result } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/desk-interview-questions/modules",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (params: TStoreInterviewQuestionParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/desk-interview-questions",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (params: TUpdateInterviewQuestionParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/desk-interview-questions`,
            "PUT",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    destroy: async (ids: string) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/desk-interview-questions`,
            "DELETE",
            { ids }
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    next: {
        list: async (params: { id: number; keyword?: string }) => {
            const { error, result } = await cnaRequest<TInterviewQuestionNext[]>(
                "/api/v1/admin/desk-interview-questions/next-questions",
                "GET",
                params
            );

            if (!error) {
                return result.data;
            } else {
                noty.error(error.message);
                return null;
            }
        },
        store: async (params: TStoreOrUpdateInterviewQuestionNextParams) => {
            const { error } = await cnaRequest<BaseApiResponse>(
                "/api/v1/admin/desk-interview-questions/next-questions",
                "POST",
                params
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        update: async (params: TStoreOrUpdateInterviewQuestionNextParams) => {
            const { error } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/desk-interview-questions/next-questions`,
                "PUT",
                params
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
        destroy: async (ids: string) => {
            const { error } = await cnaRequest<BaseApiResponse>(
                `/api/v1/admin/desk-interview-questions/next-questions`,
                "DELETE",
                { ids }
            );

            if (!error) {
                return true;
            } else {
                noty.error(error.message);
                return false;
            }
        },
    },
};

export default interviewQuestion;
