import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { zodResolver } from "@hookform/resolvers/zod";
import { TextInput } from "@mantine/core";
import { useCountDown, useMemoizedFn, useRequest } from "ahooks";
import {
    useRef,
    useImperativeHandle,
    forwardRef,
    ForwardRefExoticComponent,
    Ref,
    RefAttributes,
    useState,
    useEffect,
} from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import CnaButton from "../../components/common/CnaButton";
import { cnaRequest } from "@code.8cent/utils";
import noty from "../../library/noty";
import useDataStore from "@code.8cent/store/data";
import PhoneInput from "../../components/inputs/PhoneInput";
import useVerifyModalStore from "./store";

export type PhoneVerifyInput = {
    code: string;
    prefixID: string;
    phone: string;
};

const phoneVerifySchema = z.object({
    code: z.string().min(1, "Verify code is required"),
    prefixID: z.string().min(1, "form.country.code"),
    phone: z.string().regex(/^\d+$/g, "form.phone.number.incorrect"),
});

const VerifyPhoneForm: ForwardRefExoticComponent<
    {
        onSuccess?: (data: PhoneVerifyInput) => void;
        isUpdate?: boolean;
        getCodeUrl: string;
        verifyUrl: string;
        initialPhone?: string;
        intialPrefixID?: string;
        countdownID: string;
        startCountdown: (seconds?: number) => void;
    } & RefAttributes<SharedFormRef<PhoneVerifyInput>>
> = forwardRef(
    (
        {
            onSuccess,
            initialPhone = "",
            intialPrefixID = "",
            isUpdate = false,
            getCodeUrl,
            verifyUrl,
            countdownID,
            startCountdown,
        },
        ref: Ref<SharedFormRef<PhoneVerifyInput>>
    ) => {
        const state = useVerifyModalStore();

        const { lang, countryID } = useSettingStore();

        const { filteredCountryDatas, countryDatas } = useDataStore();

        const formRef = useRef<HTMLFormElement>();

        const {
            register,
            handleSubmit,
            formState: { errors },
            getValues,
            trigger,
            reset,
            setValue,
        } = useForm<PhoneVerifyInput>({
            resolver: zodResolver(phoneVerifySchema),
            defaultValues: {
                phone: initialPhone,
                prefixID: intialPrefixID,
                code: "",
            },
        });

        const { run: verify, loading: verifying } = useRequest(
            async (data: PhoneVerifyInput) => {
                const { result, error } = await cnaRequest(verifyUrl, "POST", {
                    prefixID: data.prefixID,
                    phone: data.phone,
                    code: data.code,
                });

                const successTitle = `${t(
                    isUpdate === true ? "update.phone.success" : "verify.phone.success",
                    lang
                )}`;

                const failTitle = `${t(
                    isUpdate === true ? "update.phone.fail" : "verify.phone.fail",
                    lang
                )}`;

                if (!error === true) {
                    noty.success(t(successTitle, lang));

                    if (typeof onSuccess === "function") {
                        onSuccess(data);
                    }
                } else {
                    noty.error(t(failTitle, lang), error.message);
                }
            },
            {
                manual: true,
            }
        );

        const submit = useMemoizedFn(() => {
            handleSubmit(verify)();
        });

        useImperativeHandle(ref, () => ({
            reset,
            submit,
        }));

        const { run: getPhoneCode, loading: requestingPhoneCode } = useRequest(
            async () => {
                let isPhoneOk = await trigger(["prefixID", "phone"]);

                if (isPhoneOk === true) {
                    let { error } = await cnaRequest(getCodeUrl, "POST", {
                        phone: getValues("phone"),
                        prefixID: getValues("prefixID"),
                    });

                    if (!error) {
                        startCountdown(60);
                    } else {
                        noty.error(error.message);
                    }
                }
            },
            {
                manual: true,
            }
        );

        const handleKeyDown = useMemoizedFn((event: React.KeyboardEvent<HTMLFormElement>) => {
            if (event.key === "Enter") {
                submit();
            }
        });

        return (
            <form
                onSubmit={submit}
                onKeyDown={handleKeyDown}
                ref={formRef}
            >
                <PhoneInput<CountryDataItem>
                    label={t("introduction.label.phone", lang)}
                    data={countryDatas}
                    prefixFlagKey="countryISOCode2"
                    prefixValueKey="countryID"
                    prefixLabelKey="countryCode"
                    wrapperProps={{
                        className: "tw-mb-3",
                        withAsterisk: true,
                    }}
                    prefixProps={{
                        readOnly: true,
                        w: 78,
                        rightSectionWidth: 0,
                        value: getValues("prefixID"),
                        error: errors.prefixID ? t(errors.prefixID.message, lang) : false,
                    }}
                    inputProps={{
                        ...register("phone"),
                        placeholder: t("login.placeholder.phone", lang),
                        rightSectionWidth: 120,
                        rightSection:
                            state[`countdown_${countdownID}`] > 0 ? (
                                <CnaButton
                                    variant="transparent"
                                    color="dark.5"
                                    className="tw-cursor-not-allowed"
                                >
                                    {`${Math.floor(state[`countdown_${countdownID}`] / 1000)} ${t(
                                        "validate.after.second",
                                        lang
                                    )}`}
                                </CnaButton>
                            ) : (
                                <CnaButton
                                    variant="transparent"
                                    color="dark.5"
                                    onClick={getPhoneCode}
                                    className="!tw-bg-transparent tw-w-full"
                                    loading={requestingPhoneCode}
                                >
                                    {t("forget_password.btn.send_validate_code", lang)}
                                </CnaButton>
                            ),
                        error: errors.phone ? t(errors.phone.message, lang) : false,
                    }}
                    readOnly={isUpdate === true ? false : true}
                />
                <TextInput
                    className="tw-mb-3"
                    error={errors.code ? true : false}
                    label={t("validation.code", lang)}
                    placeholder={t("validation.enter.code", lang)}
                    autoComplete="off"
                    {...register("code")}
                />
            </form>
        );
    }
);

export default VerifyPhoneForm;
