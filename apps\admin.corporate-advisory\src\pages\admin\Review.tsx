import api from "@/apis";
import { ActionIcon, Badge, Group, Stack, Text } from "@mantine/core";
import { useMount, useRequest, useUnmount, useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import ProfileInfoModal from "@/components/modals/associate/ProfileInfo";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import { exportToExcel } from "@/utils/xlsx";
import noty from "@code.8cent/react/noty";
import useProfileStore from "@/store/profile";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { IdentificationCard, Image, CreditCard } from "@phosphor-icons/react";

// 定义状态值常量
const STATUS_MAP = [
    { value: "1", color: "blue", text: "待付款" },
    { value: "2", color: "yellow", text: "待审核" },
    { value: "3", color: "green", text: "活跃中" },
    { value: "4", color: "red", text: "已驳回" },
    { value: "5", color: "gray", text: "已失效" },
];

const columnHelper = createColumnHelper<
    UserProfileResponse & {
        bankName: string;
        bank_account: string;
        preName: string;
        rankName: string;
        id_file?: {
            profile_id?: number;
            face_file?: string;
            back_file?: string;
        };
        bank_file?: {
            profile_id?: number;
            file?: string;
        };
    }
>();

const AdminReviewPage = () => {
    const lang = useSettingStore.use.lang();

    const [inited, setInited] = useState(false);

    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();
    const { permissions } = useProfileStore();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const { openFileView } = useFileViewer();

    const { data: { professionalsList = [], skillsList = [] } = {} } = useRequest(
        async () => {
            const [professionalsList, skillsList] = await Promise.all([
                api.profile.getInfoSelectList("professionals"),
                api.profile.getInfoSelectList("skills"),
            ]);

            return {
                professionalsList,
                skillsList,
            };
        },
        {
            ready: inited,
        }
    );

    const refreshTable = useMemoizedFn(() => {
        if (tableRef?.current) {
            tableRef.current.refresh();
        }
    });

    const handleListRefresh = useMemoizedFn(() => {
        refreshTable();
    });

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                status: "2",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.associate.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    const handleExport = async () => {
        if (!permissions.includes("partner.export")) {
            noty.error("您没有导出数据的权限！");
            return;
        }

        openConfirm({
            title: "提示",
            message: "您确定要导出数据吗？",
            onConfirm: async () => {
                // 通过接口获取导出的数据
                const { globalFilters } = tableRef.current?.getState() || {};

                const { items } = await api.associate.list({
                    keyword: globalFilters?.keyword || "",
                    status: "2",
                    page: 1,
                    page_size: totalCount, // 导出全部数据
                });

                exportToExcel(
                    items,
                    [
                        { key: "profilePartnerCode", title: "编码" },
                        {
                            key: "status",
                            title: "状态",
                            format: (value) => STATUS_MAP.find((item) => item.value == value)?.text,
                        },
                        { key: "profileName", title: "名字" },
                        { key: "profileEmail", title: "邮件地址" },
                        { key: "profileContact", title: "手机号码" },
                        { key: "profileNRIC", title: "身份证" },
                        { key: "bankName", title: "银行名称" },
                        { key: "bank_account", title: "银行账号" },
                        { key: "preName", title: "上级推荐人" },
                        { key: "rankName", title: "三三制等级" },
                        {
                            key: "created_at",
                            title: "申请日期",
                            format: (value) => value && dayjs(value).format("YYYY-MM-DD HH:mm:ss"),
                        },
                    ],
                    `合伙人审核申请表_${dayjs().format("YYYYMMDD")}.xlsx`
                );
            },
        });
    };

    const rowActions = (row) => [
        {
            key: "profile",
            label: "简介信息",
            onClick: () =>
                openModal("associateProfileModal", {
                    profile: row,
                    professionalsList,
                    skillsList,
                    readOnly: true,
                }),
        },
    ];

    useMount(() => {
        setInited(true);
    });

    useUnmount(() => {
        setInited(false);
    });

    const previewFile = useMemoizedFn((path: string, type: "local" | "oss") => {
        openFileView(`${window.api_base_url}/api/v1/admin/firm/files?path=${path}&type=${type}`, {
            title: "查看文件",
        });
    });

    const tableColumns = [
        columnHelper.accessor("profilePartnerCode", {
            header: "编码",
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("status", {
            header: "状态",
            enableSorting: false,
            cell: (info) => {
                const matchedStatus = STATUS_MAP.find((item) => item.value == info.getValue());
                return <Badge color={matchedStatus?.color}>{matchedStatus?.text}</Badge>;
            },
        }),
        columnHelper.accessor("profileName", {
            header: "名字",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileEmail", {
            header: "邮件地址",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileContact", {
            header: "手机号码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileNRIC", {
            header: "身份证",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            size: 250,
            cell: (info) => {
                const value = info.getValue();
                const idFile = info.row.original.id_file;
                return (
                    <Group wrap="nowrap">
                        <Text size="sm">{value}</Text>
                        {idFile?.face_file && (
                            <ActionIcon
                                variant="default"
                                onClick={() => previewFile(idFile.face_file, "local")}
                            >
                                <IdentificationCard />
                            </ActionIcon>
                        )}
                        {idFile?.back_file && (
                            <ActionIcon
                                variant="default"
                                onClick={() => previewFile(idFile.back_file, "local")}
                            >
                                <Image />
                            </ActionIcon>
                        )}
                    </Group>
                );
            },
        }),
        columnHelper.accessor("bankName", {
            header: "银行名称",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("bank_account", {
            header: "银行账号",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => {
                const value = info.getValue();
                const bankFile = info.row.original.bank_file;
                return (
                    <Group wrap="nowrap">
                        <Text size="sm">{value}</Text>
                        {bankFile?.file && (
                            <ActionIcon
                                variant="default"
                                onClick={() => previewFile(bankFile.file, "oss")}
                            >
                                <CreditCard />
                            </ActionIcon>
                        )}
                    </Group>
                );
            },
        }),
        columnHelper.accessor("preName", {
            header: "上级推荐人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("rankName", {
            header: "三三制等级",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("created_at", {
            header: "申请日期",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                return <TableRowDropActionMenu items={rowActions(info.row.original)} />;
            },
        }),
    ];

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="审核"
                desc="审核合伙人"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={handleExport}
                serverSideSort={false}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
            />

            {/* 加载渲染详情弹窗 */}
            <ProfileInfoModal
                onReviewSuccess={handleListRefresh}
                onRejectSuccess={handleListRefresh}
            />
        </Stack>
    );
};

export default AdminReviewPage;
