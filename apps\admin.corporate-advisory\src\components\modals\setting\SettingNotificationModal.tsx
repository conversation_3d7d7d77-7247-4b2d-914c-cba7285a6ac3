import api from "@/apis";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import useModalStore from "@/store/modal";
import useSettingStore from "@code.8cent/store/setting";
import {
    Group,
    Modal,
    Stack,
    Switch,
    Select,
    Button,
    Input,
    Text,
} from "@mantine/core";
import { Check, X } from "@phosphor-icons/react";
import { useRequest } from "ahooks";
import { useState } from "react";
import { useShallow } from "zustand/react/shallow";

const notificationsList = [
    {
        label: "setting.info_notify.switch.safe_setting",
        value: "securitySettingChange",
    },
    {
        label: "setting.info_notify.switch.emergency_notice",
        value: "emergencyNotification",
    },
    {
        label: "setting.info_notify.switch.dubious_action",
        value: "suspiciousActivity",
    },
    {
        label: "setting.info_notify.switch.aceept_letter",
        value: "receivedPrivateMessage",
    },
    {
        label: "setting.info_notify.switch.importanct_update",
        value: "importantUpdates",
    },
    { label: "setting.info_notify.switch.join", value: "participateSurvey" },
    {
        label: "setting.info_notify.switch.update",
        value: "systemFeatureUpdate",
    },
];
const SettingNotificationModal = () => {
    const lang = useSettingStore.use.lang();

    const [notificationSettings, setNotificationSettings] = useState({
        emergencyNotification: true,
        suspiciousActivity: true,
        securitySettingChange: true,
        receivedPrivateMessage: true,
        importantUpdates: true,
        systemFeatureUpdate: true,
        participateSurvey: true,
    });

    const {
        show,
        close,
        openAlert: alert,
    } = useModalStore(
        useShallow((state) => ({
            show: state.settingNotification,
            close: state.close,
            openAlert: state.openAlert,
        }))
    );
    const { run: updateUserSetting, loading: saving } = useRequest(
        async () => {
            // let update_res = await api.user.updateNotificationSetting(
            //     notificationSettings
            // );
            // return update_res;

            return true;
        },
        {
            manual: true,
            onFinally(params, updateResult) {
                if (updateResult === true) {
                    alert(
                        t("notification.user.settings", lang),
                        t("notification.settings.saved.success", lang),
                        "success"
                    );
                } else {
                    alert(
                        t("notification.user.settings", lang),
                        t("notification.settings.saved.fail", lang),
                        "danger"
                    );
                }
            },
        }
    );
    const handleSwitchChange = (key) => {
        setNotificationSettings((prev) => ({
            ...prev,
            [key]: !prev[key],
        }));
    };

    return (
        <Modal
            opened={show}
            onClose={() => close("settingNotification")}
            title={t("setting.info_notification", lang)}
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-6">
                <Select
                    label={t("setting.info_notify.label.notify_type", lang)}
                    data={[
                        {
                            value: "email",
                            label: t("project.company_form.label.mail", lang),
                        },
                        {
                            value: "sms",
                            label: t("notification.message", lang),
                        },
                    ]}
                    value="email"
                />
                <Input.Wrapper label={t("system.notification", lang)}>
                    <Stack>
                        {notificationsList.map((item) => (
                            <Group
                                className="tw-border tw-border-gray-300 tw-p-2 tw-py-3 tw-rounded tw-justify-between"
                                key={item.value}
                            >
                                <Text size="sm">{t(item.label, lang)}</Text>
                                <Switch
                                    checked={notificationSettings[item.value]}
                                    onChange={() =>
                                        handleSwitchChange(item.value)
                                    }
                                    size="sm"
                                    color="basic.4"
                                />
                            </Group>
                        ))}
                    </Stack>
                </Input.Wrapper>
            </Stack>
            <Group justify="end" className="tw-border-t tw-pt-4 tw-mt-4">
                <CnaButton
                    onClick={updateUserSetting}
                    loading={saving}
                    color="cna"
                    leftSection={<Check weight="bold" />}
                >
                    {t("common.save", lang)}
                </CnaButton>
                <Button
                    color="cna"
                    variant="outline"
                    onClick={() => close("settingNotification")}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </Button>
            </Group>
        </Modal>
    );
};

export default SettingNotificationModal;
