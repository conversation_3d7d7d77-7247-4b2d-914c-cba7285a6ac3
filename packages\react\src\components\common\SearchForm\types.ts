export type FieldType = "text" | "select" | "number" | "numberRange" | "date" | "dateRange";

export interface SelectOption {
    value: string;
    label: string;
    children?: SelectOption[];
}

export interface SearchColumn {
    label: string;
    field: string;
    type: FieldType;
    tooltip?: string;
    placeholder?: string;
    options?: SelectOption[];
    precision?: number;
    step?: number;
    min?: number;
    max?: number;
    defaultValue?: string | Date | [Date, Date];
}

export interface SearchFormProps {
    columns: SearchColumn[];
    showNumber?: number;
    labelWidth?: number | string;
    onChange?: (values: Record<string, any>) => void;
    onSearch?: (values: Record<string, any>) => void;
    onReset?: () => void;
}

export interface SearchFormRef {
    getValues: () => Record<string, any>;
}
