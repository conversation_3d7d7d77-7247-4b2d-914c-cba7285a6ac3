import { modals } from "@mantine/modals";
import { useMemoizedFn } from "ahooks";
import FileViewer from "../components";
import { useMediaQuery } from "@mantine/hooks";

type FileViewerModalOptions = {
    title?: string;
};

const useFileViewer = () => {
    const isDesktop = useMediaQuery(`(min-width: 768px)`);

    const openFileView = useMemoizedFn(
        (file: File | string, options?: FileViewerModalOptions) => {
            modals.open({
                id: "fileViewer",
                title: options?.title ?? "文件查看",
                fullScreen: !isDesktop,
                size: "100%",
                closeOnClickOutside: false,
                children: <FileViewer file={file} />,
                classNames: {
                    content: "tw-h-[100vh] tw-flex tw-flex-col",
                    body: "tw-h-0 tw-flex-1 tw-px-2 tw-pb-2 md:tw-pb-8 md:tw-px-8",
                },
            });
        }
    );

    return {
        openFileView,
    };
};

export default useFileViewer;
