import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Eye } from "@phosphor-icons/react";
import { PageHeader } from "@code.8cent/react/components";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import { DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import { exportToExcel } from "@/utils/xlsx";

const columnHelper = createColumnHelper<TAssociatePaidDetail>();

const TYPE_MAP = {
    1: "收入项目",
    2: "支出项目",
} as const;

const MAIN_MAP = {
    1: "陈玮伦",
    2: "绿智管理服务",
    3: "绿智供应链",
    4: "绿智家居",
} as const;

const DIVIDE_TYPE_MAP = {
    1: "收益分成",
    2: "管理津贴",
    3: "运营津贴",
} as const;

const CHECK_STATUS_MAP = {
    0: "暂存",
    1: "审核中",
    2: "已审核",
} as const;

const GROUP_ID_MAP = {
    1: "联盟合伙人",
    2: "管理合伙人",
    3: "三三制",
} as const;

const AssociatePaidDetail = () => {
    const { lang } = useSettingStore();

    const tableRef = useRef<DataTableRef | null>(null);
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();

    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [apiTotal, setApiTotal] = useState({
        totalFee: "0",
        totalDividePercent: "0",
        totalDivideAmount: "0",
        totalProxyFax: "0",
        totalDivideProfit: "0",
    });

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { columnFilters, page, pageSize } = params;
            const getFilterValue = (id: string) =>
                columnFilters.find((item) => item.id === id)?.value || "";

            const [startDate, endDate] = getFilterValue("created_at");

            const requestParams = {
                profileID: "",
                profilePartnerCode: getFilterValue("profilePartnerCode"),
                groupId: getFilterValue("groupId"),
                projectId: getFilterValue("projectId"),
                income_type: getFilterValue("income_type"),
                divide_type: getFilterValue("divide_type"),
                check: getFilterValue("check"),
                start_date: startDate ? dayjs(startDate).format("YYYY-MM-DD HH:mm:ss") : "",
                end_date: endDate ? dayjs(endDate).format("YYYY-MM-DD HH:mm:ss") : "",
                page,
                pageSize,
            };

            const { items, paginate, total } = await api.finance.associatePaidDetail(requestParams);
            setData(items);
            setTotalCount(paginate?.total);
            setApiTotal(total);
        } finally {
            setLoading(false);
        }
    };

    const refreshData = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    const rowActions = (row) => [
        {
            key: "view",
            label: "查看",
            icon: <Eye size={16} />,
            onClick: () => {
                console.log(row);
            },
        },
    ];

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => "合计",
        }),
        columnHelper.accessor("type", {
            header: "类型",
            enableSorting: false,
            cell: (info) => TYPE_MAP[info.getValue()],
        }),
        columnHelper.accessor("fee", {
            header: "费用金额",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.totalFee,
        }),
        columnHelper.accessor("created_at", {
            header: "收款时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("remark", {
            header: "备注",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("project_name", {
            header: "项目名称",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("detail_name", {
            header: "费用项目",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("main", {
            header: "公司主体",
            enableSorting: false,
            cell: (info) => MAIN_MAP[info.getValue()],
        }),
        columnHelper.accessor("commission", {
            header: "银行手续费",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("divide_type", {
            header: "分成类型",
            enableSorting: false,
            cell: (info) => DIVIDE_TYPE_MAP[info.getValue()],
        }),
        columnHelper.accessor("divide_percent", {
            header: "分成比例",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.totalDividePercent,
        }),
        columnHelper.accessor("divide_amount", {
            header: "分成金额",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.totalDivideAmount,
        }),
        columnHelper.accessor("income_type", {
            header: "收益类型",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("proxy_fax", {
            header: "代扣税额",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.totalProxyFax,
        }),
        columnHelper.accessor("divide_profit", {
            header: "分成净额",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.totalDivideProfit,
        }),
        columnHelper.accessor("check", {
            header: "审核状态",
            enableSorting: false,
            cell: (info) => CHECK_STATUS_MAP[info.getValue()],
        }),
        columnHelper.accessor("profileName", {
            header: "合伙人姓名",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profilePartnerCode", {
            header: "合伙人编码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileNRIC", {
            header: "身份证",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("groupId", {
            header: "合伙人角色",
            enableSorting: false,
            cell: (info) => GROUP_ID_MAP[info.getValue()],
        }),
        columnHelper.accessor("teamRankName", {
            header: "三三制等级",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("sonName", {
            header: "旗下合伙人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("divideTypeName", {
            header: "分成类型",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("paymentNumber", {
            header: "单号",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("put_amount", {
            header: "付款金额",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("put_time", {
            header: "付款时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("nopay", {
            header: "未付款金额",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("put_total", {
            header: "累计已付款金额",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        // columnHelper.accessor("created_at", {
        //     header: "创建时间",
        //     enableSorting: false,
        //     cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        // }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                return <TableRowDropActionMenu items={rowActions(info.row.original)} />;
            },
        }),
    ];

    // 导出数据
    const handleExport = useMemoizedFn(async () => {
        const { columnFilters } = tableRef.current?.getState();
        const getFilterValue = (id: string) =>
            columnFilters.find((item) => item.id === id)?.value || "";

        const [startDate, endDate] = getFilterValue("created_at");

        const requestParams = {
            profileID: "",
            profilePartnerCode: getFilterValue("profilePartnerCode"),
            groupId: getFilterValue("groupId"),
            projectId: getFilterValue("projectId"),
            income_type: getFilterValue("income_type"),
            divide_type: getFilterValue("divide_type"),
            check: getFilterValue("check"),
            start_date: startDate ? dayjs(startDate).format("YYYY-MM-DD HH:mm:ss") : "",
            end_date: endDate ? dayjs(endDate).format("YYYY-MM-DD HH:mm:ss") : "",
            page: 1,
            pageSize: totalCount,
        };

        const { items, total } = await api.finance.associatePaidDetail(requestParams);

        exportToExcel(
            items,
            [
                { key: "id", title: "ID" },
                { key: "type", title: "类型", format: (value) => TYPE_MAP[value] },
                { key: "fee", title: "费用金额" },
                {
                    key: "created_at",
                    title: "收款时间",
                    format: (value) => value && dayjs(value).format("YYYY-MM-DD HH:mm:ss"),
                },
                { key: "remark", title: "备注" },
                { key: "project_name", title: "项目名称" },
                { key: "detail_name", title: "费用项目" },
                { key: "main", title: "公司主体", format: (value) => MAIN_MAP[value] },
                { key: "commission", title: "银行手续费" },
                {
                    key: "divide_type",
                    title: "分成类型",
                    format: (value) => DIVIDE_TYPE_MAP[value],
                },
                { key: "divide_percent", title: "分成比例" },
                { key: "divide_amount", title: "分成金额" },
                { key: "income_type", title: "收益类型" },
                { key: "proxy_fax", title: "代扣税额" },
                { key: "divide_profit", title: "分成净额" },
                { key: "check", title: "审核状态", format: (value) => CHECK_STATUS_MAP[value] },
                { key: "profileName", title: "合伙人姓名" },
                { key: "profilePartnerCode", title: "合伙人编码" },
                { key: "profileNRIC", title: "身份证" },
                { key: "groupId", title: "合伙人角色", format: (value) => GROUP_ID_MAP[value] },
                { key: "teamRankName", title: "三三制等级" },
                { key: "sonName", title: "旗下合伙人" },
                { key: "divideTypeName", title: "分成类型" },
                { key: "paymentNumber", title: "单号" },
                { key: "put_amount", title: "付款金额" },
                {
                    key: "put_time",
                    title: "付款时间",
                    format: (value) => value && dayjs(value).format("YYYY-MM-DD HH:mm:ss"),
                },
                { key: "nopay", title: "未付款金额" },
                { key: "put_total", title: "累计已付款金额" },
                // {
                //     key: "created_at",
                //     title: "创建时间",
                //     format: (value) => value && dayjs(value).format("YYYY-MM-DD HH:mm:ss"),
                // },
            ],
            `合伙人结算应付明细表_${dayjs().format("YYYYMMDD")}.xlsx`,
            {
                id: "合计",
                fee: total.totalFee.toString(),
                divide_percent: total.totalDividePercent.toString(),
                divide_amount: total.totalDivideAmount.toString(),
                proxy_fax: total.totalProxyFax.toString(),
                divide_profit: total.totalDivideProfit.toString(),
            }
        );
    });

    const confirmExport = useMemoizedFn(async () => {
        openConfirm({
            title: "提示",
            message: "确定要导出数据吗？",
            onConfirm: handleExport,
        });
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="合伙人结算应付明细表"
                desc="查询合伙人结算应付明细"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                filterTypes={{
                    profilePartnerCode: "text",
                    groupId: "select",
                    income_type: "select",
                    divide_type: "select",
                    check: "select",
                    created_at: "datetimeRange",
                }}
                filterOptions={{
                    groupId: [
                        { value: "1", label: "联盟合伙人" },
                        { value: "2", label: "管理合伙人" },
                        { value: "3", label: "三三制" },
                    ],
                    income_type: [
                        { value: "A", label: "A" },
                        { value: "B", label: "B" },
                        { value: "C", label: "C" },
                        { value: "D", label: "D" },
                        { value: "E", label: "E" },
                    ],
                    divide_type: [
                        { value: "1", label: "收益分成" },
                        { value: "2", label: "管理津贴" },
                        { value: "3", label: "运营津贴" },
                    ],
                    check: [
                        { value: "0", label: "暂存" },
                        { value: "1", label: "审核中" },
                        { value: "2", label: "已审核" },
                    ],
                }}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={confirmExport}
            />
        </Stack>
    );
};

export default AssociatePaidDetail;
