import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const community = {
    search: async (params: TCommunitySearchParams) => {
        const { error, result } = await cnaRequest<MemberListResponse>(
            "/api/v1/communities",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default community;
