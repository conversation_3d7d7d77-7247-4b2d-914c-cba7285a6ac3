import { TextInput, PasswordInput, Button, Text } from "@mantine/core";
import { ArrowClockwise } from "@phosphor-icons/react";
import { Link, useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, AuthFormCard } from "@code.8cent/react/components";
import AuthenticationLayout from "@code.8cent/react/layouts/AuthenticationLayout";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import cnaRequest from "@code.8cent/utils/cnaRequest";
import { useRequest, useTitle } from "ahooks";
import { SHA256 } from "crypto-js";
import z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import noty from "@code.8cent/react/noty";

type LoginFormInput = {
    email: string;
    captcha: string;
    password: string;
};

const loginSchema = z.object({
    email: z.string().email("form.email.incorrect"),
    captcha: z.string().min(1),
    password: z.string().min(1),
});

const initialLoginFormValues = {
    email: "",
    captcha: "",
    password: "",
};

const Login = () => {
    const navigate = useNavigate();

    const { lang } = useSettingStore();

    const { setState: setWizardState } = useWizardStore();

    useTitle(`${t("login.title", lang)} | ${window.app_title}`, {
        restoreOnUnmount: true,
    });

    const {
        register,
        getValues,
        handleSubmit,
        formState: { errors },
        setError,
        reset,
        clearErrors,
        setValue,
    } = useForm<LoginFormInput>({
        defaultValues: initialLoginFormValues,
        resolver: zodResolver(loginSchema),
    });

    const {
        run: getCaptchaCode,
        data: captchaRes,
        loading: gettingCode,
    } = useRequest(async () => {
        let { result, error } = await cnaRequest<{ key: string; img: string }>(
            "/api/v1/admin/captcha",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    });

    const { run: login, loading: loggingIn } = useRequest(
        async (data) => {
            const { result, error } = await cnaRequest<{
                token: string;
                state?: number;
            }>("/api/v1/admin/login", "POST", {
                email: data.email,
                password: SHA256(data.password).toString(),
                captcha_code: data.captcha,
                captcha_key: captchaRes?.key,
            });

            if (error) {
                getCaptchaCode();
                noty.error(t("login.fail", lang), error.message);
            }

            if (result) {
                let { data } = result;
                await window.localForage.setItem("cna-token", data.token);

                if (typeof data.state === "number") {
                    setWizardState(data.state);
                    navigate("/account/wizard", { replace: true });
                } else {
                    setWizardState(4);
                    navigate("/admin/desk-associates", { replace: true });
                    // navigate("/admin/review", { replace: true });
                }
            }
        },
        {
            manual: true,
        }
    );

    return (
        <AuthenticationLayout>
            <AuthFormCard
                className="tw-w-[600px]"
                title="行政帐户 - 用户登录"
            >
                <form onSubmit={handleSubmit(login)}>
                    <TextInput
                        autoComplete="username"
                        label={t("login.label.email", lang)}
                        placeholder={t("login.placeholder.email", lang)}
                        className="tw-mb-4"
                        required
                        {...register("email")}
                        error={errors.email && t(errors.email.message, lang)}
                        onChange={() => clearErrors("email")}
                    />
                    <PasswordInput
                        autoComplete="current-password"
                        label={t("login.label.password", lang)}
                        placeholder={t("login.placeholder.password", lang)}
                        className="tw-mb-4"
                        required
                        {...register("password")}
                        error={errors.password ? true : false}
                        onChange={() => clearErrors("password")}
                    />
                    <TextInput
                        classNames={{
                            input: "tw-pl-[120px]",
                        }}
                        label={t("login.label.validate_code", lang)}
                        placeholder={t("login.placeholder.validate_code", lang)}
                        className="tw-mb-5"
                        leftSectionWidth={110}
                        leftSectionProps={{
                            className: "tw-justify-start",
                        }}
                        leftSection={
                            captchaRes && (
                                <img
                                    src={captchaRes.img}
                                    className="tw-block"
                                    alt="reCaptcha"
                                />
                            )
                        }
                        rightSection={
                            <CnaButton
                                variant="transparent"
                                color={"dark.3"}
                                onClick={getCaptchaCode}
                                className="tw-font-normal tw-px-2"
                                disabled={gettingCode}
                            >
                                <ArrowClockwise
                                    className={
                                        gettingCode ? "tw-animate-spin" : ""
                                    }
                                    size={24}
                                />
                            </CnaButton>
                        }
                        required
                        {...register("captcha")}
                        error={errors.captcha ? true : false}
                        value={getValues("captcha")}
                        onChange={(e) => {
                            setValue("captcha", e.target.value.toUpperCase(), {
                                shouldValidate: true,
                            });
                            // clearErrors("captcha")
                        }}
                    />

                    <div className="tw-flex tw-my-3 tw-space-x-3">
                        {/* <div className="tw-w-1/2">
                            <CnaButton
                                size="md"
                                fullWidth
                                variant="filled"
                                color="basic"
                                component={Link}
                                to="/account/register"
                            >
                                {t("form.sign.up", lang)}
                            </CnaButton>
                        </div> */}
                        <div className="tw-w-full">
                            <CnaButton
                                size="md"
                                fullWidth
                                variant="filled"
                                color="basic"
                                loading={loggingIn}
                                type="submit"
                            >
                                {t("login.btn.login", lang)}
                            </CnaButton>
                        </div>
                    </div>
                    {/* <div className="tw-flex tw-justify-center !tw-text-basic-6">
                        <Text
                            component={Link}
                            to={{ pathname: "/account/forget-password" }}
                            size="sm"
                            className="tw-text-neutral-600 hover:tw-text-neutral-950"
                        >
                            {t("login.forget_password", lang)}
                        </Text>
                    </div> */}
                </form>
            </AuthFormCard>
        </AuthenticationLayout>
    );
};

export default Login;
