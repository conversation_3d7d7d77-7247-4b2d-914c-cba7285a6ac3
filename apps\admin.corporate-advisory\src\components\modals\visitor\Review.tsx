import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import { Modal, Select, Stack, Textarea } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Checks, X } from "@phosphor-icons/react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { t } from "@code.8cent/i18n";
import api from "@/apis";

const schema = z
    .object({
        status: z.string().min(1, "请选择审核结果"),
        reject_reason: z.string().optional(),
    })
    .partial()
    .refine(
        (data) => data.status !== "2" || (!!data.reject_reason && data.reject_reason.trim() !== ""),
        {
            message: "驳回时请输入审核意见",
            path: ["reject_reason"],
        }
    );

type FormValues = z.infer<typeof schema>;

const Review = ({ onSubmitSuccess }: { onSubmitSuccess: () => void }) => {
    const { lang } = useSettingStore();

    const visitorReviewParams = useModalStore((state) => state.modalParams.visitorAppReviewModal);
    const visitor = visitorReviewParams?.visitorInfo;

    const statusOptions = [
        { label: "通过", value: "1" },
        { label: "拒绝", value: "2" },
    ];

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.visitorAppReviewModal,
            close: state.close,
        }))
    );

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
    } = useForm<FormValues>({
        defaultValues: {
            status: "",
            reject_reason: "",
        },
        resolver: zodResolver(schema),
    });

    const closeModal = useMemoizedFn(() => {
        reset();
        close("visitorAppReviewModal");
    });

    const onSubmit: SubmitHandler<FormValues> = async (data) => {
        try {
            const reviewParams = {
                id: visitor?.id,
                status: Number(data.status),
                reject_reason: data.reject_reason,
            };

            const res = await api.visitor.reviewApply(reviewParams);

            if (res) {
                closeModal();
                onSubmitSuccess();
            }
        } catch (error) {
            console.error(error);
        }
    };

    const modalFooterButtons = [
        {
            key: "submit",
            label: "提交",
            leftSection: <Checks />,
            onClick: handleSubmit(onSubmit),
        },
        {
            key: "close",
            label: "关闭",
            leftSection: <X />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    return (
        <Modal
            title="到访申请审核"
            opened={show}
            onClose={closeModal}
            size="xl"
        >
            <Stack>
                <Select
                    label="审核状态"
                    data={statusOptions}
                    error={errors.status?.message}
                    {...register("status")}
                    onChange={(value) => setValue("status", value || "")}
                />
                <Textarea
                    label="审核意见"
                    placeholder="请输入审核意见"
                    autosize
                    minRows={4}
                    error={errors.reject_reason?.message}
                    {...register("reject_reason")}
                />
            </Stack>

            <div className="tw-mt-10">
                <ModalFooter buttons={modalFooterButtons} />
            </div>
        </Modal>
    );
};

export default Review;
