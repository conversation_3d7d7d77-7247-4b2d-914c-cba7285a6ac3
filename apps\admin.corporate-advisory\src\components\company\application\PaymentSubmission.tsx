import api from "@/apis";
import { CompanyApplicationContext } from "@/contexts/project";
import useCompanyApplicationFormEnabled from "@/hooks/project/useCompanyApplicationFormEnabled";
import useModalStore from "@/store/modal";
import { useEventBus, useListener } from "@/utils/eventBus";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    Group,
    Input,
    Select,
    Stack,
    Table,
    Text,
    TextInput,
} from "@mantine/core";
import { Dropzone, MIME_TYPES } from "@mantine/dropzone";
import { useMemoizedFn, useMount, useRequest, useUnmount } from "ahooks";
import { useContext, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Image, Spinner } from "@phosphor-icons/react";

type PaymentSubmissionFormInput = {
    bankID: string;
    paymentCategoryID: string;
    paymentCode: string;
    file: File;
};

const paymentSubmissionSchema = z.object({
    bankID: z.string().min(1, "payment.select.bank"),
    paymentCategoryID: z.string().min(1, "payment.select.method"),
    paymentCode: z.string().min(1, "payment.enter.code"),
    file: z.custom<File>((v) => v instanceof File, "payment.upload.proof"),
});

const initalPaymentSubmissionFormValues = {
    bankID: "",
    paymentCategoryID: "",
    paymentCode: "",
    file: null,
};

const CompanyPaymentSubmission: React.FC = () => {
    const [receiptFile, setReceiptFile] = useState<File | null>(null);

    const lang = useSettingStore.use.lang();

    const { companyInfo } = useContext(CompanyApplicationContext);

    const {
        register,
        handleSubmit,
        formState: { errors },
        setError,
        clearErrors,
        reset,
        setValue,
        getValues,
    } = useForm<PaymentSubmissionFormInput>({
        defaultValues: initalPaymentSubmissionFormValues,
        resolver: zodResolver(paymentSubmissionSchema),
        mode: "all",
    });

    const [paymentSubmissionData, setPaymentSubmissionData] = useState<
        Partial<PaymentSubmissionData>
    >({});

    const [inited, setInited] = useState(false);

    const alert = useModalStore.use.openAlert();

    const formEnabled = useCompanyApplicationFormEnabled(companyInfo, 2);

    const bus = useEventBus();

    const { data: { bankList = [], paymentMethodList = [] } = {} } = useRequest(
        async () => {
            const [bankList, paymentMethodList] = await Promise.all([
                api.gernal.getBankList(),
                api.gernal.getPaymentMethodList(),
            ]);

            return {
                bankList,
                paymentMethodList,
            };
        },
        {
            ready: inited,
        }
    );

    useRequest(
        async () => {
            let info = await api.project.getPaymentSubmissionData(
                companyInfo.companyID
            );

            setPaymentSubmissionData(info);

            setValue("bankID", String(info.bankID));
            setValue("paymentCategoryID", String(info.paymentCategoryID));
            setValue("paymentCode", info.paymentCode);
        },
        {
            ready: inited,
        }
    );

    useRequest(
        async () => {
            let receiptFile =
                await api.project.getUploadedPaymentSubmissionFile(
                    companyInfo.companyID
                );

            setValue("file", receiptFile);
        },
        {
            ready: inited && formEnabled === false,
        }
    );

    useMount(() => {
        setInited(true);

        bus.on(
            "project.company.application.submit.click",
            updatePaymentSubmissionData
        );
    });

    useUnmount(() => {
        bus.off("project.company.application.submit.click");
    });

    const updatePaymentSubmissionData = useMemoizedFn(
        handleSubmit(async (data) => {
            bus.emit("project.company.application.submitting", true);

            let submit_res = await api.project.updatePaymentSubmissionData({
                companyID: companyInfo.companyID,
                bankID: data.bankID,
                paymentCategoryID: data.paymentCategoryID,
                paymentCode: data.paymentCode,
                file: data.file,
            });

            if (submit_res === true) {
                alert(
                    t("file.submit.title", lang),
                    t("file.submit.title.success", lang),
                    "success"
                );
                bus.emit("project.company.application.close");
                bus.emit("project.list.refresh");
            } else {
                alert(
                    t("file.submit.title", lang),
                    t("file.submit.title.fail", lang),
                    "danger"
                );
            }

            bus.emit("project.company.application.submitting", false);
        })
    );

    return (
        <Stack className="tw-mt-3">
            <Table
                withTableBorder
                classNames={{
                    th: "tw-text-xs tw-bg-neutral-200 tw-text-neutral-700",
                    td: "tw-text-xs tw-text-neutral-700",
                }}
            >
                <Table.Thead>
                    <Table.Tr>
                        <Table.Th>
                            {t(
                                "project.edit.step2.form.title.payee_info",
                                lang
                            )}
                        </Table.Th>
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                    <Table.Tr>
                        <Table.Td>
                            {t("payment.bank.information", lang)}: 工商银行
                        </Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>
                            {t(
                                "project.edit.step2.form.label.bank_holder",
                                lang
                            )}
                            : 陈玮伦国际咨询（海南）有限公司
                        </Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>
                            {t(
                                "project.edit.step2.form.label.bank_account",
                                lang
                            )}
                            : **********
                        </Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>
                            {t(
                                "project.edit.step2.form.label.payment_amount",
                                lang
                            )}
                            : {paymentSubmissionData?.currency}{" "}
                            {paymentSubmissionData?.totalPrice}
                        </Table.Td>
                    </Table.Tr>
                </Table.Tbody>
            </Table>

            <Table
                withTableBorder
                classNames={{
                    th: "tw-text-xs tw-bg-neutral-200 tw-text-neutral-700",
                    td: "tw-text-neutral-700",
                }}
            >
                <Table.Thead>
                    <Table.Tr>
                        <Table.Th>
                            {t(
                                "project.edit.step2.form.title.sender_info",
                                lang
                            )}
                        </Table.Th>
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                    <Table.Tr>
                        <Table.Td>
                            <Group>
                                <Text className="tw-text-xs">
                                    {t("payment.bank", lang)}:{" "}
                                </Text>
                                <Select
                                    className="tw-w-[320px] tw-w-max-[100%]"
                                    size="xs"
                                    data={bankList.map((bank) => ({
                                        label: bank.bankName,
                                        value: String(bank.ID),
                                    }))}
                                    searchable
                                    allowDeselect={false}
                                    {...register("bankID")}
                                    value={getValues("bankID")}
                                    onChange={(e) => {
                                        setValue("bankID", e, {
                                            shouldValidate: true,
                                        });
                                    }}
                                    error={errors.bankID?.message}
                                    disabled={!formEnabled}
                                />
                            </Group>
                        </Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>
                            <Group>
                                <Text className="tw-text-xs">
                                    {t("payment.method", lang)}:{" "}
                                </Text>
                                <Select
                                    className="tw-w-[320px] tw-w-max-[100%]"
                                    size="xs"
                                    data={paymentMethodList.map((method) => ({
                                        label: method.paymentName,
                                        value: String(method.ID),
                                    }))}
                                    allowDeselect={false}
                                    {...register("paymentCategoryID")}
                                    value={getValues("paymentCategoryID")}
                                    onChange={(e) => {
                                        setValue("paymentCategoryID", e, {
                                            shouldValidate: true,
                                        });
                                    }}
                                    error={errors.paymentCategoryID?.message}
                                    disabled={!formEnabled}
                                />
                            </Group>
                        </Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>
                            <Group>
                                <Text className="tw-text-xs">
                                    {t("payment.account.number", lang)}:{" "}
                                </Text>
                                <TextInput
                                    className="tw-w-[320px] tw-w-max-[100%]"
                                    size="xs"
                                    {...register("paymentCode")}
                                    error={errors.paymentCode?.message}
                                    disabled={!formEnabled}
                                />
                            </Group>
                        </Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>
                            <Input.Wrapper size="xs" {...register("file")}>
                                <Input.Label className="tw-text-xs tw-mb-2 tw-text-neutral-700 tw-font-normal">
                                    {t(
                                        "project.edit.step2.form.label.upload_payment_file",
                                        lang
                                    )}
                                    :
                                </Input.Label>
                                <Dropzone
                                    onDrop={(files) => {
                                        setValue("file", files[0], {
                                            shouldValidate: true,
                                        });
                                    }}
                                    multiple={false}
                                    accept={[MIME_TYPES.png, MIME_TYPES.jpeg]}
                                    className={`${
                                        errors?.file &&
                                        "!tw-border-[var(--mantine-color-red-6)]"
                                    }`}
                                    style={{
                                        marginBottom:
                                            "calc(var(--mantine-spacing-xs) / 2)",
                                    }}
                                    disabled={!formEnabled}
                                >
                                    <Stack align="center" gap={3}>
                                        <Image size={32} className="tw-text-neutral-400"/>
                                        <Text c="dimmed" size="sm">
                                            {getValues("file") instanceof
                                                File ===
                                            true
                                                ? getValues("file").name
                                                : t("upload.files", lang)}
                                        </Text>
                                    </Stack>
                                </Dropzone>
                                {errors?.file && (
                                    <Input.Error>
                                        {errors.file.message}
                                    </Input.Error>
                                )}
                            </Input.Wrapper>
                        </Table.Td>
                    </Table.Tr>
                </Table.Tbody>
            </Table>
        </Stack>
    );
};

export default CompanyPaymentSubmission;
