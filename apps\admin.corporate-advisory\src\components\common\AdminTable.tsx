import React from 'react'
import {
  Table,
  Group,
  Text,
  UnstyledButton,
  Center,
  rem,
} from '@mantine/core'
import { CaretUp, CaretDown, CaretUpDown } from "@phosphor-icons/react";
import {
  useReactTable,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  ColumnDef,
} from '@tanstack/react-table'

interface TableSortProps<T extends object> {
  data: T[]
  columns: ColumnDef<T>[]
}

export function AdminTable<T extends object>({ data, columns }: TableSortProps<T>) {
  const [sorting, setSorting] = React.useState<SortingState>([])

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    enableMultiSort: true,
    maxMultiSortColCount: 2
  })

  return (
    <Table striped highlightOnHover withColumnBorders>
      <Table.Thead>
        {table.getHeaderGroups().map((headerGroup) => (
          <Table.Tr key={headerGroup.id}>
            {headerGroup.headers.map((header) => (
              <Table.Th key={header.id}>
                {header.isPlaceholder ? null : (
                  <UnstyledButton
                    onClick={header.column.getToggleSortingHandler()}
                    className="w-full"
                  >
                    <Group justify="space-between">
                      <Text fw={500} fz="sm">
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </Text>
                      <Center>
                        {header.column.getIsSorted() === 'desc' ? (
                          <CaretDown size={rem(14)} />
                        ) : header.column.getIsSorted() === 'asc' ? (
                          <CaretUp size={rem(14)} />
                        ) : (
                          <CaretUpDown size={rem(14)} />
                        )}
                      </Center>
                    </Group>
                  </UnstyledButton>
                )}
              </Table.Th>
            ))}
          </Table.Tr>
        ))}
      </Table.Thead>
      <Table.Tbody>
        {table.getRowModel().rows.map((row) => (
          <Table.Tr key={row.id}>
            {row.getVisibleCells().map((cell) => (
              <Table.Td key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </Table.Td>
            ))}
          </Table.Tr>
        ))}
      </Table.Tbody>
    </Table>
  )
}
