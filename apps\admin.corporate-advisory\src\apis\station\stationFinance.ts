import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const stationFinance = {
    // 列表
    list: async (params: TStationFinanceSearchParams) => {
        const { error, result } = await cnaRequest<TStationFinanceResponse>(
            `/api/v1/admin/finance/index`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 统计
    staticTotal: async () => {
        const { error, result } = await cnaRequest<TStationFinanceStaticTotal>(
            `/api/v1/admin/finance/total`,
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default stationFinance;
