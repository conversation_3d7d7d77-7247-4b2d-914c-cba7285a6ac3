import { Group, Menu, ActionIcon, Checkbox, ScrollArea } from "@mantine/core";
import { DownloadSimple, SplitVertical, ArrowsIn, ArrowsOut, GearSix } from "@phosphor-icons/react";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { DENSITY_OPTIONS } from "../types";

export function ActionBar({
    table,
    onExport,
    isFullscreen,
    toggleFullscreen,
    density,
    setDensity,
}) {
    return (
        <Group justify="flex-end">
            {/* 导出按钮 */}
            {onExport && (
                <CnaButton
                    size="xs"
                    leftSection={<DownloadSimple size={16} />}
                    onClick={onExport}
                >
                    导出数据
                </CnaButton>
            )}

            <Group
                gap={0}
                style={{ zIndex: isFullscreen ? 200 : "auto" }}
            >
                {/* 密度选择 */}
                <Menu
                    shadow="md"
                    width={100}
                >
                    <Menu.Target>
                        <ActionIcon variant="subtle">
                            <SplitVertical />
                        </ActionIcon>
                    </Menu.Target>
                    <Menu.Dropdown>
                        {DENSITY_OPTIONS.map(({ value, label }) => (
                            <Menu.Item
                                key={value}
                                onClick={() => setDensity(value)}
                                rightSection={density === value ? "✓" : null}
                            >
                                {label}
                            </Menu.Item>
                        ))}
                    </Menu.Dropdown>
                </Menu>

                {/* 列设置菜单 */}
                <Menu
                    shadow="md"
                    width={200}
                >
                    <Menu.Target>
                        <ActionIcon variant="subtle">
                            <GearSix />
                        </ActionIcon>
                    </Menu.Target>
                    <Menu.Dropdown>
                        <Menu.Label>显示列</Menu.Label>
                        <ScrollArea className="tw-h-64">
                            <Menu.Item closeMenuOnClick={false}>
                                <Checkbox
                                    checked={table.getIsAllColumnsVisible()}
                                    indeterminate={
                                        !table.getIsAllColumnsVisible() &&
                                        table.getAllLeafColumns().some((col) => col.getIsVisible())
                                    }
                                    onChange={(e) =>
                                        table.toggleAllColumnsVisible(e.currentTarget.checked)
                                    }
                                    label="全选"
                                />
                            </Menu.Item>
                            {table.getAllLeafColumns().map((column) => (
                                <Menu.Item
                                    key={column.id}
                                    closeMenuOnClick={false}
                                >
                                    <Checkbox
                                        checked={column.getIsVisible()}
                                        onChange={(e) =>
                                            column.toggleVisibility(e.currentTarget.checked)
                                        }
                                        label={column.columnDef.header?.toString()}
                                    />
                                </Menu.Item>
                            ))}
                        </ScrollArea>
                    </Menu.Dropdown>
                </Menu>

                {/* 全屏切换按钮 */}
                <ActionIcon
                    variant="subtle"
                    onClick={toggleFullscreen}
                >
                    {isFullscreen ? <ArrowsIn /> : <ArrowsOut />}
                </ActionIcon>
            </Group>
        </Group>
    );
}
