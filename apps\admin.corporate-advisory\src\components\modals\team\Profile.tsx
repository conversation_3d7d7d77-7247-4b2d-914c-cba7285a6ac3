import api from "@/apis";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Modal, Stack, TextInput, Select } from "@mantine/core";
import React, { useCallback, useState, useEffect } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm, Controller } from "react-hook-form";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn, useMount } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";

const teamSchema = z.object({
    rank: z.string().min(1, "请选择级别"),
    name: z.string().min(1, "请输入名字"),
    email: z.string().min(1, "请输入邮件地址"),
    phone: z.string().min(1, "请输入手机号"),
    referee: z.string().optional(),
});

type TeamForm = z.infer<typeof teamSchema>;

interface ProfileProps {
    profile?: TTeam | null;
    onUpdateSuccess?: () => void;
    onCloseSuccess?: () => void;
}

const Profile: React.FC<ProfileProps> = React.memo(
    ({ profile, onUpdateSuccess = () => {}, onCloseSuccess = () => {} }) => {
        const { lang } = useSettingStore();

        const [loading, setLoading] = useState(false);

        const openConfirm = useModalStore.use.openConfirm();

        const [rankOptions, setRankOptions] = useState<
            { value: string; label: string }[]
        >([]);

        const { show, close } = useModalStore(
            useShallow((state) => ({
                show: state.teamProfileModal,
                close: state.close,
            }))
        );

        useEffect(() => {
            if (show === false) {
                reset();
            }
        }, [show]);

        const closeModal = useMemoizedFn(() => {
            close("teamProfileModal");
            onCloseSuccess();
        });

        const {
            control,
            handleSubmit,
            formState: { errors },
            reset,
        } = useForm<TeamForm>({
            defaultValues: {
                rank: "11",
                name: "",
                email: "",
                phone: "",
                referee: "",
            },
            resolver: zodResolver(teamSchema),
        });

        useEffect(() => {
            if (profile) {
                reset({
                    rank: profile.rank.toString(),
                    name: profile.name,
                    email: profile.email,
                    phone: profile.phone,
                    referee: "",
                });
            } else {
                reset({
                    rank: "11",
                    name: "",
                    email: "",
                    phone: "",
                    referee: "",
                });
            }
        }, [profile, reset]);

        const submitForm: SubmitHandler<TeamForm> = useCallback(
            async (data) => {
                setLoading(true);
                try {
                    if (profile) {
                        // 更新
                        const res = await api.team.update(profile.id, {
                            name: data.name,
                            email: data.email,
                            phone: data.phone,
                        });

                        if (res) {
                            noty.success("操作成功");
                            onUpdateSuccess();
                            closeModal();
                        }
                    } else {
                        // 创建
                        const res = await api.team.store({
                            name: data.name,
                            email: data.email,
                            phone: data.phone,
                        });

                        if (res) {
                            noty.success("操作成功");
                            onUpdateSuccess();
                            closeModal();
                        }
                    }
                } catch (error) {
                    noty.error("操作失败，请重试");
                } finally {
                    setLoading(false);
                }
            },
            [profile, onUpdateSuccess, closeModal]
        );

        const handleSave = useCallback(() => {
            openConfirm({
                title: "提示",
                message: "您确定此操作么？",
                onConfirm: handleSubmit(submitForm),
            });
        }, [openConfirm, handleSubmit, submitForm]);

        const fetchRankOptions = async () => {
            const response = await api.team.getRankOptions();
            setRankOptions(response);
        };

        useMount(() => {
            fetchRankOptions();
        });

        if (!show) {
            return null;
        }

        return (
            <Modal
                opened={true}
                onClose={closeModal}
                title={"三三制 - " + (profile ? "更新" : "创建")}
                size="xl"
            >
                <form onSubmit={handleSubmit(handleSave)}>
                    <Stack gap="lg">
                        <Controller
                            name="rank"
                            control={control}
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="级别"
                                    placeholder="请选择级别"
                                    data={rankOptions}
                                    allowDeselect={false}
                                    withCheckIcon
                                    error={errors.rank?.message}
                                />
                            )}
                        />
                        <Controller
                            name="name"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="名字"
                                    placeholder="输入名字"
                                    error={errors.name?.message}
                                />
                            )}
                        />
                        <Controller
                            name="email"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="邮件地址"
                                    placeholder="输入邮件地址"
                                    error={errors.email?.message}
                                />
                            )}
                        />
                        <Controller
                            name="phone"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="手机号"
                                    placeholder="输入手机号"
                                    error={errors.phone?.message}
                                />
                            )}
                        />
                        <Controller
                            name="referee"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="推荐人"
                                    placeholder="输入推荐人"
                                    error={errors.referee?.message}
                                />
                            )}
                        />

                        <ModalFooter
                            // timelineContent="最近修改: Amos Wu (2024-11-18 12:00:00)"
                            buttons={[
                                {
                                    key: "create",
                                    label: profile ? "更新" : "创建",
                                    leftSection: <Check size={18} />,
                                    loading: loading,
                                    type: "submit",
                                },
                                {
                                    key: "close",
                                    label: "关闭",
                                    style: "outline",
                                    leftSection: <X size={18} />,
                                    onClick: closeModal,
                                },
                            ]}
                        />
                    </Stack>
                </form>
            </Modal>
        );
    }
);

export default Profile;
