import { viewDay, viewMonthAgenda, viewMonthGrid, viewWeek } from "@schedule-x/calendar";
import { ScheduleXCalendar, useCalendarApp } from "@schedule-x/react";

import { createEventModalPlugin } from "@schedule-x/event-modal";
import { createEventsServicePlugin } from "@schedule-x/events-service";

import "@/schedule-x.css";

import api from "@/apis";
import CalendarEventCreationModal from "@/components/modals/calendar/CalendarEventCreationModal";
import useModalStore from "@/store/modal";
import { t } from "@code.8cent/i18n";
import { PageHeader } from "@code.8cent/react/components";
import noty from "@code.8cent/react/noty";
import useSettingStore from "@code.8cent/store/setting";
import { Stack } from "@mantine/core";
import { useRequest } from "ahooks";
import dayjs from "dayjs";
import { useState } from "react";

const MemberCalendarPage = () => {
    const openModal = useModalStore.use.open();

    const closeModal = useModalStore.use.close();

    const lang = useSettingStore.use.lang();

    const plugins = [
        createEventsServicePlugin(),
        createEventModalPlugin(),
    ];

    const [selectDate, setSelectDate] = useState(dayjs().format("YYYY-MM-DD HH:mm"));

    const { run: getEvents } = useRequest(
        async (start_time: number, end_time: number) => {
            calendar.events.set([]);

            const events = await api.schedule.list(start_time, end_time);

            let formattedEvents = events.map((event, _i) => {
                return {
                    id: new Date().getTime(),
                    description: event.desc,
                    title: event.title,
                    start: dayjs(event.start_time * 1000).format("YYYY-MM-DD HH:mm"),
                    end: dayjs(event.end_time * 1000).format("YYYY-MM-DD HH:mm"),
                };
            });

            console.log(formattedEvents);

            calendar.events.set(formattedEvents);
        },
        {
            manual: true,
        }
    );

    const calendar = useCalendarApp(
        {
            views: [viewMonthAgenda, viewMonthGrid, viewWeek, viewDay],
            defaultView: viewMonthGrid.name,
            events: [],
            locale: "zh-CN",
            firstDayOfWeek: 0,
            callbacks: {
                /**
                 * Runs after the calendar is rendered
                 * */
                onRender($app) {
                    const range = $app.calendarState.range.value;
                    // fetchYourEventsFor(range.start, range.end);
                    getEvents(dayjs(range.start).unix(), dayjs(range.end).unix());
                },
                onRangeUpdate(range) {
                    getEvents(dayjs(range.start).unix(), dayjs(range.end).unix());
                },
                onEventClick: (event) => {
                    console.log(event);
                },
                onClickDate(date) {
                    setSelectDate(date);
                    openModal("calendarEventCreation");
                    console.log("onClickDate", date); // e.g. 2024-01-01
                },
                onClickDateTime(dateTime) {
                    console.log("onClickDateTime", dateTime);
                },
                async onDoubleClickAgendaDate(date) {
                    setSelectDate(date);
                    openModal("calendarEventCreation");
                },
            },
        },
        plugins
    );

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0 [&>div.sx-react-calendar-wrapper]:tw-h-full">
            <PageHeader
                title={t("navigation.schedule", lang)}
                desc={t("navigation.schedule.system.quote", lang)}
            />

            <ScheduleXCalendar
                calendarApp={calendar}
                customComponents={
                    {
                        // headerContent: (e) => {
                        //     return (
                        //         <Group className="tw-justify-between tw-text-basic-4 tw-w-full">
                        //             <DatePickerInput
                        //                 miw={200}
                        //                 placeholder={dayjs().format("YYYY-MM-DD")}
                        //                 classNames={{ wrapper: "tw-border" }}
                        //             />
                        //         </Group>
                        //     );
                        // },
                    }
                }
            />
            <CalendarEventCreationModal
                defaultValue={selectDate}
                onEventCreate={async (event) => {
                    let status = await api.schedule.add({
                        title: event.title,
                        start_time: dayjs(event.start).unix(),
                        end_time: dayjs(event.end).unix(),
                        notify_type: event.notify_type,
                        desc: event.desc,
                    });

                    if (status === true) {
                        calendar.events.add({
                            ...event,
                            id: new Date().getTime(),
                            description: event.desc,
                        });

                        noty.success("行程添加成功");

                        closeModal("calendarEventCreation");
                    }
                }}
            />
        </Stack>
    );
};

export default MemberCalendarPage;
