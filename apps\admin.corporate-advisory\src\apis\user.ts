import cnaRequest from "@code.8cent/utils/cnaRequest";
import noty from "@code.8cent/react/noty";

const user = {
    updatePassword: async (oldPassword: string, newPassword: string) => {
        let updatePasswordRes = await cnaRequest(
            "/api/v1/password/update",
            "POST",
            {
                oldPassword,
                newPassword,
            }
        );

        if (updatePasswordRes.error) {
            return false;
        } else {
            return true;
        }
    },
    resetPassword: async (params: {
        code: string;
        email: string;
        password: string;
    }) => {
        const resetPasswordRes = await cnaRequest(
            "/api/v1/password/forgotPsw/reset",
            "POST",
            params
        );

        if (resetPasswordRes.error) {
            return false;
        } else {
            return true;
        }
    },
    getUserProfile: async () => {
        let profileRes = await cnaRequest<UserProfileResponse>(
            "/api/v1/user/profile",
            "POST"
        );

        const { result, error } = profileRes;

        if (result) {
            return result.data;
        } else {
            return null;
        }
    },
    updateUserProfile: async (
        profile: Partial<
            UserProfileResponse & {
                professional: UserProfileResponse["userProfessional"];
                skill: UserProfileResponse["userSkill"];
                experience: UserProfileResponse["userExperience"];
            }
        >
    ) => {
        let updateProfileRes = await cnaRequest(
            "/api/v1/user/updateProfile",
            "POST",
            profile
        );

        const { result, error } = updateProfileRes;

        if (result) {
            return true;
        } else {
            return false;
        }
    },
    /**
     * Updates the user's avatar.
     *
     * @param {File} avatar The avatar file selected by the user.
     * @returns Returns the updated avatar URL, or null if the update fails.
     */
    updateAvatar: async (avatar: File) => {
        const formData = new FormData();

        formData.append("avatar", avatar);

        let updateAvatarRes = await cnaRequest<string>(
            "/api/v1/user/updateAvatar",
            "POST",
            formData
        );

        const { result, error } = updateAvatarRes;

        if (error) {
            return null;
        } else {
            return result.data;
        }
    },

    readNotification: async (params: ReadNotificationParams) => {
        let url = "";

        if (params.mode === "all") {
            url = "/api/v1/notification/readAll";
        } else {
            url = "/api/v1/notification/readSingle";
        }

        let readNotificationRes = await cnaRequest(
            url,
            "POST",
            params.mode === "all"
                ? {}
                : { notificationID: params.notificationID }
        );

        const { result, error } = readNotificationRes;

        if (result) {
            return true;
        } else {
            return false;
        }
    },

    getNotificationCount: async () => {
        let url = "/api/v1/notification/notificationStatusNumber";

        type NotificationCountResponse = {
            all: number;
            unread: number;
        };

        let countRes = await cnaRequest<NotificationCountResponse>(url, "GET");

        const { result, error } = countRes;

        if (error) {
            return {
                all: 0,
                unread: 0,
            };
        } else {
            return result.data;
        }
    },

    getDocumentToken: async (documentID: number) => {
        let docRealLinkRes = await cnaRequest<string>(
            "/api/v1/document/generateKey",
            "POST",
            {
                documentID,
            }
        );

        const { error, result } = docRealLinkRes;

        if (error) {
            return null;
        } else {
            return result.data;
        }
    },

    updateLanguageRegionSetting: async (langRegion: {
        settingCurrency?: string;
        settingTimeFormat?: string;
        settingTimezone?: string;
        settingDateFormat?: string;
        settingLanguage?: string;
    }) => {
        const { result, error } = await cnaRequest(
            "/api/v1/setting/languageArea/update",
            "POST",
            JSON.stringify(langRegion)
        );

        if (error) {
            return false;
        } else {
            return true;
        }
    },

    // 获取登录的行政用户信息
    getAuthUser: async () => {
        const { error, result } = await cnaRequest<UserProfileResponse>(
            "/api/v1/admin/profile",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },

    getWizardState: async () => {
        const { result, error } = await cnaRequest<{
            state: number;
        }>("/api/v1/admin/login/getRegisterSettingState", "GET");

        if (error) {
            return null;
        } else {
            return result.data;
        }
    },

    setLanguage: async (languageCode: string) => {
        const { result, error } = await cnaRequest<{ state: number }>(
            "/api/v1/admin/login/setLanguage",
            "POST",
            { languageCode }
        );

        if (error) {
            noty.error("发生错误", error.message);
            return null;
        } else {
            return result.data;
        }
    },

    setSecurity: async (params: {
        currentPassword: string;
        newPassword: string;
        passwordConfirmation: string;
    }) => {
        const { result, error } = await cnaRequest<{ state: number }>(
            "/api/v1/admin/login/setSecurity",
            "POST",
            params
        );

        if (error) {
            noty.error("发生错误", error.message);
            return null;
        } else {
            return result.data;
        }
    },

    setProfile: async (file: File) => {
        const formData = new FormData();

        formData.append("file", file);

        const { result, error } = await cnaRequest<{ token: string }>(
            "/api/v1/admin/login/setProfile",
            "POST",
            formData
        );

        if (error) {
            noty.error("发生错误", error.message);
            return null;
        } else {
            return result.data;
        }
    },
};

type ReadNotificationParams =
    | {
          mode: "all";
      }
    | {
          mode: "single";
          notificationID: string;
      };

export default user;
