import api from "@/apis";
import useModalStore from "@/store/modal";
import { useMemoizedFn } from "ahooks";
import React, { useState } from "react";
import { Button, Group, Modal, PasswordInput } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import SHA256 from "crypto-js/sha256";
import PasswordCheckerInput from "@/components/password/PasswordCheckerInput";
import PasswordMatcherInput from "@/components/password/PasswordMatcherInput";
import CnaButton from "@code.8cent/react/components/CnaButton";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";

type ResetPasswordFormInput = {
    currentPassword: string;
    newPassword: string;
    confirmNewPassword: string;
};

const resetPasswordSchema = z
    .object({
        currentPassword: z.string().min(1, "password.current.empty"),
        newPassword: z.string().min(1, "password.new.empty"),
        confirmNewPassword: z.string().min(1, "password.enter.empty"),
    })
    .refine((data) => data.newPassword === data.confirmNewPassword, {
        message: "password.enter.not.match",
        path: ["confirmNewPassword"],
    });

const initalValues = {
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
};

const PasswordResetModal: React.FC = () => {
    const lang = useSettingStore.use.lang();

    const {
        show,
        close,
        openAlert: alert,
    } = useModalStore(
        useShallow((state) => ({
            show: state.passwordReset,
            close: state.close,
            openAlert: state.openAlert,
        }))
    );

    const [updating, setUpdating] = useState(false);

    const closeModal = () => {
        close("passwordReset");
        reset(initalValues);
    };

    const {
        register,
        handleSubmit,
        formState: { errors },
        control,
        trigger,
        reset,
    } = useForm<ResetPasswordFormInput>({
        defaultValues: initalValues,
        resolver: zodResolver(resetPasswordSchema),
        mode: "all",
    });

    const onSubmit: SubmitHandler<ResetPasswordFormInput> = async (data) => {
        setUpdating(true);

        let updateRes = await api.user.updatePassword(
            SHA256(data.currentPassword).toString(),
            SHA256(data.newPassword).toString()
        );

        if (updateRes === true) {
            closeModal();
            alert(t("password.update", lang), t("password.update.success", lang), "success");
        } else {
            alert(t("password.update", lang), t("password.update.fail", lang), "danger");
        }

        setUpdating(false);
    };

    return (
        <Modal opened={show} onClose={closeModal} title={t("password.update", lang)}>
            <form onSubmit={handleSubmit(onSubmit)}>
                <PasswordInput
                    className="tw-mb-6"
                    label={t("password.current", lang)}
                    placeholder={t("password.current.details", lang)}
                    {...register("currentPassword")}
                    error={errors.currentPassword ? true : false}
                    required
                />

                <PasswordCheckerInput<ResetPasswordFormInput>
                    label={t("password.new", lang)}
                    name="newPassword"
                    control={control}
                    trigger={trigger}
                    inputProps={{
                        className: "tw-mb-6",
                        placeholder: t("password.new.details", lang),
                        required: true,
                    }}
                    rulerProps={{
                        className: "-tw-mt-3",
                    }}
                />

                <PasswordMatcherInput<ResetPasswordFormInput>
                    label={t("password.confirm", lang)}
                    name="confirmNewPassword"
                    control={control}
                    trigger={trigger}
                    inputProps={{
                        className: "tw-mb-6",
                        placeholder: t("password.reconfirm", lang),
                        required: true,
                    }}
                    rulerProps={{
                        className: "-tw-mt-3",
                    }}
                />

                <Group className="tw-justify-end">
                    <Button variant="default" onClick={closeModal}>
                    {t("common.close", lang)}
                    </Button>
                    <CnaButton
                        color="cna"
                        type="submit"
                        className="btn btn-primary"
                        loading={updating}
                    >
                        {t("common.save.update", lang)}
                    </CnaButton>
                </Group>
            </form>
        </Modal>
    );
};

export default PasswordResetModal;
