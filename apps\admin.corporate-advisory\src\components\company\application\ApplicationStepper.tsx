import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import {
    Box,
    Center,
    Grid,
    Group,
    SimpleGrid,
    Stack,
    Text,
} from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { Mouse<PERSON>vent<PERSON>and<PERSON>, useState } from "react";

const StepItem: React.FC<{
    title: string;
    desc: string;
    active?: boolean;
    onClick?: MouseEventHandler<HTMLDivElement>;
}> = ({ title, desc, active = false, onClick = () => {} }) => {
    return (
        <Center
            className={`${
                active === true ? "tw-bg-neutral-100" : "tw-bg-white"
            } tw-cursor-pointer first:!tw-border-l tw-border-b tw-border-r md:odd:tw-border-l-0 odd:tw-border-l`}
            onClick={onClick}
        >
            <Stack className="tw-gap-1 tw-p-5">
                <Text className="tw-text-left tw-text-xs">{title}: </Text>
                <Text className="tw-text-left tw-text-xs">{desc}</Text>
            </Stack>
        </Center>
    );
};

const CompanyApplicationStepper: React.FC<{
    step?: number;
    onStepChange?: (step: number) => void;
}> = ({ step = 1, onStepChange = () => {} }) => {
    const lang = useSettingStore.use.lang();

    const stepItemClick = useMemoizedFn((step: number) => {
        typeof onStepChange === "function" && onStepChange(step);
    });

    const [steps] = useState([
        {
            title: "project.edit.step1",
            desc: "project.edit.submit_form",
        },
        {
            title: "project.edit.step2",
            desc: "project.edit.submit_pay",
        },
        {
            title: "project.edit.step3",
            desc: "project.edit.account_setting",
        },
        {
            title: "project.edit.step4",
            desc: "project.edit.prepare_report",
        },
        {
            title: "project.edit.step5",
            desc: "project.edit.complete",
        },
    ]);

    return (
        <Box>
            <Group justify="space-between" className="tw-bg-neutral-200 tw-p-2">
                <Text size="xs" className="tw-text-gray-500">
                    {t("application.process", lang)}
                </Text>
                <Text size="xs" className="tw-text-gray-500">
                    {t("project.name", lang)}
                </Text>
            </Group>
            <SimpleGrid cols={{ base: 2, md: 5 }} spacing={0}>
                {steps.map((item, idx) => (
                    <StepItem
                        title={t(item.title, lang)}
                        desc={t(item.desc, lang)}
                        key={idx}
                        active={step === idx + 1}
                        onClick={() => stepItemClick(idx + 1)}
                    />
                ))}
            </SimpleGrid>
        </Box>
    );
};

export default CompanyApplicationStepper;
