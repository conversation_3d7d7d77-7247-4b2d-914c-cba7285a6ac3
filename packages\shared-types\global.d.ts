import LocalForage from "localforage";
import React from "react";
import { UseFormReset } from "react-hook-form";

declare global {
    interface Window {
        api_base_url: string;
        api_desk_url?: string; // AI工作站接口地址
        app_title: string;
        team_prefix_id: string;
        app_prefer_lang: LangCode;
        localForage: LocalForage;
        WeixinJSBridge: {
            invoke: (name: string, param: any, callback: ({ err_msg: string }) => void) => void;
        };
    }

    declare module "*.svg?react" {
        import React from "react";
        const ReactComponent: React.FC<React.SVGProps<SVGSVGElement>>;
        export default ReactComponent;
    }

    type LanguageItem = {
        languageType: string;
        languageCode: string;
    };

    type CurrencyItem = {
        currencyName: string;
        currencyCode: string;
    };

    type TimezoneItem = {
        timeZone: string;
        timeZoneGMT: string;
    };

    type DateFormatItem = {
        dateFormat: string;
    };

    type TimeFormatItem = {
        timeFormat: string;
    };

    type SettingConfigOptionResponse = {
        countryID: string;
        currencyList: CurrencyItem[];
        languageList: LanguageItem[];
        timezoneList: TimeZoneItem[];
        timeFormatList: TimeFormatItem[];
        dateFormatList: DateFormatItem[];
    };

    type LanguageStructure = {
        structureCode: string;
        structureEN: string;
        structureMS: string;
        structureZH: string;
        structureZT: string;
    };

    type CountryDataItem = {
        countryCode: string;
        countryEN: string;
        countryID: number;
        countryISOCode2: string;
        countryMS: string;
        countryZH: string;
        countryZT: string;
        // [property: string]: any;
    };

    interface ImportMetaEnv {
        readonly APP_PREFER_LANG: string;
        // Add other variables here...
    }

    interface ImportMeta {
        readonly env: ImportMetaEnv;
    }

    type SharedFormRef<T = any> = {
        submit: HTMLFormElement["submit"];
        reset: UseFormReset<T>;
    };

    /**
     * A type representing the available languages.
     * @var ZH: Simplefied Chinese
     * @var EN: English
     * @var MS: Malay
     * @var ZT: Traditional Chinese
     */
    type LangCode = "ZH" | "EN" | "MS" | "ZT";

    type AreaData = {
        id: number;
        name: string;
        children?: AreaData[];
    };
}

export {};
