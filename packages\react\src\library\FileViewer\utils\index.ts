import { cnaRequest } from "@code.8cent/utils";

type CnaRequest = typeof cnaRequest;

async function getFileByUrl(
    url: string,
    onDownloadProgress?: Parameters<CnaRequest>[3]["onDownloadProgress"]
) {
    let { result, error } = await cnaRequest<Blob>(
        url,
        "GET",
        {},
        {
            responseType: "blob",
            onDownloadProgress: (event) => {
                if (typeof onDownloadProgress === "function") {
                    onDownloadProgress(event);
                }
            },
        }
    );

    if (!error) {
        let file = new File([result.data], url.split("/").pop(), {
            type: result.data.type,
        });

        return file;
    } else {
        return null;
    }
}

export default {
    getFileByUrl,
};

export { getFileByUrl };
