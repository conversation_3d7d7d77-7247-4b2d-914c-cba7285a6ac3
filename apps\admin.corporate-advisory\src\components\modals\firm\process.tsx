import { Modal, <PERSON>ack, Group, Text, Badge, Button, Grid, Paper } from "@mantine/core";
import useModalStore from "@/store/modal";
import {
    Eye,
    MapPin,
    Building,
    Calendar,
    FileText,
    Users,
    Ruler,
    User,
} from "@phosphor-icons/react";
import dayjs from "dayjs";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import useDataStore from "@code.8cent/store/data";

const STATUS_MAP = {
    0: { label: "待审核", color: "orange" },
    10: { label: "审核通过", color: "green" },
    20: { label: "审核不通过", color: "red" },
};

const STEP_MAP = {
    1: { label: "第一步", color: "blue" },
    2: { label: "第二步", color: "cyan" },
    3: { label: "第三步", color: "indigo" },
};

const LEVEL_MAP = {
    1: "省级",
    2: "市级",
    3: "区级",
    4: "镇级",
};

const SPACE_TYPE_MAP = {
    0: "自有",
    1: "租赁",
};

const FirmProcessDetailModal = () => {
    const { openFileView } = useFileViewer();

    const processParams = useModalStore((state) => state.modalParams.firmProcessDetailModal);
    const process = processParams?.process as TFirmProcess;

    const show = useModalStore((state) => state.firmProcessDetailModal);
    const closeModal = useModalStore((state) => state.close);

    const { areas } = useDataStore();

    if (!process) return null;

    const { affiliated_firm, affiliated_firm_spaces } = process;

    // 递归查找地区名称
    const findAreaName = (areaId: number, areaList: AreaData[]): string => {
        for (const area of areaList) {
            if (area.id === areaId) {
                return area.name;
            }
            if (area.children) {
                const found = findAreaName(areaId, area.children);
                if (found) return found;
            }
        }
        return "";
    };

    // 预览文件
    const handlePreviewFile = (fileUrl: string, title: string) => {
        if (fileUrl) {
            openFileView(
                `${window.api_base_url}/api/v1/admin/firm/files?path=${fileUrl}&type=local`,
                { title }
            );
        }
    };

    // 预览图片
    const handlePreviewImage = (imageUrl: string, title: string) => {
        if (imageUrl) {
            openFileView(
                `${window.api_base_url}/api/v1/admin/firm/files?path=${imageUrl}&type=local`,
                { title }
            );
        }
    };

    return (
        <Modal
            opened={show}
            onClose={() => closeModal("firmProcessDetailModal")}
            title="事务所流程详情"
            size="xl"
        >
            <Stack
                gap="lg"
                className="tw-p-4"
            >
                {/* 流程信息 */}
                <Paper
                    p="md"
                    withBorder
                >
                    <Group
                        justify="space-between"
                        mb="md"
                    >
                        <Text
                            fw={600}
                            size="lg"
                        >
                            流程信息
                        </Text>
                        <Group gap="xs">
                            <Badge color={STEP_MAP[process.step]?.color || "gray"}>
                                {STEP_MAP[process.step]?.label || `第${process.step}步`}
                            </Badge>
                            <Badge color={STATUS_MAP[process.process_status]?.color || "gray"}>
                                {STATUS_MAP[process.process_status]?.label || "未知状态"}
                            </Badge>
                        </Group>
                    </Group>

                    <Grid>
                        <Grid.Col span={6}>
                            <Group gap="xs">
                                <User size={16} />
                                <Text>申请合伙人：{affiliated_firm.profile.profileName}</Text>
                            </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                            <Group gap="xs">
                                <Calendar size={16} />
                                <Text>
                                    申请时间：
                                    {dayjs(process.created_at).format("YYYY-MM-DD HH:mm:ss")}
                                </Text>
                            </Group>
                        </Grid.Col>
                        {affiliated_firm.profile_contracts &&
                            affiliated_firm.profile_contracts.length > 0 && (
                                <Grid.Col span={12}>
                                    <Group
                                        gap="xs"
                                        align="center"
                                    >
                                        <FileText size={16} />
                                        <Text>申请签署文书：</Text>
                                        {affiliated_firm.profile_contracts.map((contract) => (
                                            <Button
                                                key={contract.contract}
                                                size="xs"
                                                variant="outline"
                                                onClick={() =>
                                                    handlePreviewFile(
                                                        contract.contract,
                                                        "合伙人合同"
                                                    )
                                                }
                                            >
                                                查看文件
                                            </Button>
                                        ))}
                                    </Group>
                                </Grid.Col>
                            )}
                        {process.process_time && (
                            <Grid.Col span={6}>
                                <Group gap="xs">
                                    <Calendar size={16} />
                                    <Text>
                                        审核时间：
                                        {dayjs(process.process_time).format("YYYY-MM-DD HH:mm:ss")}
                                    </Text>
                                </Group>
                            </Grid.Col>
                        )}
                        {process.reason && (
                            <Grid.Col span={12}>
                                <Group
                                    gap="xs"
                                    align="flex-start"
                                >
                                    <FileText
                                        size={16}
                                        className="tw-mt-1"
                                    />
                                    <Text>审核意见：{process.reason}</Text>
                                </Group>
                            </Grid.Col>
                        )}
                    </Grid>
                </Paper>

                {/* 事务所基本信息 */}
                <Paper
                    p="md"
                    withBorder
                >
                    <Text
                        fw={600}
                        size="lg"
                        mb="md"
                    >
                        事务所基本信息
                    </Text>

                    <Grid>
                        <Grid.Col span={6}>
                            <Group gap="xs">
                                <Building size={16} />
                                <Text>
                                    事务所等级：{LEVEL_MAP[affiliated_firm.level] || "未知"}
                                </Text>
                            </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                            <Group gap="xs">
                                <MapPin size={16} />
                                <Text>
                                    省市区：
                                    {findAreaName(affiliated_firm.province, areas)}
                                    {findAreaName(affiliated_firm.city, areas) &&
                                        ` ${findAreaName(affiliated_firm.city, areas)}`}
                                    {findAreaName(affiliated_firm.district, areas) &&
                                        ` ${findAreaName(affiliated_firm.district, areas)}`}
                                    {findAreaName(affiliated_firm.town, areas) &&
                                        ` ${findAreaName(affiliated_firm.town, areas)}`}
                                </Text>
                            </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                            <Group gap="xs">
                                <MapPin size={16} />
                                <Text>
                                    场地类型：{SPACE_TYPE_MAP[affiliated_firm.space_type] || "未知"}
                                </Text>
                            </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                            <Group gap="xs">
                                <Ruler size={16} />
                                <Text>场地总面积：{affiliated_firm.total_area_space}</Text>
                            </Group>
                        </Grid.Col>
                        <Grid.Col span={6}>
                            <Group gap="xs">
                                <Calendar size={16} />
                                <Text>装修竣工日期：{affiliated_firm.completion_date}</Text>
                            </Group>
                        </Grid.Col>
                        <Grid.Col span={12}>
                            <Group
                                gap="xs"
                                align="flex-start"
                            >
                                <MapPin
                                    size={16}
                                    className="tw-mt-1"
                                />
                                <Text>详细地址：{affiliated_firm.address}</Text>
                            </Group>
                        </Grid.Col>

                        {/* 场地产权或租赁合同文件 */}
                        {affiliated_firm.space_property && (
                            <Grid.Col span={12}>
                                <Group
                                    gap="xs"
                                    align="center"
                                >
                                    <FileText size={16} />
                                    <Text>场地产权或租赁合同：</Text>
                                    <Button
                                        size="xs"
                                        variant="outline"
                                        onClick={() =>
                                            handlePreviewFile(
                                                affiliated_firm.space_property,
                                                "场地产权或租赁合同"
                                            )
                                        }
                                    >
                                        查看文件
                                    </Button>
                                </Group>
                            </Grid.Col>
                        )}
                    </Grid>
                </Paper>

                {/* 场地信息 */}
                {affiliated_firm_spaces && affiliated_firm_spaces.length > 0 && (
                    <Paper
                        p="md"
                        withBorder
                    >
                        <Text
                            fw={600}
                            size="lg"
                            mb="md"
                        >
                            场地信息
                        </Text>

                        {affiliated_firm_spaces.map((space, index) => (
                            <Paper
                                key={space.id}
                                p="sm"
                                withBorder
                                mb="sm"
                            >
                                <Group
                                    justify="space-between"
                                    mb="xs"
                                >
                                    <Text fw={500}>{space.space_name}</Text>
                                    <Text c="dimmed">场地 {index + 1}</Text>
                                </Group>

                                <Grid>
                                    <Grid.Col span={6}>
                                        <Group gap="xs">
                                            <Ruler size={14} />
                                            <Text>容纳面积：{space.space_area}</Text>
                                        </Group>
                                    </Grid.Col>
                                    <Grid.Col span={6}>
                                        <Group gap="xs">
                                            <Users size={14} />
                                            <Text>容纳人数：{space.space_capacity}人</Text>
                                        </Group>
                                    </Grid.Col>
                                    {space.space_equipment_detail && (
                                        <Grid.Col span={12}>
                                            <Group
                                                gap="xs"
                                                align="flex-start"
                                            >
                                                <Building
                                                    size={14}
                                                    className="tw-mt-1"
                                                />
                                                <Text>
                                                    设备明细：{space.space_equipment_detail}
                                                </Text>
                                            </Group>
                                        </Grid.Col>
                                    )}

                                    {/* 场地照片 (多张) */}
                                    {space.space_photos && (
                                        <Grid.Col span={12}>
                                            <Group
                                                gap="xs"
                                                align="center"
                                            >
                                                <Eye size={14} />
                                                <Text>场地照片：</Text>
                                                {space.space_photos.map((photo, index) => (
                                                    <Button
                                                        key={index}
                                                        size="xs"
                                                        variant="outline"
                                                        onClick={() =>
                                                            handlePreviewImage(
                                                                photo,
                                                                `${space.space_name}场地照片`
                                                            )
                                                        }
                                                    >
                                                        查看照片
                                                    </Button>
                                                ))}
                                            </Group>
                                        </Grid.Col>
                                    )}
                                </Grid>
                            </Paper>
                        ))}
                    </Paper>
                )}

                {/* 操作按钮 */}
                <Group justify="flex-end">
                    <Button onClick={() => closeModal("firmProcessDetailModal")}>关闭</Button>
                </Group>
            </Stack>
        </Modal>
    );
};

export default FirmProcessDetailModal;
