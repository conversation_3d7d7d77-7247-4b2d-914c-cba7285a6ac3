import { Stack, Image, Title, Box } from "@mantine/core";
import React, { PropsWithChildren } from "react";

const AuthFormCard: React.FC<PropsWithChildren<{ title?: string; className?: string }>> = ({
    title = "",
    children,
    className = "",
}) => {
    return (
        <Stack
            className={`tw-max-w-[95%] tw-mx-auto tw-px-4 tw-mt-5 tw-bg-white tw-shadow-md tw-rounded-lg tw-p-6 tw-border ${className}`}
        >
            <Box className="tw-w-full tw-py-4 tw-bg-basic-5">
                <Image
                    className="tw-mx-auto tw-w-[300px] tw-max-w-[90%]"
                    src="/images/auth-layout/form-header.png"
                />
            </Box>

            {/* <Title
                order={4}
                className="tw-font-bold tw-text-center"
            >
                {title}
            </Title> */}
            {children}
        </Stack>
    );
};

export default AuthFormCard;
