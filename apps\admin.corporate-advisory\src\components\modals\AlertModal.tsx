import useModalStore from "@/store/modal";
import { useMemoizedFn } from "ahooks";
import { useEffect, useRef, useState } from "react";
import { Button, ButtonProps, Group, Modal, Stack } from "@mantine/core";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { X } from "@phosphor-icons/react";
const AlertModal = () => {
    const { show, title, message, variant } = useModalStore.use.alert();

    const close = useModalStore.use.closeAlert();

    const lang = useSettingStore.use.lang();

    const [key, setKey] = useState(0);

    useEffect(() => {
        if (show) {
            setKey(new Date().getTime());
        }
    }, [show]);

    const icons = useMemoizedFn(() => {
        switch (variant) {
            case "success": {
                return `/images/icons/icon-alert-success.svg?k=${key}`;
            }
            case "danger": {
                return `/images/icons/icon-alert-error.svg?k=${key}`;
            }
            case "warning": {
                return `/images/icons/icon-alert-warning.svg?k=${key}`;
            }
            default: {
                return `/images/icons/icon-alert-success.svg?k=${key}`;
            }
        }
    });

    const colors: () => ButtonProps["color"] = useMemoizedFn(() => {
        switch (variant) {
            case "success": {
                return "dark.3";
            }
            case "danger": {
                return "dark.3";
            }
            case "warning": {
                return "dark.3";
            }
            default: {
                return "dark.3";
            }
        }
    });

    return (
        <Modal
            onClose={close}
            opened={show}
            title={title}
            zIndex={9999}
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-pt-5">
                {show === true && (
                    <img src={icons()} className="tw-w-[80px] tw-mx-auto" />
                )}
                <p className="tw-text-center tw-text-sm">{message}</p>
            </Stack>
            <Group justify="end" className="tw-border-t tw-pt-4 tw-mt-4">
                <CnaButton
                    color="cna"
                    onClick={close}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default AlertModal;
