import api from "@/apis";
import useRegisterStore from "@/store/register";
import { Group, Image, Stack, Table, Title } from "@mantine/core";
import { useRequest } from "ahooks";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { useState } from "react";
import { ArrowClockwise, Check } from "@phosphor-icons/react";
import { useEventBus } from "@/utils/eventBus";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

const RegisterPay = () => {
    const token = useRegisterStore.use.token();
    const bus = useEventBus();
    const lang = useSettingStore.use.lang();

    const { run: generatePayQRCode, data: payQRCode } = useRequest(async () => {
        let qrCode = await api.register.generateWechatPayQRCode(token);

        return qrCode;
    });

    const { data: payStatus } = useRequest(async() => {
        let payStatus = await api.register.checkPayStatus(token);

        if(payStatus.payment_state === true){
            bus.emit("account.register.step", 4);
        }

        return payStatus;
    }, {
        pollingInterval: 2000,
        pollingWhenHidden: false
    })

    return (
        <Stack>
            <Table
                withTableBorder
                classNames={{
                    th: "tw-bg-neutral-200 tw-text-neutral-700",
                    td: "tw-text-neutral-700",
                }}
                verticalSpacing={"sm"}
            >
                <Table.Thead>
                    <Table.Tr>
                        <Table.Th>{t("payment.information", lang)}</Table.Th>
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                    <Table.Tr>
                        <Table.Td>{t("payment.status", lang)}</Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>{t("payment.details", lang)}</Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>{t("payment.amount", lang)}</Table.Td>
                    </Table.Tr>
                    <Table.Tr>
                        <Table.Td>{t("payment.method", lang)}</Table.Td>
                    </Table.Tr>
                </Table.Tbody>
            </Table>

            <Stack gap={2}>
                <Image className="tw-mx-auto" src={payQRCode} w={180} h={180} />

                <CnaButton
                    variant="transparent"
                    leftSection={<ArrowClockwise />}
                    color="cna"
                    onClick={generatePayQRCode}
                    w="auto"
                    className="tw-mx-auto"
                >
                    {t("payment.qr.code", lang)}
                </CnaButton>
            </Stack>
        </Stack>
    );
};

export default RegisterPay;
