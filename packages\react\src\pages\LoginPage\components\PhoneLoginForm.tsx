import useSettingStore from "@code.8cent/store/setting";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { CnaButton, PhoneInput } from "../../../components";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import { count, error } from "console";
import { useCountDown, useRequest } from "ahooks";
import useGetCodeCountdown from "../../../modals/VerifyModal/hooks/useGetCodeCountdown";
import { cnaRequest } from "@code.8cent/utils";
import noty from "../../../library/noty";
import { TextInput } from "@mantine/core";
import { ArrowClockwise } from "@phosphor-icons/react";
import { Link, useNavigate } from "react-router-dom";
import { SHA256 } from "crypto-js";
import useWizardStore from "@code.8cent/store/wizard";

type PhoneLoginFormInput = {
    phone: string;
    prefixID: string;
    captcha: string;
    code: string;
};

const phoneLoginSchema = z.object({
    phone: z.string().regex(/^1[3-9]\d{9}$/g, "form.phone.number.incorrect"),
    prefixID: z.string().min(1, "form.country.code"),
    captcha: z.string().min(1),
    code: z.string().min(1),
});

const initialPhoneLoginFormValues = {
    phone: "",
    prefixID: "",
    captcha: "",
    code: "",
};

const PhoneLoginForm = () => {
    const { countryID } = useSettingStore();

    const navigate = useNavigate();

    const { countdown, startCountdown } = useGetCodeCountdown();

    const { filteredCountryDatas } = useDataStore();

    const { lang } = useSettingStore();

    const { setRegisterSetting, setState: setWizardState } = useWizardStore();

    const { setValue, getValues, control, handleSubmit, trigger } = useForm<PhoneLoginFormInput>({
        defaultValues: {
            phone: "",
            prefixID: countryID,
            captcha: "",
            code: "",
        },
        resolver: zodResolver(phoneLoginSchema),
    });

    const {
        run: getCaptchaCode,
        data: captchaRes,
        loading: gettingCode,
    } = useRequest(async () => {
        let { result, error } = await cnaRequest<{ key: string; img: string }>(
            "/api/v1/captcha",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    });

    const { run: getPhoneCode, loading: requestingPhoneCode } = useRequest(
        async () => {
            let phoneOk = await trigger("phone");

            if (phoneOk === true) {
                let { error } = await cnaRequest("/api/v1/login/sendCode", "POST", {
                    phone: getValues("phone"),
                });

                if (!error) {
                    startCountdown(60);
                } else {
                    noty.error(error.message);
                }
            }
        },
        {
            manual: true,
        }
    );

    const { run: phoneLogin, loading: loggingIn } = useRequest(
        async (data: PhoneLoginFormInput) => {
            const { result, error } = await cnaRequest<{
                token: string;
                profile_status: number;
                register_setting: string[];
            }>("/api/v1/login", "POST", {
                phone: data.phone,
                code: data.code,
                captcha_code: data.captcha,
                captcha_key: captchaRes?.key,
            });

            if (error) {
                getCaptchaCode();
                noty.error(t("login.fail", lang), error.message);
            }

            if (result) {
                let { data } = result;

                await window.localForage.setItem("cna-token", data.token);

                if (data.register_setting?.length > 0) {
                    setRegisterSetting(data.register_setting);
                    setWizardState(0);
                    navigate("/account/wizard", { replace: true });
                } else {
                    navigate("/member/profile", { replace: true });
                }
            }
        },
        {
            manual: true,
        }
    );

    return (
        <form onSubmit={handleSubmit(phoneLogin, (error) => console.log(error))}>
            <Controller
                control={control}
                name="phone"
                render={({ field, fieldState }) => (
                    <PhoneInput
                        prefixFlagKey={"countryISOCode2"}
                        prefixLabelKey={"countryCode"}
                        prefixValueKey={"countryID"}
                        label={t("project.company_form.label.phone", lang)}
                        wrapperProps={{
                            className: "tw-mb-3",
                            withAsterisk: true,
                        }}
                        prefixProps={{
                            readOnly: true,
                            w: 78,
                            rightSectionWidth: 0,
                            value: getValues("prefixID"),
                        }}
                        inputProps={{
                            ...field,
                            placeholder: t("login.placeholder.phone", lang),
                            rightSectionWidth: 120,
                            rightSection:
                                countdown > 0 ? (
                                    <CnaButton
                                        variant="transparent"
                                        color="dark.5"
                                        className="tw-cursor-not-allowed"
                                    >
                                        {`${Math.floor(countdown / 1000)} ${t(
                                            "validate.after.second",
                                            lang
                                        )}`}
                                    </CnaButton>
                                ) : (
                                    <CnaButton
                                        variant="transparent"
                                        color="dark.5"
                                        onClick={getPhoneCode}
                                        className="!tw-bg-transparent tw-w-full"
                                        loading={requestingPhoneCode}
                                    >
                                        {t("forget_password.btn.send_validate_code", lang)}
                                    </CnaButton>
                                ),
                            error: fieldState.error ? t(fieldState.error?.message, lang) : false,
                        }}
                        data={filteredCountryDatas()}
                    />
                )}
            />
            <Controller
                control={control}
                name="code"
                render={({ field, fieldState: { error } }) => (
                    <TextInput
                        className="tw-mb-3"
                        error={error ? true : false}
                        label={t("validation.code", lang)}
                        placeholder={t("validation.enter.code", lang)}
                        autoComplete="off"
                        withAsterisk
                        {...field}
                    />
                )}
            />

            <Controller
                name="captcha"
                control={control}
                render={({ field, fieldState }) => (
                    <TextInput
                        classNames={{
                            input: "tw-pl-[120px]",
                        }}
                        label={t("login.label.captcha", lang)}
                        placeholder={t("login.placeholder.captcha", lang)}
                        className="tw-mb-5"
                        leftSectionWidth={110}
                        leftSectionProps={{
                            className: "tw-justify-start",
                        }}
                        leftSection={
                            captchaRes && (
                                <img
                                    src={captchaRes.img}
                                    className="tw-block"
                                    alt="reCaptcha"
                                />
                            )
                        }
                        rightSection={
                            <CnaButton
                                variant="transparent"
                                color={"dark.3"}
                                onClick={getCaptchaCode}
                                className="tw-font-normal tw-px-2"
                                disabled={gettingCode}
                            >
                                <ArrowClockwise
                                    className={gettingCode ? "tw-animate-spin" : ""}
                                    size={24}
                                />
                            </CnaButton>
                        }
                        withAsterisk
                        {...field}
                        onChange={(e) => {
                            field.onChange(e.target.value.toUpperCase());
                        }}
                        error={fieldState.error ? true : false}
                    />
                )}
            />
            <div className="tw-flex tw-my-3 tw-space-x-3">
                <div className="tw-w-1/2">
                    <CnaButton
                        size="md"
                        fullWidth
                        variant="outline"
                        color="basic"
                        component={Link}
                        to="/account/register"
                    >
                        {t("form.sign.up", lang)}
                    </CnaButton>
                </div>
                <div className="tw-w-1/2">
                    <CnaButton
                        size="md"
                        fullWidth
                        variant="filled"
                        color="basic"
                        // loading={loggingIn}
                        type="submit"
                    >
                        {t("login.btn.login", lang)}
                    </CnaButton>
                </div>
            </div>
        </form>
    );
};

export default PhoneLoginForm;
