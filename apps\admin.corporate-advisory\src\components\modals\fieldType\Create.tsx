import api from "@/apis";
import { Modal, Stack, TextInput, Select } from "@mantine/core";
import { useState, useEffect } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";
import { useMemoizedFn } from "ahooks";
import { useShallow } from "zustand/react/shallow";

const accountSchema = z.object({
    industry_name: z.string().min(1, "请输入行业类型名称"),
    market_code: z.string().min(1, "请输入市场标识符"),
    id: z.string().optional(),
});


type AccountForm = z.infer<typeof accountSchema>;



const Create = ({

    onUpdateSuccess = () => { },
}: {

    onUpdateSuccess?: () => void;
}) => {
    const [loading, setLoading] = useState(false);
    const openConfirm = useModalStore.use.openConfirm();

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.accountCreateUpdateModal,
            close: state.close,
        }))
    );

    const modalParams = useModalStore((state) => state.modalParams.accountCreateUpdateModal);
    const account = modalParams?.account || {};

    const {
        handleSubmit,
        formState: { errors },
        control,
        reset,
    } = useForm<AccountForm>({
        defaultValues: {
            industry_name: account?.industry_name || "",
            market_code: account?.market_code || "",
            id: account?.id?.toString() || "",
        },
        resolver: zodResolver(accountSchema),
    });

    // 当 account 变化时重置表单
    useEffect(() => {
        if (account && Object.keys(account).length > 0) {
            reset({
                industry_name: account?.industry_name || "",
                market_code: account?.market_code || "",
                id: account?.id?.toString() || "",
            });
        }
    }, [account, reset]);

    const submitForm: SubmitHandler<AccountForm> = useMemoizedFn(async (data) => {
        const postData = {
            industry_name: data.industry_name,
            market_code: data.market_code,
        };

        setLoading(true);
        try {
            let res = false;
            if (account?.id) {
                // res = await api.fieldType.update(postData as any, account?.id);
            } else {
                res = await api.fieldType.store(postData as any);
            }
            if (res) {
                noty.success("操作成功");
                onUpdateSuccess();
                closeModal();
            }
        } catch (error) {
            noty.error("操作失败，请重试");
        } finally {
            setLoading(false);
        }
    });

    const handleSave = useMemoizedFn(() => {
        openConfirm({
            title: "提示",
            message: account?.id ? "您确定更新信息么？" : "您确定创建新记录么？",
            onConfirm: handleSubmit(submitForm),
        });
    });

    const closeModal = useMemoizedFn(() => {
        reset({
            industry_name: "",
            market_code: "",
            id: "",
        });
        close("accountCreateUpdateModal");
    });

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title={account?.id ? "更新信息" : "创建新记录"}
            size="md"
        >
            <form
                onSubmit={handleSubmit(handleSave, (error) => {
                    console.log(error);
                })}
            >
                <Stack gap={"lg"}>
                    <Controller
                        control={control}
                        name="industry_name"
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="行业名"
                                placeholder="请输入行业名"
                                {...field}
                                error={errors.industry_name?.message}
                            />
                        )}
                    />

                    <Controller
                        control={control}
                        name="market_code"
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="行业编码"
                                placeholder="请输入行业编码(只能输入大写字母)"
                                {...field}
                                error={errors.market_code?.message}
                            />
                        )}
                    />



                    <ModalFooter
                        buttons={[
                            {
                                key: "save",
                                label: account?.id ? "更新" : "保存",
                                style: "outline",
                                type: "submit",
                                loading: loading,
                                leftSection: <Check size={16} />,
                            },
                            {
                                key: "cancel",
                                label: "取消",
                                leftSection: <X size={16} />,
                                onClick: closeModal,
                            },
                        ]}
                    />
                </Stack>
            </form>
        </Modal>
    );
};

export default Create;
