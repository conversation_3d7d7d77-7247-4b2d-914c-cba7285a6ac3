import { Stack, Text } from "@mantine/core";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

const CompanyApplicationComplete = () => {
    const lang = useSettingStore.use.lang();

    return (
        <Stack align="center" className="tw-mt-5">
            <img
                src={`/images/icons/icon-alert-success.svg?k=${new Date().getTime()}`}
                className="tw-w-[80px] tw-mx-auto"
            />

            <Text>{t("project.edit.step5.form.title", lang)}</Text>
        </Stack>
    );
};

export default CompanyApplicationComplete;
