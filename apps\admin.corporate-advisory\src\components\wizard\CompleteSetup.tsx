import { useEventBus } from "@/utils/eventBus";
import { Stack, Text, Title } from "@mantine/core";
import { CheckCircle } from "@phosphor-icons/react";
import { useMount, useUnmount } from "ahooks";
import { useNavigate } from "react-router-dom";

const CompleteSetup = () => {
    const navigate = useNavigate();

    const bus = useEventBus();

    useMount(() => {
        bus.on("wizard.submit.click", () => {
            navigate("/admin/desk-associates");
            // navigate("/admin/review");
        });
    });

    useUnmount(() => {
        bus.off("wizard.submit.click");
    });

    return (
        <Stack>
            <CheckCircle
                size={120}
                weight="light"
                className="tw-text-green-600 tw-mx-auto"
            />
            <Title order={2} ta="center" className="tw-text-zinc-800">
                设置已完成
            </Title>
            <Text ta="center" className="tw-text-zinc-500">
                立即开始使用合伙人行政后台
            </Text>
        </Stack>
    );
};

export default CompleteSetup;
