import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import FirmInfo from "@/components/modals/firm/Info";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import noty from "@code.8cent/react/noty";

const columnHelper = createColumnHelper<TFirm>();

const FIRM_STATUS_MAP = {
    1: "申请中",
    2: "通过",
    3: "拒绝",
};

const AdminFirmPage = () => {
    const { lang } = useSettingStore();
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("company_name", {
            header: "企业名字",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("credit_code", {
            header: "企业信用代码",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileName", {
            header: "申请人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileEmail", {
            header: "申请人邮箱",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("status", {
            header: "状态",
            enableSorting: false,
            cell: (info) => FIRM_STATUS_MAP[info.getValue()] || "未知",
        }),
        columnHelper.accessor("created_at", {
            header: "创建日期",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("updated_at", {
            header: "更新日期",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        }),
    ];

    const handleReview = useMemoizedFn(async (id: number, status: number) => {
        try {
            setLoading(true);
            const res = await api.firm.review({ id, status });
            if (res) {
                noty.success("操作成功");
                refreshData();
            } else {
                noty.error("操作失败");
            }
        } catch (error) {
            noty.error("操作失败");
        } finally {
            setLoading(false);
        }
    });

    const rowActions = (row) => [
        {
            key: "profile",
            label: "查看详情",
            onClick: () => {
                openModal("firmInfoModal", { firmInfo: row });
            },
        },
        ...(row.status == 1
            ? [
                  {
                      key: "review",
                      label: "通过申请",
                      onClick: () => {
                          openConfirm({
                              title: "通过申请",
                              message: "确定通过此联号事务所申请吗？",
                              onConfirm: () => handleReview(row.id, 2),
                          });
                      },
                  },
                  {
                      key: "review-reject",
                      label: "拒绝申请",
                      onClick: () => {
                          openConfirm({
                              title: "拒绝申请",
                              message: "确定拒绝此联号事务所申请吗？",
                              onConfirm: () => handleReview(row.id, 3),
                          });
                      },
                  },
              ]
            : []),
    ];

    const handleFetch = useMemoizedFn(async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.firm.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    });

    const refreshData = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="联号事务所"
                desc="查询联号事务所申请信息"
            />

            <DataTable
                ref={tableRef}
                columns={tableColumns as any}
                data={data}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
            />

            {/* 事务所申请信息详情 */}
            <FirmInfo onUpdateSuccess={refreshData} />
        </Stack>
    );
};

export default AdminFirmPage;
