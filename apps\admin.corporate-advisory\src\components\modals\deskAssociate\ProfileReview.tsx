import deskApi from "@/apis/desk";
import useModalStore from "@/store/modal";
import { useMemoizedFn } from "ahooks";
import React, { useState } from "react";
import { Group, Modal, Textarea } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { SubmitHandler, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import CnaButton from "@code.8cent/react/components/CnaButton";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import noty from "@code.8cent/react/noty";

type ReviewFormInput = {
    review_content: string;
};

interface ProfileReviewProps {
    onSubmitSuccess: () => void;
}

const reviewSchema = z.object({
    review_content: z.string().min(1, "请填写审核意见"),
});

const initalValues = {
    review_content: "",
};

// 审核用户资料弹窗
const ProfileReview = ({ onSubmitSuccess }: ProfileReviewProps) => {
    const lang = useSettingStore.use.lang();

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.modalParams.associateReviewModal,
            close: state.close,
        }))
    );

    const modalParams = useModalStore((state) => state.modalParams.associateReviewModal) || {};
    const { profileId } = modalParams;

    const closeModal = useMemoizedFn(() => {
        close("associateReviewModal");
        reset(initalValues);
    });

    const [updating, setUpdating] = useState(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
    } = useForm<ReviewFormInput>({
        defaultValues: initalValues,
        resolver: zodResolver(reviewSchema),
        mode: "all",
    });

    const onSubmit: SubmitHandler<ReviewFormInput> = async (data) => {
        setUpdating(true);

        try {
            const updateRes = await Promise.all([
                // 更新审核状态
                deskApi.associate.review({
                    status: 2, // 驳回
                    id: profileId,
                }),
                // 更新备注
                deskApi.remark.store({
                    remarkType: 1,
                    objID: profileId,
                    remarkContent: data.review_content,
                }),
            ]);

            if (updateRes) {
                noty.success("操作成功");
                closeModal();
                onSubmitSuccess();
            }
        } catch (error) {
            console.error("Error submitting review:", error);
            noty.error("操作失败");
        } finally {
            setUpdating(false);
            reset(initalValues);
        }
    };

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title="驳回备注信息"
            zIndex={360}
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Textarea
                    required
                    className="tw-mb-6"
                    label="驳回意见"
                    placeholder="请输入审核意见"
                    {...register("review_content")}
                    error={errors.review_content ? true : false}
                />

                <Group className="tw-justify-end">
                    <CnaButton
                        variant="outline"
                        color="basic"
                        onClick={closeModal}
                    >
                        关闭
                    </CnaButton>
                    <CnaButton
                        color="basic"
                        type="submit"
                        className="btn btn-primary"
                        loading={updating}
                    >
                        提交
                    </CnaButton>
                </Group>
            </form>
        </Modal>
    );
};

export default ProfileReview;
