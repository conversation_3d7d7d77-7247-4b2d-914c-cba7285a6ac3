import { useEventBus } from "@/utils/eventBus";
import { Stack, Text, Title } from "@mantine/core";
import { CheckCircle } from "@phosphor-icons/react";
import { useMemoizedFn, useMount, useUnmount } from "ahooks";
import { useNavigate } from "react-router-dom";

const CompleteSetup = () => {
    const navigate = useNavigate();

    const bus = useEventBus();

    const toProfile = useMemoizedFn(() => {
        navigate("/member/profile", { replace: true });
    });

    useMount(() => {
        bus.on("wizard.submit.click", toProfile);
    });

    useUnmount(() => {
        bus.off("wizard.submit.click", toProfile);
    });

    return (
        <Stack>
            <CheckCircle
                size={120}
                weight="light"
                className="tw-text-green-600 tw-mx-auto"
            />
            <Title order={2} ta="center" className="tw-text-zinc-800">
                设置已完成
            </Title>
            <Text ta="center" className="tw-text-zinc-500">
                欢迎使用合伙人AI办公室
            </Text>
        </Stack>
    );
};

export default CompleteSetup;
