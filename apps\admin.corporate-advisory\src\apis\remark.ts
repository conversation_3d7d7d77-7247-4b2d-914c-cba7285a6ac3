import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const remark = {
    list: async (params: TRemarkSearchParams) => {
        const { error, result } = await cnaRequest<TRemarksResponse>(
            "/api/v1/admin/remark/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    store: async (params: TStoreRemarkParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/remark/create",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    update: async (params: TUpdateRemarkParams, id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/remark/edit/${id}`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    delete: async (id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/remark/delete/${id}`,
            "DELETE"
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default remark;
