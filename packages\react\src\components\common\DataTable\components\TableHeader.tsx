import { Table, Group, Checkbox } from "@mantine/core";
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import { flexRender } from "@tanstack/react-table";
import { CaretUp, CaretDown, CaretUpDown } from "@phosphor-icons/react";
import { ColumnFilters } from "./ColumnFilters";

export function TableHeader({
    table,
    enableMultiSelect,
    filterTypes,
    filterOptions,
    localColumnFilters,
    setLocalColumnFilters,
    handleDragEnd,
}) {
    return (
        <DragDropContext onDragEnd={handleDragEnd}>
            <Table.Thead>
                {table.getHeaderGroups().map((headerGroup) => (
                    <Droppable
                        droppableId={headerGroup.id}
                        direction="horizontal"
                        key={headerGroup.id}
                    >
                        {(provided) => (
                            <Table.Tr
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                            >
                                {enableMultiSelect && (
                                    <Table.Th>
                                        <Checkbox
                                            checked={table.getIsAllRowsSelected()}
                                            indeterminate={table.getIsSomeRowsSelected()}
                                            onChange={table.getToggleAllRowsSelectedHandler()}
                                        />
                                    </Table.Th>
                                )}
                                {headerGroup.headers.map((header, index) => (
                                    <Draggable
                                        key={header.id}
                                        draggableId={header.id}
                                        index={index}
                                    >
                                        {(provided) => (
                                            <Table.Th
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                                onClick={
                                                    header.column.getCanSort()
                                                        ? header.column.getToggleSortingHandler()
                                                        : undefined
                                                }
                                                style={{
                                                    cursor: header.column.getCanSort()
                                                        ? "pointer"
                                                        : "move",
                                                    ...provided.draggableProps.style,
                                                    minWidth:
                                                        filterTypes[header.column.id] ===
                                                        "datetimeRange"
                                                            ? 240
                                                            : header.column.getSize(),
                                                }}
                                            >
                                                <Group gap={2}>
                                                    {flexRender(
                                                        header.column.columnDef.header,
                                                        header.getContext()
                                                    )}

                                                    {/* 排序指示器 */}
                                                    {header.column.getCanSort() &&
                                                        ({
                                                            asc: <CaretUp weight="fill" />,
                                                            desc: <CaretDown weight="fill" />,
                                                        }[
                                                            header.column.getIsSorted() as string
                                                        ] ?? <CaretUpDown weight="fill" />)}
                                                </Group>
                                            </Table.Th>
                                        )}
                                    </Draggable>
                                ))}
                                {provided.placeholder}
                            </Table.Tr>
                        )}
                    </Droppable>
                ))}

                {/* 筛选行 */}
                {Object.keys(filterTypes).length > 0 && (
                    <ColumnFilters
                        table={table}
                        filterTypes={filterTypes}
                        filterOptions={filterOptions}
                        localColumnFilters={localColumnFilters}
                        setLocalColumnFilters={setLocalColumnFilters}
                        enableMultiSelect={enableMultiSelect}
                    />
                )}
            </Table.Thead>
        </DragDropContext>
    );
}
