import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const newFirm = {
    getProcessList: async (params: { page: number; per_page: number }) => {
        const { error, result } = await cnaRequest<{ data: TFirmProcess[], last_page: number, total: number }>(
            "/api/v1/admin/affiliatedFirm/get_process_list",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    getProcessDetail: async (id: number) => {
        const { error, result } = await cnaRequest<TFirmProcess>(
            `/api/v1/admin/affiliatedFirm/process/${id}`,
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 通过流程审核
    passProcess: async (id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/affiliatedFirm/process/approve/${id}`,
            "POST"
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 不通过流程审核
    rejectProcess: async (id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/affiliatedFirm/process/reject/${id}`,
            "POST"
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default newFirm;
