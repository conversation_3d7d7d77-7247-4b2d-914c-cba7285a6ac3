declare global {
    type BaseOperateResponse = {
        createUser: number;
        createRole: number;
        createTime: string;
        editUser: number;
        editRole: number;
        editTime: string;
    };

    type BasePaginateResponse = {
        currentPage: number;
        perPage: number;
        totalRecord: number;
        totalPage: number;
        total_page: number;
        total?: number;
    };

    type BaseApiResponse<T = any> = {
        code: number;
        data: T;
        message: string;
        status: boolean;
    };

    type ActivityLogResponse = {
        list: ({
            id: number;
            active_type: number;
            create_time: number;
            created_at?: string;
        } & Record<`desc${LangCode}`, string>)[];
        paginate: BasePaginateResponse;
    };

    type LoginLogResponse = {
        list: {
            id: number;
            token_status: number;
            device: string;
            ip: string;
            current: number;
            create_time: number;
            created_at?: string;
        }[];
        paginate: BasePaginateResponse;
    };
}

export {};
