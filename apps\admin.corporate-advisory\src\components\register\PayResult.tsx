import { Image, Stack, Text, Title } from "@mantine/core";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";

const PayResult = () => {
    const lang = useSettingStore.use.lang();

    return (
        <Stack align="center" gap={"xl"} className="tw-pb-16">
            <Image w={100} h={100} src={`/images/icons/icon-alert-success.svg?t=${new Date().getTime()}`} />

            <Title order={2}>{t("payment.result", lang)}</Title>

            <Text>{t("payment.result.details", lang)}</Text>
        </Stack>
    );
};

export default PayResult;
