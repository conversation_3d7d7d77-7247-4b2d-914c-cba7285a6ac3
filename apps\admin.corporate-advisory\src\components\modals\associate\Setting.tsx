import useModalStore from "@/store/modal";
import { useMemoizedFn, useRequest } from "ahooks";
import React, { useState } from "react";
import { Group, Modal, Stack, UnstyledButton, Text } from "@mantine/core";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { CaretRight, X } from "@phosphor-icons/react";
import ModalFooter from "@/components/common/ModalFooter";
import { useShallow } from "zustand/react/shallow";
import api from "@/apis";

const Setting = () => {
    const lang = useSettingStore.use.lang();
    const openModal = useModalStore.use.open();

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.settingModal,
            close: state.close,
        }))
    );

    // 邮件模版列表
    const { data: emailTemplates = [], loading } = useRequest(
        async () => {
            const response = await api.emailTemplate.list();

            return response?.items.map((item) => ({
                ...item,
                templateId: item.id,
                key: item.eventCode,
                title: item.remark,
            }));
        },
        {
            refreshDeps: [isVisible], // 当 isVisible 变化时重新请求
            ready: isVisible, // 只在模态框可见时发起请求
        }
    );

    const handleClose = useMemoizedFn(() => {
        close("settingModal");
    });

    const handleTemplateClick = useMemoizedFn((template) => {
        handleClose();
        openModal("mailSettingModal", { template });
    });

    return (
        <Modal
            opened={isVisible}
            onClose={handleClose}
            title="电邮模版"
            size="lg"
        >
            <Stack
                gap="sm"
                p="md"
            >
                {emailTemplates.map((template) => (
                    <UnstyledButton
                        key={template.key}
                        className="tw-block tw-w-full tw-p-4 tw-border tw-border-solid tw-border-gray-300 tw-rounded-sm hover:tw-bg-gray-200"
                        onClick={() => handleTemplateClick(template)}
                    >
                        <Group
                            justify="space-between"
                            align="center"
                        >
                            <Text>{template.title}</Text>
                            <CaretRight size={18} />
                        </Group>
                    </UnstyledButton>
                ))}
            </Stack>

            <ModalFooter
                buttons={[
                    {
                        key: "close",
                        label: "关闭",
                        style: "outline",
                        leftSection: <X size={18} />,
                        onClick: handleClose,
                    },
                ]}
            />
        </Modal>
    );
};

export default Setting;
