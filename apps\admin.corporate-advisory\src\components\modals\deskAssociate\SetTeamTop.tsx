import { Group, Modal, Select, Stack, TextInput } from "@mantine/core";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useShallow } from "zustand/react/shallow";
import useModalStore from "@/store/modal";
import { useMemoizedFn, useRequest } from "ahooks";
import { SubmitHandler, useForm } from "react-hook-form";
import noty from "@code.8cent/react/noty";
import deskApi from "@/apis/desk";
import { CnaButton } from "@code.8cent/react/components";
import { useState } from "react";
import api from "@/apis";

const setTeamTopSchema = z.object({
    industry_name: z.string().min(1, "请输入行业名"),
    market_code: z.string().min(1, "请输入市场标识符"),
});

const initalValues = {
    industry_name: "",
    market_code: "",
};

type SetTeamTopFormInput = z.infer<typeof setTeamTopSchema>;

const SetTeamTop = () => {
    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        setValue
    } = useForm<SetTeamTopFormInput>({
        defaultValues: initalValues,
        resolver: zodResolver(setTeamTopSchema),
    });

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.modalParams.setTeamTopModal,
            close: state.close,
        }))
    );

    const modalParams = useModalStore((state) => state.modalParams.setTeamTopModal) || {};
    const { row } = modalParams;

    const [loading, setLoading] = useState(false);

    const openConfirm = useModalStore.use.openConfirm();

    const closeModal = useMemoizedFn(() => {
        reset(initalValues);
        close("setTeamTopModal");
    });

    const setTeamTop = useMemoizedFn((profileID: number, data: SetTeamTopFormInput) => {
        openConfirm({
            title: "提示",
            message: "您确定设置该合伙人作为三三制总吗？",
            onConfirm: async () => {
                setLoading(true);
                try {
                    const res = await deskApi.associate.setTeamLeader(
                        profileID,
                        data.industry_name,
                        data.market_code
                    );

                    if (res) {
                        noty.success("设置成功");
                        closeModal();
                    }
                } catch (error) {
                    console.error(error);
                } finally {
                    setLoading(false);
                }
            },
        });
    });

    const onSubmit: SubmitHandler<SetTeamTopFormInput> = async (data) => {
        setTeamTop(row?.profileID, data);
    };

    const { data: fieldTypeList = [] } = useRequest(api.fieldType.list, {
        defaultParams: [{
            getType: 'chooseList'
        }],
    });
    console.log('fieldTypeList:', fieldTypeList)


    const handleChangeIndustryType = (value: string, opt: any) => {
        console.log('value:', value)
        console.log('opt:', opt)
        setValue('industry_name', opt.value)
        setValue('market_code', opt.code)
        // const [industry_name, market_code] = value.split('-');
        // setValue('industry_name', industry_name);
        // setValue('market_code', market_code);
    }

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title={`设置 ${row?.profileName} 为三三制总`}
        >
            <Stack>
                <form
                    onSubmit={handleSubmit(onSubmit, (errors) => {
                        console.log(errors);
                    })}
                >


                    {/* todo:需要添加数据录入操作并根据业务需求决定显示（行业类型） */}
                    <Select
                        label="行业类型"
                        placeholder="请选择行业类型"
                        withAsterisk
                        data={fieldTypeList?.map(x => ({
                            value: `${x.name}`,
                            code: `${x.market_code}`,
                            label: `${x.name}-${x.market_code}`
                        }))}
                        onChange={(e, opt) => handleChangeIndustryType(e, opt)}
                    // {...register("industry_id")}
                    // error={errors.industry_id?.message}
                    />


                    <TextInput
                        label="行业"
                        placeholder="请输入行业名"
                        withAsterisk
                        disabled
                        {...register("industry_name")}
                        error={errors.industry_name?.message}
                        />

                    <TextInput
                        label="市场标识符"
                        placeholder="请输入市场标识符"
                        withAsterisk
                        disabled
                        {...register("market_code")}
                        error={errors.market_code?.message}
                    />



                    <Group
                        className="tw-justify-end"
                        mt="md"
                    >
                        <CnaButton
                            variant="outline"
                            color="basic"
                            onClick={closeModal}
                        >
                            关闭
                        </CnaButton>
                        <CnaButton
                            color="basic"
                            type="submit"
                            className="btn btn-primary"
                            loading={loading}
                        >
                            提交
                        </CnaButton>
                    </Group>
                </form>
            </Stack>
        </Modal>
    );
};

export default SetTeamTop;
