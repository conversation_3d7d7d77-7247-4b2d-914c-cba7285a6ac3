declare global {
    type TWebStructureSearchParams = {
        keyword?: string | null;
    } & TPageQueryParams;

    type TWebStructure = {
        structureID: number;
        structureCode: string;
        structureEN: string;
        structureMS: string;
        structureZH: string;
        structureZT: string;
    }

    type TWebStructuresResponse = {
        items: TWebStructure[];
        paginate: BasePaginateResponse;
    };
}

export {};
