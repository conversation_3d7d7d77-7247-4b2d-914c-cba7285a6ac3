import { Stack } from "@mantine/core";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import dayjs from "dayjs";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import gsp from "@/apis/gsp";
import useModalStore from "@/store/modal";
import CnaAdminButton from "@/components/common/CnaAdminButton";
import RefundExamine from "@/components/modals/gsp/RefundExamine";

const columnHelper = createColumnHelper<TGspRefund>();

const AdminRefundPage = () => {
    const lang = useSettingStore.use.lang();

    const tableRef = useRef<DataTableRef | null>(null);
    const openModal = useModalStore.use.open();
    const [data, setData] = useState<TGspRefund[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    // 获取状态显示文本
    const getStatusText = (status: number) => {
        switch (status) {
            case 0:
                return "已取消";
            case 1:
                return "申请中";
            case 2:
                return "审核通过";
            case 3:
                return "审核不通过";
            default:
                return "未知状态";
        }
    };

    // 获取状态颜色
    const getStatusColor = (status: number) => {
        switch (status) {
            case 0:
                return "gray";
            case 1:
                return "blue";
            case 2:
                return "green";
            case 3:
                return "red";
            default:
                return "gray";
        }
    };

    // 获取退款状态显示文本
    const getRefundStatusText = (refundStatus: number) => {
        switch (refundStatus) {
            case 0:
                return "未退款";
            case 1:
                return "已退款";
            case 2:
                return "退款失败";
            default:
                return "未知状态";
        }
    };

    // 获取退款状态颜色
    const getRefundStatusColor = (refundStatus: number) => {
        switch (refundStatus) {
            case 0:
                return "gray";
            case 1:
                return "green";
            case 2:
                return "red";
            default:
                return "gray";
        }
    };

    const handleExamine = (refundData: TGspRefund) => {
        openModal("gspRefundExamineModal", { refundData });
    };

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("company_name", {
            header: "公司名称",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("refund_price", {
            header: "退款金额",
            enableSorting: false,
            cell: (info) => `¥${info.getValue()}`,
        }),
        columnHelper.accessor("status", {
            header: "状态",
            enableSorting: false,
            cell: (info) => (
                <span className={`tw-text-${getStatusColor(info.getValue())}-600`}>
                    {getStatusText(info.getValue())}
                </span>
            ),
        }),
        columnHelper.accessor("refund_status", {
            header: "退款状态",
            enableSorting: false,
            cell: (info) => (
                <span className={`tw-text-${getRefundStatusColor(info.getValue())}-600`}>
                    {getRefundStatusText(info.getValue())}
                </span>
            ),
        }),
        columnHelper.accessor("remark", {
            header: "申请备注",
            enableSorting: false,
            cell: (info) => info.getValue() || "-",
        }),
        columnHelper.accessor("review_comments", {
            header: "审核备注",
            enableSorting: false,
            cell: (info) => info.getValue() || "-",
        }),
        columnHelper.accessor("created_at", {
            header: "创建时间",
            enableSorting: false,
            cell: (info) =>
                info.getValue() && dayjs(info.getValue() as string).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.accessor("updated_at", {
            header: "更新时间",
            enableSorting: false,
            cell: (info) =>
                info.getValue() && dayjs(info.getValue() as string).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                const row = info.row.original;
                return (
                    <div className="tw-flex tw-gap-2">
                        {row.status === 1 && (
                            <CnaAdminButton
                                size="xs"
                                variant="outline"
                                onClick={() => handleExamine(row)}
                            >
                                审核
                            </CnaAdminButton>
                        )}
                    </div>
                );
            },
        }),
    ];

    const handleFetch = async (params: any) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams: TGspRefundSearchParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const response = await gsp.refundList(requestParams);
            if (response) {
                setData(response.items);
                setTotalCount(response.paginate.total);
            }
        } catch (error) {
            console.error("获取退款列表失败:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleExamineSuccess = () => {
        // 刷新表格数据
        tableRef.current?.refresh();
    };

    const handleExport = async () => {
        console.log("export");
    };

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="绿智地球申请退款管理"
                desc="查询和管理绿智地球申请退款"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={handleExport}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键词",
                        type: "text",
                    },
                ]}
            />

            <RefundExamine onSubmitSuccess={handleExamineSuccess} />
        </Stack>
    );
};

export default AdminRefundPage;
