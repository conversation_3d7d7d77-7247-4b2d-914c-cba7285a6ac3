import api from "@/apis";
import { t } from "@code.8cent/i18n";
import {
    Group,
    Image,
    Input,
    Modal,
    Stack,
    Tabs,
    Text,
    Box,
    ActionIcon,
    LoadingOverlay,
    Popover,
} from "@mantine/core";
import useModalStore from "@/store/modal";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn, useRequest } from "ahooks";
import {
    Check,
    Eye,
    DownloadSimple,
    FilePdf,
    FileDoc,
    File as FileIcon,
    Info,
} from "@phosphor-icons/react";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import noty from "@code.8cent/react/noty";
import { downloadFile } from "@/utils/files";
import { useEffect, useState, useMemo, memo } from "react";
import CnaAdminButton from "@/components/common/CnaAdminButton";

// 文件类型和标签配置
type FileItem = {
    id?: string;
    file_id?: string;
    file_path: string;
    file_owner: string;
    created_at: string | null;
    status?: string;
};

type TabItemProps = {
    title: string;
    withAsterisk: boolean;
    files: Record<string, any> | undefined;
    fileKey: string;
    gspId: string;
    onRefresh: () => void;
};

type TabConfig = {
    value: string;
    label: string;
    fileKeys: string[];
};

// 标签配置
const TAB_CONFIGS: TabConfig[] = [
    { value: "基础信息", label: "基础信息", fileKeys: ["file1", "file2", "file3", "file4"] },
    { value: "财务信息", label: "财务信息", fileKeys: ["file5", "file6", "file7", "file8"] },
    { value: "法律信息", label: "法律信息", fileKeys: ["file9", "file10", "file11", "file12"] },
    { value: "业务信息", label: "业务信息", fileKeys: ["file13", "file14", "file15"] },
    { value: "人力资源", label: "人力资源", fileKeys: ["file16", "file17", "file18"] },
    { value: "资产信息", label: "资产信息", fileKeys: ["file19", "file20", "file21"] },
    { value: "环境与社会责任", label: "环境与社会责任", fileKeys: ["file22", "file23"] },
];

// 标签内容配置
const TAB_ITEMS_CONFIG = {
    基础信息: [
        { title: "营业执照", withAsterisk: true, fileKey: "file1" },
        { title: "公司章程", withAsterisk: true, fileKey: "file2" },
        { title: "股东结构、组织架构", withAsterisk: true, fileKey: "file3" },
        { title: "主要管理人员简历", withAsterisk: true, fileKey: "file4" },
    ],
    财务信息: [
        { title: "财务报表", withAsterisk: true, fileKey: "file5" },
        { title: "审计报告", withAsterisk: false, fileKey: "file6" },
        { title: "税务记录、银行对账", withAsterisk: true, fileKey: "file7" },
        { title: "财务预测、预算", withAsterisk: true, fileKey: "file8" },
    ],
    法律信息: [
        { title: "公司注册文件", withAsterisk: true, fileKey: "file9" },
        { title: "重大合同", withAsterisk: true, fileKey: "file10" },
        { title: "诉讼仲裁记录", withAsterisk: true, fileKey: "file11" },
        { title: "知识产权", withAsterisk: true, fileKey: "file12" },
    ],
    业务信息: [
        { title: "产品或服务介绍", withAsterisk: true, fileKey: "file13" },
        { title: "主要客户和供应商名单", withAsterisk: true, fileKey: "file14" },
        { title: "销售渠道、市场份额", withAsterisk: true, fileKey: "file15" },
    ],
    人力资源: [
        { title: "员工数量、结构、薪酬福利", withAsterisk: true, fileKey: "file16" },
        { title: "劳动合同、保密协议", withAsterisk: true, fileKey: "file17" },
        { title: "员工手册、培训计划", withAsterisk: true, fileKey: "file18" },
    ],
    资产信息: [
        { title: "固定资产清单", withAsterisk: true, fileKey: "file19" },
        { title: "无形资产", withAsterisk: true, fileKey: "file20" },
        { title: "资产抵押、担保情况", withAsterisk: true, fileKey: "file21" },
    ],
    环境与社会责任: [
        { title: "环保合规记录", withAsterisk: true, fileKey: "file22" },
        { title: "社会责任报告", withAsterisk: true, fileKey: "file23" },
    ],
};

// 标签右上角数字
const TabRightSection = memo(({ value }: { value: string }) => {
    return (
        <Text
            size="sm"
            px="8"
            className="tw-rounded tw-bg-red-100 tw-text-red-500"
        >
            {value}
        </Text>
    );
});
TabRightSection.displayName = "TabRightSection";

// 标签图片组件，预览、下载
const TabItemImage = memo(({ file_path, file_owner, created_at }: FileItem) => {
    const { openFileView } = useFileViewer();
    const openConfirm = useModalStore.use.openConfirm();

    // 处理文件下载
    const handleDownload = useMemoizedFn(async () => {
        try {
            await downloadFile(file_path, "local");
        } catch (error) {
            console.error("下载失败:", error);
            noty.error("下载失败，请稍后重试");
        }
    });

    // 确认下载
    const confirmDownload = useMemoizedFn(() => {
        openConfirm({
            title: "确定下载此文件吗？",
            onConfirm: handleDownload,
        });
    });

    const previewFile = useMemoizedFn((path: string, type: "local" | "oss") => {
        openFileView(`${window.api_base_url}/api/v1/admin/firm/files?path=${path}&type=${type}`, {
            title: "查看文件",
        });
    });

    // 格式化时间
    const formatDate = (dateStr: string | null) => {
        if (!dateStr) return "暂无";
        return new Date(dateStr).toLocaleString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    // 获取文件扩展名
    const getFileExtension = (filePath: string): string => {
        return filePath.split(".").pop()?.toLowerCase() || "";
    };

    // 获取文件类型信息
    const fileTypeInfo = useMemo(() => {
        const extension = getFileExtension(file_path);

        // 图片类型
        if (["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension)) {
            return {
                type: "image",
                icon: null,
                color: "",
                label: "",
            };
        }

        // PDF文件
        if (extension === "pdf") {
            return {
                type: "pdf",
                icon: (
                    <FilePdf
                        size={48}
                        weight="fill"
                    />
                ),
                color: "#e74c3c",
                label: "PDF",
            };
        }

        // Word文档
        if (["doc", "docx"].includes(extension)) {
            return {
                type: "word",
                icon: (
                    <FileDoc
                        size={48}
                        weight="fill"
                    />
                ),
                color: "#4285f4",
                label: "Word",
            };
        }

        // 其他文件类型
        return {
            type: "other",
            icon: (
                <FileIcon
                    size={48}
                    weight="fill"
                />
            ),
            color: "#95a5a6",
            label: "文件",
        };
    }, [file_path]);

    return (
        <Box className="tw-relative tw-group">
            {fileTypeInfo.type !== "image" ? (
                <Box
                    className="tw-flex tw-items-center tw-justify-center tw-bg-gray-100 tw-cursor-pointer"
                    h={100}
                    w={100}
                    onClick={() => previewFile(file_path, "local")}
                >
                    <div style={{ color: fileTypeInfo.color }}>{fileTypeInfo.icon}</div>
                    <Text
                        size="xs"
                        className="tw-absolute tw-bottom-1 tw-text-center"
                    >
                        {fileTypeInfo.label}
                    </Text>
                </Box>
            ) : (
                <Image
                    h={100}
                    w={100}
                    src={`${window.api_base_url}/storage/${file_path}`}
                    className="tw-cursor-pointer"
                    onClick={() => previewFile(file_path, "local")}
                />
            )}
            <Box className="tw-absolute tw-inset-0 tw-bg-black/30 tw-opacity-0 group-hover:tw-opacity-100 tw-transition-opacity tw-flex tw-items-center tw-justify-center">
                <Group justify="center">
                    <Popover
                        withArrow
                        shadow="md"
                        position="top"
                        closeOnClickOutside
                        withinPortal
                    >
                        <Popover.Target>
                            <ActionIcon
                                variant="filled"
                                color="dark"
                                title="详情"
                            >
                                <Info size={20} />
                            </ActionIcon>
                        </Popover.Target>
                        <Popover.Dropdown>
                            <Stack>
                                <Group justify="space-between">
                                    <Text size="sm">上传角色：</Text>
                                    <Text size="sm">
                                        {file_owner == "1"
                                            ? "合伙人"
                                            : file_owner == "2"
                                            ? "企业用户"
                                            : "其他"}
                                    </Text>
                                </Group>
                                <Group justify="space-between">
                                    <Text size="sm">上传时间：</Text>
                                    <Text size="sm">{formatDate(created_at)}</Text>
                                </Group>
                                <Group justify="space-between">
                                    <Text size="sm">文件类型：</Text>
                                    <Text size="sm">{fileTypeInfo.label || "未知"}</Text>
                                </Group>
                            </Stack>
                        </Popover.Dropdown>
                    </Popover>
                    <ActionIcon
                        variant="filled"
                        color="dark"
                        onClick={() => previewFile(file_path, "local")}
                        title="预览"
                    >
                        <Eye size={20} />
                    </ActionIcon>
                    <ActionIcon
                        variant="filled"
                        color="dark"
                        onClick={confirmDownload}
                        title="下载"
                    >
                        <DownloadSimple size={20} />
                    </ActionIcon>
                </Group>
            </Box>
        </Box>
    );
});
TabItemImage.displayName = "TabItemImage";

// 标签组件，显示文件列表
const TabItem = memo(({ title, withAsterisk, files, fileKey, gspId, onRefresh }: TabItemProps) => {
    const openConfirm = useModalStore.use.openConfirm();

    const handleReview = useMemoizedFn(() => {
        openConfirm({
            title: "确定审核",
            message: `确定审核通过 ${title} 文件吗？`,
            onConfirm: () => confirmReview(),
        });
    });

    const confirmReview = useMemoizedFn(async () => {
        try {
            const res = await api.gsp.checkReportFile({
                id: gspId,
                status: 1,
                file_id: files["file_id"],
            });
            if (res) {
                noty.success("审核通过");
                onRefresh();
            } else {
                noty.error("审核失败");
            }
        } catch (error) {
            noty.error("审核失败");
        }
    });

    return (
        <Group
            justify="space-between"
            p="xs"
            className="tw-rounded tw-bg-gray-100"
        >
            <Input.Wrapper
                label={title}
                withAsterisk={withAsterisk}
            ></Input.Wrapper>
            <Group>
                {files &&
                    files["file"] &&
                    files["file"].map((file: FileItem) => (
                        <TabItemImage
                            key={file.file_path}
                            file_path={file.file_path}
                            file_owner={file.file_owner}
                            created_at={file.created_at}
                        />
                    ))}
                {files && files["status"] === 0 && (
                    <CnaAdminButton
                        size="xs"
                        onClick={handleReview}
                    >
                        审核通过
                    </CnaAdminButton>
                )}
            </Group>
        </Group>
    );
});
TabItem.displayName = "TabItem";

const ReportFiles = () => {
    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.gspReportFilesModal,
            close: state.close,
        }))
    );

    const modalParams = useModalStore((state) => state.modalParams.gspReportFilesModal);
    const gspApplication = modalParams?.gspApplication;

    const closeModal = useMemoizedFn(() => {
        close("gspReportFilesModal");
    });

    const [files, setFiles] = useState<Record<string, any>>({});
    const [applicationId, setApplicationId] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // 获取报告文件
    const { run: getReportFiles } = useRequest(
        async (id: string) => {
            if (!id) return;
            setIsLoading(true);
            try {
                const res = await api.gsp.getReportFiles(id);
                if (res) {
                    setFiles(res);
                }
                return res;
            } catch (error) {
                console.error("获取报告文件失败:", error);
                noty.error("获取报告文件失败，请稍后重试");
            } finally {
                setIsLoading(false);
            }
        },
        {
            manual: true,
        }
    );

    // 当模态框可见且有应用ID时获取报告文件
    useEffect(() => {
        if (isVisible && gspApplication?.id) {
            setApplicationId(gspApplication.id);
            getReportFiles(gspApplication.id);
        }
    }, [isVisible, gspApplication]);

    // 获取标签状态
    const getTabStatus = useMemoizedFn((fileNames: string[]) => {
        let count = 0;
        for (const fileName of fileNames) {
            if (files[fileName] && files[fileName]["status"] === 0) {
                count++;
            }
        }

        if (count === 0) {
            return (
                <Check
                    color="green"
                    weight="bold"
                />
            );
        } else {
            return <TabRightSection value={count.toString()} />;
        }
    });

    // 渲染标签项
    const renderTabItems = (tabValue: string) => {
        const items = TAB_ITEMS_CONFIG[tabValue as keyof typeof TAB_ITEMS_CONFIG] || [];

        return (
            <Stack mt="md">
                {items.map((item) => (
                    <TabItem
                        key={item.fileKey}
                        title={item.title}
                        withAsterisk={item.withAsterisk}
                        files={files[item.fileKey]}
                        fileKey={item.fileKey}
                        gspId={applicationId}
                        onRefresh={() => getReportFiles(applicationId)}
                    />
                ))}
            </Stack>
        );
    };

    // 渲染标签列表
    const renderTabsList = useMemo(() => {
        return TAB_CONFIGS.map((tab) => (
            <Tabs.Tab
                key={tab.value}
                value={tab.value}
                rightSection={getTabStatus(tab.fileKeys)}
            >
                {tab.label}
            </Tabs.Tab>
        ));
    }, [files]);

    // 渲染标签面板
    const renderTabsPanels = useMemo(() => {
        return TAB_CONFIGS.map((tab) => (
            <Tabs.Panel
                key={tab.value}
                value={tab.value}
            >
                {renderTabItems(tab.value)}
            </Tabs.Panel>
        ));
    }, [files, applicationId]);

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title="审核尽调报告文件"
            size="xxl"
        >
            {isLoading && (
                <LoadingOverlay
                    visible={isLoading}
                    zIndex={1000}
                />
            )}
            {!isLoading && (
                <Tabs defaultValue="基础信息">
                    <Tabs.List>{renderTabsList}</Tabs.List>
                    {renderTabsPanels}
                </Tabs>
            )}
        </Modal>
    );
};

export default ReportFiles;
