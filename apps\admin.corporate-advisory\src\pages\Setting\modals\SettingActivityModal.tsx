import { <PERSON><PERSON>, <PERSON><PERSON>, Group, Button, ScrollArea, Box } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import dayjs from "dayjs";
import { t } from "@code.8cent/i18n";
import CnaButton from "@code.8cent/react/components/CnaButton";
import useSettingStore from "@code.8cent/store/setting";
import { X } from "@phosphor-icons/react";
import { useContext, useEffect } from "react";
import SettingPageContext from "../context";
import { useMemoizedFn, useRequest, useSetState } from "ahooks";
import { cnaRequest } from "@code.8cent/utils";

const pageSize = 6;

const ActivityLogModal = () => {
    const { SettingActivityModal: show, close } = useContext(SettingPageContext);

    const [state, setState] = useSetState<{
        logs: ActivityLogResponse["list"];
        page: number;
        hasMore: boolean;
    }>({
        logs: [],
        page: 1,
        hasMore: true,
    });

    const lang = useSettingStore.use.lang();

    const { run: fetchActivityLog } = useRequest(
        async (page: number) => {
            const { result, error } = await cnaRequest<ActivityLogResponse & { items: ActivityLogResponse["list"] }>(
                "/api/v1/admin/activeLog/index",
                "GET",
                {
                    pageSize,
                    page,
                }
            );

            let list: ActivityLogResponse["list"] = [];

            if (!error) {
                list = result.data.items;
            }

            if (page === 1) {
                setState({
                    page: 1,
                    hasMore: true,
                    logs: list,
                });
            } else {
                setState((prev) => ({
                    logs: [...prev.logs, ...list],
                }));
            }

            if (list.length < pageSize) {
                setState({
                    hasMore: false,
                });
            }
        },
        {
            manual: true,
        }
    );

    const getMore = useMemoizedFn(() => {
        if (state.hasMore === true) {
            console.log("should get more");

            const page = state.page + 1;

            fetchActivityLog(page);

            setState({ page });
        }
    });

    useEffect(() => {
        if (show === true) {
            fetchActivityLog(1);
        }
    }, [show]);

    return (
        <Modal
            opened={show}
            onClose={() => close("SettingActivityModal")}
            title={t("setting.active_log", lang)} // Using dynamic lang variable
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-6 tw-h-[40vh] tw-px-3">
                <p className="tw-text-gray-500">{t("activity.log.check", lang)}</p>

                {state.logs.length === 0 && (
                    <p className="tw-text-gray-500 tw-text-center tw-my-56">
                        {t("activity.log.empty", lang)}
                    </p>
                )}

                <Box className="tw-flex-1 tw-h-0">
                    <ScrollArea
                        className="tw-w-full tw-h-full"
                        scrollbars="y"
                        onBottomReached={() => {
                            getMore();
                        }}
                    >
                        <Stack className="tw-w-full">
                            {state.logs.map((log, index) => (
                                <Group
                                    key={index}
                                    className="tw-text-sm tw-border tw-rounded-lg tw-p-3 tw-justify-between tw-items-center"
                                >
                                    <div className="tw-w-full">
                                        <div className="tw-text-gray-500 tw-mt-1">
                                            {dayjs(log.created_at).format("YYYY-MM-DD HH:mm:ss")}
                                        </div>
                                        <div className="tw-text-gray-700 tw-font-medium">
                                            {log[`desc${lang}`]}
                                        </div>
                                    </div>
                                </Group>
                            ))}
                        </Stack>
                    </ScrollArea>
                </Box>
            </Stack>
            <div className="tw-border-t tw-pt-4 tw-mt-4 tw-flex tw-justify-end tw-space-x-3">
                <CnaButton
                    color="basic"
                    variant="outline"
                    onClick={() => close("SettingActivityModal")}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </div>
        </Modal>
    );
};

export default ActivityLogModal;
