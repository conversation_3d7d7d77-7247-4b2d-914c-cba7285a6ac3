import CnaButton from "@code.8cent/react/components/CnaButton";
import { t } from "@code.8cent/i18n";
import { Group, Modal, Stack, Button, Box, ScrollArea } from "@mantine/core";
import { useMemoizedFn, useRequest, useSetState } from "ahooks";
import dayjs from "dayjs";
import bowser from "bowser";
import useSettingStore from "@code.8cent/store/setting";
import { SignOut, X } from "@phosphor-icons/react";
import { useContext, useEffect } from "react";
import SettingPageContext from "../context";
import { cnaRequest } from "@code.8cent/utils";
import noty from "@code.8cent/react/noty";

const pageSize = 6;

const LoginLogModal = () => {
    const { SettingLoginModal: show, close } = useContext(SettingPageContext);

    const lang = useSettingStore.use.lang();

    const [state, setState] = useSetState<{
        logs: LoginLogResponse["list"];
        page: number;
        hasMore: boolean;
    }>({
        logs: [],
        page: 1,
        hasMore: true,
    });

    const { run: fetchLoginLog } = useRequest(
        async (page: number) => {
            const { result, error } = await cnaRequest<LoginLogResponse & { items: LoginLogResponse["list"] }>(
                "/api/v1/admin/loginLog/index",
                "GET",
                {
                    pageSize,
                    page,
                }
            );

            let list: LoginLogResponse["list"] = [];

            if (!error) {
                list = result.data.items;
            }

            if (page === 1) {
                setState({
                    page: 1,
                    hasMore: true,
                    logs: list,
                });
            } else {
                setState((prev) => ({
                    logs: [...prev.logs, ...list],
                }));
            }

            if (list.length < pageSize) {
                setState({
                    hasMore: false,
                });
            }
        },
        {
            manual: true,
        }
    );

    const getMore = useMemoizedFn(() => {
        if (state.hasMore === true) {
            console.log("should get more");

            const page = state.page + 1;

            fetchLoginLog(page);

            setState({ page });
        }
    });

    const { run: logoutSession } = useRequest(
        async (id: number, index?: number) => {
            // /api/v1/setting/loginLog/quit

            const { result, error } = await cnaRequest<LoginLogResponse>(
                "/api/v1/admin/loginLog/quit",
                "POST",
                {
                    id,
                }
            );

            if (!error) {
                let logs = state.logs;

                if (id === 0) {
                    logs = logs.map((log) => ({ ...log, token_status: 0 }));
                } else {
                    logs[index].token_status = 0;
                }

                setState({
                    logs,
                });
                return true;
            } else {
                noty.error("登出足迹失败", error.message);
                return false;
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        if (show === true) {
            fetchLoginLog(1);
        }
    }, [show]);

    return (
        <Modal
            opened={show}
            onClose={() => close("SettingLoginModal")}
            title={t("setting.login_history.title", lang)}
            size="lg"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <Stack className="tw-m-3 tw-mb-6 tw-h-[40vh] tw-px-3">
                <p className="tw-text-gray-500 tw-mb-6">{t("system.log.history.details", lang)}</p>

                <Box className="tw-flex-1 tw-h-0">
                    <ScrollArea
                        className="tw-w-full tw-h-full"
                        scrollbars="y"
                        onBottomReached={() => {
                            getMore();
                        }}
                    >
                        <Stack className="tw-w-full">
                            {state.logs.map((log, index) => (
                                <Group
                                    key={index}
                                    className={`tw-text-sm tw-border tw-rounded-lg tw-p-3 tw-justify-between tw-items-center ${
                                        log.current ? "tw-bg-gray-100" : ""
                                    }`}
                                >
                                    <div>
                                        <div className="tw-text-gray-700 tw-font-medium">
                                            {dayjs(log.created_at).format("YYYY-MM-DD HH:mm:ss")}
                                            {log.current === 1 && (
                                                <span className="tw-font-bold tw-ml-2">
                                                    (Current)
                                                </span>
                                            )}
                                        </div>
                                        <div className="tw-text-gray-500">
                                            {log.device} | {log.ip}
                                        </div>
                                    </div>
                                    {log.current !== 1 && log.token_status !== 0 && (
                                        <CnaButton
                                            color="gray.6"
                                            variant="transparent"
                                            className="tw-px-2 tw-py-1"
                                            onClick={() => {
                                                logoutSession(log.id, index);
                                            }}
                                        >
                                            <SignOut
                                                weight="bold"
                                                size={18}
                                            />
                                        </CnaButton>
                                    )}
                                </Group>
                            ))}
                        </Stack>
                    </ScrollArea>
                </Box>
            </Stack>
            <Group
                justify="end"
                className="tw-border-t tw-pt-4 tw-mt-4"
            >
                <CnaButton
                    color="basic"
                    variant="outline"
                    onClick={() => logoutSession(0)}
                    leftSection={<SignOut weight="bold" />}
                >
                    {t("setting.login_history.all_log_out", lang)}
                </CnaButton>
                <CnaButton
                    color="basic"
                    variant="outline"
                    onClick={() => close("SettingLoginModal")}
                    leftSection={<X weight="bold" />}
                >
                    {t("common.close", lang)}
                </CnaButton>
            </Group>
        </Modal>
    );
};

export default LoginLogModal;
