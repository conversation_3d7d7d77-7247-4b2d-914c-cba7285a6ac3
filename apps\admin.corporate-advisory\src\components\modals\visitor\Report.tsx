import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import {
    ActionIcon,
    Group,
    Modal,
    SimpleGrid,
    Stack,
    Text,
    Textarea,
    TextInput,
    Tooltip,
} from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Download, Eye, Trash } from "@phosphor-icons/react";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import { downloadFile } from "@/utils/files";
import dayjs from "dayjs";

const Report = () => {
    const { lang } = useSettingStore();

    const { openFileView } = useFileViewer();

    const visitorReportParams = useModalStore((state) => state.modalParams.visitorReportModal);
    const report = visitorReportParams?.report;

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.visitorReportModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        close("visitorReportModal");
    });

    const modalFooterButtons = [
        {
            key: "close",
            label: "关闭",
            leftSection: <Trash />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    const previewFile = useMemoizedFn((url: string) => {
        // 把 url 中的域名给去除
        const urlWithoutDomain = url.replace(/^(https?:\/\/)?([^\/]+)\//, '');
        openFileView(`${window.api_base_url}/api/v1/admin/file/resource?path=${urlWithoutDomain}&type=remote`, {
            title: "报告附件",
        });
    });

    const handleDownloadFile = useMemoizedFn((url: string) => {
        // 把 url 中的域名给去除
        const urlWithoutDomain = url.replace(/^(https?:\/\/)?([^\/]+)\//, '');
        downloadFile(urlWithoutDomain, "remote");
    });

    return (
        <Modal
            title="C&A会谈结果报告"
            opened={show}
            onClose={closeModal}
            size="xl"
        >
            <Stack>
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                    <TextInput
                        label="报告人"
                        defaultValue={report?.report_author}
                        readOnly
                    />
                    <TextInput
                        label="创建时间"
                        defaultValue={
                            report?.created_at &&
                            dayjs(report?.created_at).format("YYYY-MM-DD HH:mm:ss")
                        }
                        readOnly
                    />
                </SimpleGrid>
                <SimpleGrid cols={{ base: 1, md: 2 }}>
                    <TextInput
                        label="到访日期"
                        defaultValue={report?.visitor_date}
                        readOnly
                    />
                    <TextInput
                        label="到访时间"
                        defaultValue={report?.visitor_time}
                        readOnly
                    />
                </SimpleGrid>
                <Textarea
                    label="来宾到访目的"
                    defaultValue={report?.visitor_purpose}
                    readOnly
                />
                <Textarea
                    label="期待值"
                    defaultValue={report?.expect}
                    readOnly
                />
                <Textarea
                    label="沟通/会议内容概述"
                    defaultValue={report?.content}
                    readOnly
                />
                <Textarea
                    label="详细进展"
                    defaultValue={report?.developments}
                    readOnly
                />
                <Textarea
                    label="讨论结果及评估"
                    defaultValue={report?.result}
                    readOnly
                />
                <Textarea
                    label="后续跟进计划内容"
                    defaultValue={report?.follow_content}
                    readOnly
                />
                <Textarea
                    label="备注"
                    defaultValue={report?.remark}
                    readOnly
                />
                {report?.attach && report?.attach.length > 0 && (
                    <>
                        <Text className="tw-pl-2 tw-font-bold tw-bg-[#060d3d] tw-text-white">
                            附件
                        </Text>
                        {report?.attach.map((item, index) => (
                            <Group
                                justify="space-between"
                                key={index}
                            >
                                <Text>{`附件${index + 1}`}</Text>
                                <Group>
                                    <Tooltip label="查看">
                                        <ActionIcon onClick={() => previewFile(item)}>
                                            <Eye />
                                        </ActionIcon>
                                    </Tooltip>
                                    <Tooltip label="下载">
                                        <ActionIcon onClick={() => handleDownloadFile(item)}>
                                            <Download />
                                        </ActionIcon>
                                    </Tooltip>
                                </Group>
                            </Group>
                        ))}
                    </>
                )}
            </Stack>
            <div className="tw-mt-10">
                <ModalFooter buttons={modalFooterButtons} />
            </div>
        </Modal>
    );
};

export default Report;
