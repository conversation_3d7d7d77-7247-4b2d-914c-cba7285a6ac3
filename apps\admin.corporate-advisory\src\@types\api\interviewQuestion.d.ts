declare global {
    type TInterviewQuestionSearchParams = {
        module?: number;
        keyword?: string;
    } & BasePaginateParams;

    type TInterviewQuestion = {
        id: number;
        module: number;
        module_name: string;
        question: string;
        next_questions?: TInterviewQuestionNext[];
        created_at: string;
        updated_at: string;
    };

    type TStoreInterviewQuestionParams = {
        module: number;
        question: string;
    };

    type TUpdateInterviewQuestionParams = {
        id: number;
        module: number;
        question: string;
    };

    type TInterviewQuestionNext = {
        id: number;
        base_id: number;
        next: string;
        created_at: string;
        updated_at: string;
    };

    type TStoreOrUpdateInterviewQuestionNextParams = {
        id: number;
        next: string;
    };

    type TInterviewQuestionResponse = {
        items: TInterviewQuestion[];
        paginate: BasePaginateResponse;
    };
}

export {};
