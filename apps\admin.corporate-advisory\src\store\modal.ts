import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "@code.8cent/store/createSelectors";

export type ModalState = {
    // 传入弹窗的参数
    modalParams: {
        [key: string]: any;
    };
    alert: {
        show: boolean;
        title?: string;
        message?: string;
        variant?: "success" | "danger" | "warning" | "info";
    };
    upload: {
        show: boolean;
        title?: string;
        accept: string[];
        maxSize: number;
        required?: boolean;
        onSuccess?: (file: File) => void;
        onError?: (error: any) => void;
    };
    languageSelect: boolean;
    passwordReset: boolean;
    profileValidate: boolean;
    languageRegion: boolean;
    calendarEventCreation: boolean;
    settingNotification: boolean;
    settingActivity: boolean;
    settingLoginHistory: boolean;
    settingSupport: boolean;
    projectCompanyCreate: boolean;
    projectCompanyApplication: boolean;
    projectCompanyDetail: boolean;
    confirm: ModalState["alert"] & {
        onConfirm?: () => void;
    };
    settingModal: boolean;
    mailSettingModal: boolean;
    associatePersonalSettingModal: boolean;
    teamProfileModal: boolean;
    clientProfileModal: boolean;
    remarkModal: boolean;
    tableModal: boolean;
    noticeInfoModal: boolean;
    benefitInfoModal: boolean;
    benefitApplicationModal: boolean;
    associateProfileModal: boolean;
    associateReviewModal: boolean;
    webStructureModal: boolean;
    firmInfoModal: boolean;
    digitalHumanFilesModal: boolean;
    gspApplicationModal: boolean;
    gspReviewModal: boolean;
    associatePreNameModal: boolean;
    associateOrganizationModal: boolean;
    accountCreateUpdateModal: boolean;
    gspReportFilesModal: boolean;
    gspReportFileReviewModal: boolean;
    accountPermissionModal: boolean;
    documentFolderModal: boolean;
    documentCreateModal: boolean;
    visitorAppInfoModal: boolean;
    visitorAppReviewModal: boolean;
    visitorVipInfoModal: boolean;
    visitorReportModal: boolean;
    activityInfoModal: boolean;
    activitySignupInfoModal: boolean;
    interviewQuestionForm: boolean;
    interviewQuestionNextList: boolean;
    interviewQuestionNextForm: boolean;
    examQuestionForm: boolean;
    currencyModal: boolean;
    refundExamineModal: boolean;
    gspRefundExamineModal: boolean;
    firmProcessDetailModal: boolean;
    setTeamTopModal: boolean;
    fieldTypeTeamListModal: boolean;
    threeCreateModal: boolean;
    gspPaymentVoucherExamineModal: boolean;
};

type ModalAction = {
    open: (modal: keyof ModalState, params?: any) => void;
    close: (modal: keyof ModalState) => void;
    openAlert: (title?: string, message?: string, variant?: ModalState["alert"]["variant"]) => void;
    closeAlert: () => void;
    openConfirm: (props: {
        title?: string;
        message?: string;
        variant?: ModalState["alert"]["variant"];
        onConfirm?: () => void;
    }) => void;
    closeConfirm: () => void;
    openUpload: (opts: {
        title?: string;
        accept?: string[];
        maxSize: number;
        required?: boolean;
        onSuccess?: (file: File) => void;
        onError?: (error: any) => void;
    }) => void;
    closeUpload: () => void;
};

const baseModalStore = create<ModalState & ModalAction>()(
    devtools(
        (set) => ({
            modalParams: {},
            alert: {
                show: false,
                title: "",
                message: "",
                variant: "info",
            },
            confirm: {
                show: false,
                title: "",
                message: "",
                onConfirm: () => { },
            },
            upload: {
                title: "",
                show: false,
                accept: ["image/png", "image/jpeg"],
                maxSize: 5 * 1024 * 1024,
                required: true,
                onSuccess: () => { },
                onError: () => { },
            },
            calendarEventCreation: false,
            languageSelect: false,
            passwordReset: false,
            profileValidate: false,
            languageRegion: false,
            settingNotification: false,
            settingActivity: false,
            settingLoginHistory: false,
            settingSupport: false,
            projectCompanyCreate: false,
            projectCompanyApplication: false,
            projectCompanyDetail: false,
            open: (modal, params) =>
                set((state) => ({
                    ...state,
                    [modal]: true,
                    modalParams: params
                        ? { ...state.modalParams, [modal]: params }
                        : state.modalParams,
                })),
            close: (modal) =>
                set((state) => ({
                    ...state,
                    [modal]: false,
                    modalParams: {
                        ...state.modalParams,
                        [modal]: undefined,
                    },
                })),
            openAlert: (title = "", message = "", variant = "info") => {
                set({
                    alert: {
                        show: true,
                        title,
                        message,
                        variant,
                    },
                });
            },
            closeAlert: () => {
                set({
                    alert: {
                        show: false,
                        title: "",
                        message: "",
                    },
                });
            },
            openConfirm: ({ title, message, variant = "info", onConfirm = () => { } }) => {
                set({
                    confirm: {
                        show: true,
                        title,
                        message,
                        variant,
                        onConfirm,
                    },
                });
            },
            closeConfirm: () => {
                set({
                    confirm: {
                        show: false,
                        title: "",
                        message: "",
                        variant: "info",
                        onConfirm: () => { },
                    },
                });
            },
            openUpload: ({
                title = "文件上传",
                accept = ["image/png", "image/jpeg"],
                required = true,
                maxSize,
                onSuccess,
                onError,
            }) => {
                set({
                    upload: {
                        show: true,
                        title,
                        accept,
                        required,
                        maxSize,
                        onSuccess,
                        onError,
                    },
                });
            },
            closeUpload: () => {
                set({
                    upload: {
                        show: false,
                        title: "",
                        accept: ["image/png", "image/jpeg"],
                        maxSize: 5 * 1024 * 1024,
                        required: true,
                        onSuccess: () => { },
                        onError: () => { },
                    },
                });
            },
            settingModal: false,
            mailSettingModal: false,
            associatePersonalSettingModal: false,
            teamProfileModal: false,
            clientProfileModal: false,
            remarkModal: false,
            tableModal: false,
            noticeInfoModal: false,
            benefitInfoModal: false,
            benefitApplicationModal: false,
            associateProfileModal: false,
            associateReviewModal: false,
            webStructureModal: false,
            firmInfoModal: false,
            digitalHumanFilesModal: false,
            gspApplicationModal: false,
            gspReviewModal: false,
            associatePreNameModal: false,
            associateOrganizationModal: false,
            accountCreateUpdateModal: false,
            gspReportFilesModal: false,
            gspReportFileReviewModal: false,
            accountPermissionModal: false,
            documentFolderModal: false,
            documentCreateModal: false,
            visitorAppInfoModal: false,
            visitorAppReviewModal: false,
            visitorVipInfoModal: false,
            visitorReportModal: false,
            activityInfoModal: false,
            activitySignupInfoModal: false,
            interviewQuestionForm: false,
            interviewQuestionNextList: false,
            interviewQuestionNextForm: false,
            examQuestionForm: false,
            currencyModal: false,
            refundExamineModal: false,
            gspRefundExamineModal: false,
            firmProcessDetailModal: false,
            setTeamTopModal: false,
            fieldTypeTeamListModal: false,
            threeCreateModal: false,
            gspPaymentVoucherExamineModal: false,
        }),
        {
            name: "modal-store",
        }
    )
);

const useModalStore = createSelectors(baseModalStore);

export default useModalStore;
