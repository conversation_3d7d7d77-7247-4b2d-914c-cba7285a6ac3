import { create } from "zustand";
import { devtools } from "zustand/middleware";
import createSelectors from "./createSelectors";

type DataState = {
    languages: LanguageItem[];
    currencies: CurrencyItem[];
    timezones: TimezoneItem[];
    timeFormats: TimeFormatItem[];
    dateFormats: DateFormatItem[];
    languageStructures: LanguageStructure[];
    countryDatas: CountryDataItem[];
    filteredCountryDatas: () => CountryDataItem[];
    areas: AreaData[];
};

type DataAction = {
    setLanguages: (languages: LanguageItem[]) => void;
    setCurrencies: (currencies: CurrencyItem[]) => void;
    setTimezones: (timezones: TimezoneItem[]) => void;
    setTimeFormats: (timeFormats: TimeFormatItem[]) => void;
    setDateFormats: (dateFormats: DateFormatItem[]) => void;
    setLanguageStructures: (languageStructures: LanguageStructure[]) => void;
    setCountryDatas: (countryDatas: CountryDataItem[]) => void;
    setAreas: (areas: AreaData[]) => void;
};

type DataStateAndAction = DataState & DataAction;

const baseDataStore = create<DataStateAndAction>()(
    devtools(
        (set, get) => ({
            languages: [],
            currencies: [],
            timezones: [],
            timeFormats: [],
            dateFormats: [],
            languageStructures: [],
            countryDatas: [],
            areas: [],
            filteredCountryDatas: () => {
                let countryDatas = get().countryDatas;

                let filteredCountryDatas = countryDatas?.filter?.(
                    (data) =>
                        data.countryID === 44 ||
                        data.countryID === 127 ||
                        data.countryID === 92 ||
                        data.countryID === 193 ||
                        data.countryID === 228
                );

                return filteredCountryDatas || [];
            },
            setLanguages: (languages) => set({ languages }),
            setCurrencies: (currencies) => set({ currencies }),
            setTimezones: (timezones) => set({ timezones }),
            setTimeFormats: (timeFormats) => set({ timeFormats }),
            setDateFormats: (dateFormats) => set({ dateFormats }),
            setLanguageStructures: (languageStructures) => {
                set({ languageStructures });
            },
            setCountryDatas: (countryDatas) => {
                set({ countryDatas });
            },
            setAreas: (areas) => {
                set({ areas });
            },
        }),
        {
            name: "data-store",
        }
    )
);

const useDataStore = createSelectors(baseDataStore);

export default useDataStore;
