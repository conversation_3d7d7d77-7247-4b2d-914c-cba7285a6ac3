declare global {
    type CompanyCategory = {
        companyCategoriesID: string;
        companyCategoriesName: string;
    };

    type CountryDataItem = {
        countryCode: string;
        countryEN: string;
        countryID: number;
        countryISOCode2: string;
        countryMS: string;
        countryZH: string;
        countryZT: string;
        // [property: string]: any;
    };

    type ProjectSelectItem = {
        projectCategoriesID: string;
        projectCategoriesName: string;
    };

    type BankItem = {
        ID: number;
        bankCountry: string;
        bankName: string;
        bankSwiftCode: string;
    };

    type PaymentMethod = {
        ID: number;
        paymentName: string;
    };
}
export {};
