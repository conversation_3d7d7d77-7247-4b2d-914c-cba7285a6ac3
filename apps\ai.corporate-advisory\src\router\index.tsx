import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, json, Navigate } from "react-router-dom";
import RouteError from "@code.8cent/react/components/RouteError";
import RootLayout from "@/components/layouts/RootLayout";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import MemberProfilePage from "@/pages/member/Profile";
import MemberNotificationPage from "@/pages/member/Notification";
import MemberDocumentPage from "@/pages/member/Document";
import MemberBenefitPage from "@/pages/member/Benefit";
import MemberBillingsPage from "@/pages/member/Billings";
import MemberProjectPage from "@/pages/member/Project";
import AccountRegisterPage from "@/pages/account/Register";
import AccountWizardPage from "@/pages/wizard";
import MemberCommunityPage from "@/pages/member/Community";
import MemberSchedulePage from "@/pages/member/Schedule";
import LoginPage from "@code.8cent/react/pages/LoginPage";
import ForgetPasswordPage from "@code.8cent/react/pages/ForgetPasswordPage";
import SettingPage from "@code.8cent/react/pages/SettingPage";
import withRouteGuard from "@code.8cent/react/hoc/withRouteGuard";
import TeamPage from "@/pages/team";
import TeamJoinPage from "@/pages/team/join";
import AccountRegisterPageV2 from "@/pages/account/RegisterV2";
import AccountWelcomePage from "@/pages/account/Welcome";
import AccountDocumentsPage from "@/pages/account/Documents";
import AccountRegisterPayment from "@/pages/account/RegisterPayment";
import TeamHubPage from "@/pages/team/hub";
import MemberTeamPage from "@/pages/member/Team";
import JoinPage from "@/pages/member/Join";
import OfficePage from "@/pages/member/Office";
const GuardedRootLayout = withRouteGuard(RootLayout, {
    publicRoutes: ["/account/*", "/team", "/team/*"],
});

const router = createBrowserRouter([
    {
        path: "/",
        element: <GuardedRootLayout />,
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        children: [
            {
                path: "team",
                element: <TeamHubPage />,
            },
            // {
            //     path: "team",
            //     element: <TeamPage />
            // },
            // {
            //     path: "team/:phone",
            //     element: <TeamJoinPage />
            // },
            {
                path: "account/welcome",
                element: <AccountWelcomePage />,
            },
            {
                path: "account/documents",
                element: <AccountDocumentsPage />,
            },
            {
                path: "account/login",
                element: <LoginPage />,
            },
            {
                path: "account/forget-password",
                element: <ForgetPasswordPage />,
            },
            {
                path: "account/wizard",
                element: <AccountWizardPage />,
            },
            {
                path: "account/register",
                element: <AccountRegisterPageV2 />,
            },
            {
                path: "account/register-payment",
                element: <AccountRegisterPayment />,
            },
            {
                path: "member",
                element: <DashboardLayout />,
                children: [
                    {
                        path: "profile",
                        element: <MemberProfilePage />,
                    },
                    {
                        path: "settings",
                        element: <SettingPage />,
                    },
                    {
                        path: "notifications",
                        element: <MemberNotificationPage />,
                    },
                    {
                        path: "documents",
                        element: <MemberDocumentPage />,
                    },
                    {
                        path: "projects",
                        element: <MemberProjectPage />,
                    },
                    {
                        path: "billings",
                        element: <MemberBillingsPage />,
                    },
                    {
                        path: "benefits",
                        element: <MemberBenefitPage />,
                    },
                    {
                        path: "community",
                        element: <MemberCommunityPage />,
                    },
                    {
                        path: "schedule",
                        element: <MemberSchedulePage />,
                    },
                    {
                        path: "team",
                        element: <MemberTeamPage />,
                    },
                    {
                        path: "join",
                        element: <JoinPage />,
                    },
                    {
                        path: "office",
                        element: <OfficePage />,
                    },
                    {
                        path: "*",
                        errorElement: <RouteError className="tw-h-[100%] tw-w-[100%]" />,
                        loader: () => {
                            throw json({}, { status: 404, statusText: "Page Not Found" });
                        },
                    },
                ],
            },
        ],
    },
    {
        path: "*",
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        loader: () => {
            throw json({}, { status: 404, statusText: "Page Not Found" });
        },
    },
]);

export default router;
