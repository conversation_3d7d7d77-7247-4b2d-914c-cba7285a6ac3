declare global {
    type TStationFinance = {
        id: string;
        paymentNumber: string;
        type: number; // 1业务收入 2业务支出 3豁免 4津贴收入 5津贴支出
        pay_type: number; // 1 微信 2支付宝 3网银 4豁免
        amount: string;
        project_name: string;
        status: number;
        profile: {
            profileID: number;
            profileName: string;
        };
        created_at: string;
        main: string; // 公司主体
        commission: string; // 手续费
        profilePartnerCode: string; // 合伙人编码
        profileNRIC: string; // 身份证号
        // todo more field to display
    };

    // 财务统计
    type TStationFinanceStaticTotal = {
        income: number;
        expense: number;
        total: number;
        deductible: number;
        yesterday: {
            incomeIncreasePercentage: number;
            expenseIncreasePercentage: number;
        };
    };

    type TStationFinanceSearchParams = {
        type?: 1 | 2 | 3; // 1系统的收入 2系统的支出 3豁免
        profile_name?: string;
        profile_code?: string;
        start_time?: string;
        end_time?: string;
    } & TPageQueryParams;

    type TStationFinanceResponse = {
        data: TStationFinance[];
        last_page: number;
        total: number;
    };
}

export {};
