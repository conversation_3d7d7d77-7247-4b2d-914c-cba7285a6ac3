import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const web = {
    structureList: async (params: TWebStructureSearchParams) => {
        const { error, result } = await cnaRequest<TWebStructuresResponse>(
            "/api/v1/admin/web/structures",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    structureStore: async (params: Omit<TWebStructure, "structureID">) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/web/structures",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    structureUpdate: async (params: Omit<TWebStructure, "structureID">, id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/web/structures/${id}`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default web;
