import api from "@/apis";
import useProfileStore from "@/store/profile";
import { useMemoizedFn, useMount } from "ahooks";
import React, { cloneElement, ReactElement, useState } from "react";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import {
    ActionIcon,
    Badge,
    Box,
    Drawer,
    Group,
    Image,
    ScrollArea,
    Text,
    NavLink,
} from "@mantine/core";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import {
    User,
    Bell,
    LineSegments,
    Handshake,
    Coin,
    Gear,
    UsersThree,
    IconProps,
    Gift,
    Question,
    SignOut,
    List,
    CalendarDots,
    UsersFour,
    AddressBook,
    AddressBookTabs,
    Robot,
    Brain,
    HeadCircuit,
} from "@phosphor-icons/react";
import useModalStore from "@/store/modal";
import { cnaRequest } from "@code.8cent/utils";

const SideBarLinks = [
    {
        name: "navigation.profile",
        path: "/member/profile",
        icon: <User />,
    },
    // {
    //     name: "团队",
    //     path: "/member/team",
    //     icon: <UsersThree />,
    // },
    {
        name: "navigation.project",
        path: "/member/projects",
        icon: <Handshake />,
    },
    {
        name: "navigation.notification",
        path: "/member/notifications",
        icon: <Bell />,
    },
    {
        name: "navigation.schedule",
        path: "/member/schedule",
        icon: <CalendarDots />,
    },
    {
        name: "navigation.data",
        path: "/member/documents",
        icon: <LineSegments />,
    },
    {
        name: "navigation.billing",
        path: "/member/billings",
        icon: <Coin />,
    },
    {
        name: "navigation.community",
        path: "/member/community",
        icon: <AddressBookTabs />,
    },
    {
        name: "navigation.benefits",
        path: "/member/benefits",
        icon: <Gift />,
    },
    {
        name: "navigation.ai_assistant",
        icon: <HeadCircuit />,
    },
    {
        name: "navigation.setting",
        path: "/member/settings",
        icon: <Gear />,
    },
] as const;
const BottomLinks=[
    {
        name: "晋级管理合伙人",
        path: "/member/join",
        
    },
    {
        name: "开设办事处",
        path: "/member/team",
       
    },
    {
        name: "开设事务所",
        path: "/member/office",
        
    },
]

const DashboardLayout: React.FC = () => {
    const [setProfile, setInited] = useProfileStore((state) => [state.setProfile, state.setInited]);

    const [minNavOpened, setMinNavOpened] = useState<boolean>(false);

    const openConfirm = useModalStore.use.openConfirm();

    const { lang, setUpSetting } = useSettingStore();

    const { pathname } = useLocation();

    const navigate = useNavigate();

    const logout = useMemoizedFn(async () => {
        openConfirm({
            title: t("logout.title", lang),
            message: t("logout.detail", lang),
            onConfirm: async () => {
                await cnaRequest("/api/v1/login/logout", "POST");
                await window.localForage.removeItem("cna-token");
                navigate("/account/login");
            },
        });
    });

    const toAIAssistant = useMemoizedFn(async () => {
        let { result, error } = await cnaRequest<{ link: string }>("/api/v1/ai", "GET");

        if (result) {
            let url = result.data.link;
            const anchor = document.createElement("a");
            anchor.href = url;
            anchor.target = "_blank";
            anchor.rel = "noopener noreferrer";
            anchor.click();
            anchor.remove();
        }
    });

    useMount(async () => {
        let userProfile = await api.user.getUserProfile();

        if (userProfile) {
            setInited(true);

            setProfile(userProfile);

            setUpSetting({
                lang: userProfile.settingLanguage,
                settingCurrency: userProfile.settingCurrency,
                settingTimeFormat: userProfile.settingTimeFormat,
                settingTimezone: userProfile.settingTimezone,
                settingDateFormat: userProfile.settingDateFormat,
                settingLanguage: userProfile.settingLanguage,
                settingNotifyEmergency: userProfile.settingNotifyEmergency,
                settingNotifyImportanceUpdate: userProfile.settingNotifyImportanceUpdate,
                settingNotifyJoinInvestigate: userProfile.settingNotifyJoinInvestigate,
                settingNotifyRecPrivateMsg: userProfile.settingNotifyRecPrivateMsg,
                settingNotifySafeUpdated: userProfile.settingNotifySafeUpdated,
                settingNotifySuspiciousOperation: userProfile.settingNotifySuspiciousOperation,
                settingNotifySystemUpdate: userProfile.settingNotifySystemUpdate,
            });
        } else {
            navigate("/account/login");
        }
    });

    const NavLinks = () => {
        return (
            <>
                <ScrollArea
                    classNames={{
                        scrollbar: "tw-bg-basic-5",
                        thumb: "tw-bg-basic-3",
                    }}
                    type="hover"
                    scrollbars="y"
                    scrollHideDelay={500}
                    scrollbarSize={6}
                    className="tw-flex-1 tw-py-3 tw-overflow-y-auto tw-overflow-x-hidden tw-w-full"
                >
                    {SideBarLinks.map((link, index) => {
                        const icon = cloneElement(link.icon, {
                            size: 26,
                            weight: "thin",
                        } as IconProps);

                        switch (link.name) {
                            case "navigation.ai_assistant": {
                                return (
                                    <Box
                                        className={`tw-flex tw-px-8 tw-text-sm lg:tw-text-sm tw-py-2 lg:tw-py-2 tw-items-center tw-justify-start tw-text-gray-400 tw-cursor-pointer`}
                                        key={index}
                                        onClick={toAIAssistant}
                                    >
                                        {icon}
                                        <Text className="tw-flex-1 tw-text-left tw-ml-6 tw-text-sm">
                                            {t(link.name, lang)}
                                        </Text>
                                    </Box>
                                );
                            }

                            // case "navigation.log_out": {
                            //     return (
                            //         <Box
                            //             className={`tw-flex tw-px-8 tw-text-sm lg:tw-text-sm tw-py-2 lg:tw-py-2 tw-items-center tw-justify-start tw-text-gray-400 tw-cursor-pointer`}
                            //             key={index}
                            //             onClick={logout}
                            //         >
                            //             {icon}
                            //             <Text className="tw-flex-1 tw-text-left tw-ml-6 tw-text-sm">
                            //                 {t(link.name, lang)}
                            //             </Text>
                            //         </Box>
                            //     );
                            // }

                            default: {
                                return (
                                    <Box
                                        className={`tw-flex tw-px-8 tw-text-sm lg:tw-text-sm tw-py-2 lg:tw-py-2 tw-items-center tw-justify-start ${
                                            pathname === link.path
                                                ? "tw-text-gray-50 tw-font-extrabold tw-bg-basic-7"
                                                : "tw-text-gray-400"
                                        }`}
                                        key={index}
                                        component={Link}
                                        to={{ pathname: link.path }}
                                        onClick={() => {
                                            setMinNavOpened(false);
                                        }}
                                    >
                                        {icon}
                                        <Text className="tw-flex-1 tw-text-left tw-ml-6 tw-text-sm">
                                            {t(link.name, lang)}
                                        </Text>
                                        {/* <Badge
                                            color="red"
                                            className="tw-ml-auto tw-text-[9px]"
                                            size="md"
                                            circle
                                        >
                                            99
                                        </Badge> */}
                                    </Box>
                                );
                            }
                        }
                    })}
                </ScrollArea>
                {/* <div className="tw-pt-3 tw-pb-4">
                    <div
                        className={`tw-flex tw-px-4 tw-items-center tw-text-gray-200 tw-cursor-pointer`}
                        onClick={toAIAssistant}
                    >
                        <Question size={20} />
                        <Text className="tw-text-sm tw-ml-4">
                            {t("navigation.ai_assistant", lang)}
                        </Text>
                    </div>
                    <div
                        className={`tw-mt-2 tw-flex tw-px-4 tw-items-center tw-text-gray-200 tw-cursor-pointer`}
                        onClick={logout}
                    >
                        <SignOut size={20} />
                        <Text className="tw-text-sm tw-ml-4">{t("navigation.log_out", lang)}</Text>
                    </div>
                </div> */}
            </>
        );
    };

    return (
        <div className="tw-flex tw-h-[100vh] tw-w-[100vw] tw-flex-col md:tw-flex-row">
            {/*
                This is the side navigation bar on the left side of the screen.
                It is hidden on small screens and shows up on larger screens.
                It contains a logo, a heading, and a list of links.
            */}
            <div className="md:tw-w-[240px]  tw-transition-all md:tw-flex tw-border-r tw-bg-basic-5 tw-hidden tw-flex-col tw-w-0">
                <div className="tw-mb-2 tw-mt-5">
                    <Image
                        src="/images/C&A-Logo-Icon-White.svg"
                        w={60}
                        className="tw-mx-auto tw-my-4"
                    />
                    <Text className="tw-text-center tw-mt-3 tw-text-xl tw-text-white tw-tracking-[3px] tw-font-bold">
                        {/* {t("dashboard.title", lang)} */}
                        AI 办公室
                    </Text>
                </div>
                <NavLinks />
                <div className="tw-mb-4">
                    {BottomLinks.map((item, index) => {
                        return (
                            <Box
                                key={index}
                                className={`bottomButton emo-${index} tw-mb-3 tw-mx-4 tw-flex tw-px-8 tw-text-sm lg:tw-text-sm tw-py-2 lg:tw-py-2 tw-items-center tw-justify-start ${
                                    pathname === item.path
                                        ? "tw-text-gray-50 tw-font-extrabold tw-bg-basic-7"
                                        : "tw-text-gray-400"
                                }`}
                                component={Link}
                                to={{ pathname: item.path }}
                           >
                                <Text className="tw-flex-1 tw-text-left  tw-text-lg">
                                    {item.name}
                                </Text>
                            </Box>
                        );
                    })}
                </div>
            </div>
            {/*
                This is the top navigation bar that shows up on small screens.
                It is hidden on larger screens and shows up on smaller screens.
                It contains a logo and a button to open or close the side navigation bar.
            */}
            <Group
                className="md:tw-hidden tw-py-2 tw-border-b tw-px-6 tw-bg-basic-5"
                justify="space-between"
            >
                <Image
                    src="/images/navbar-logo-sm.png"
                    w={"60%"}
                />

                <ActionIcon
                    variant="transparent"
                    color="white"
                    onClick={() => {
                        setMinNavOpened(true);
                    }}
                >
                    <List size={36} />
                </ActionIcon>
            </Group>
            {/*
                This is the main content area of the page. It will display
                the content of the currently selected page. It takes up
                the majority of the screen, and is scrollable if the content
                is too large to fit on the screen.
            */}
            <div className="tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden">
                <Outlet />
            </div>
            {/*
                This is the Drawer component from Mantine.
                It is only visible on small screens, and is used
                to display the side navigation bar when the user
                clicks on the hamburger button on the top right
                of the screen.
            */}
            <Drawer
                opened={minNavOpened}
                onClose={() => {
                    setMinNavOpened(false);
                }}
                size={"80%"}
                className="md:tw-hidden"
                classNames={{
                    content: "tw-bg-basic-5 tw-flex tw-flex-col",
                    header: "tw-py-0 tw-bg-basic-5",
                    body: "tw-flex-1 tw-flex tw-flex-col",
                    close: "tw-text-gray-50 hover:tw-bg-basic-6",
                }}
                title={
                    <Group className="tw-py-2">
                        <Image
                            src="/images/navbar-logo-sm.png"
                            w={"80%"}
                        />
                    </Group>
                }
            >
                <NavLinks />
            </Drawer>
        </div>
    );
};

export default DashboardLayout;
