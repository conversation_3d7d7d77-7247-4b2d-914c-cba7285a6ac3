import { useState } from "react";
import useProfileStore from "@/store/profile";
import { useShallow } from "zustand/react/shallow";
import { Grid, GridColProps, Input, TextInput } from "@mantine/core";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import CountrySelect from "@code.8cent/react/components/CountrySelect";
import useDataStore from "@code.8cent/store/data";

const AddressInput = () => {
    // Fetch the current language from the setting store
    const lang = useSettingStore.use.lang();

    // Define the address fields with placeholders and database keys
    const [addressFields] = useState<
        {
            placeholder: string;
            key: keyof UserProfileResponse;
            span: GridColProps["span"];
        }[]
    >([
        {
            placeholder: t("introduction.label.unit", lang),
            key: "profileAddressUnit",
            span: { base: 12, md: 6 },
        },
        {
            placeholder: t("introduction.label.street", lang),
            key: "profileAddressStreet",
            span: { base: 12, md: 6 },
        },
        {
            placeholder: t("introduction.label.city", lang),
            key: "profileAddressCity",
            span: { base: 12 },
        },
        {
            placeholder: t("introduction.label.state", lang),
            key: "profileAddressState",
            span: { base: 12 },
        },
        {
            placeholder: t("introduction.label.postcode", lang),
            key: "profileAddressPostcode",
            span: { base: 12, md: 6 },
        },
        {
            placeholder: t("introduction.label.country", lang),
            key: "profileAddressCountry",
            span: { base: 12, md: 6 },
        },
    ]);

    // Fetch user profile data from Zustand store
    const user = useProfileStore(
        useShallow((state) => ({
            profileAddressUnit: state.profileAddressUnit,
            profileAddressStreet: state.profileAddressStreet,
            profileAddressCity: state.profileAddressCity,
            profileAddressState: state.profileAddressState,
            profileAddressPostcode: state.profileAddressPostcode,
            profileAddressCountry: state.profileAddressCountry,
        }))
    );

    const countryDatas = useDataStore.use.countryDatas();

    // Function to update profile values in Zustand store
    const setProfileValue = useProfileStore.use.setProfileValue();

    return (
        <Input.Wrapper
            label={t("introduction.label.address", lang)}
            labelProps={{ className: "tw-mb-3" }}
        >
            <Grid gutter={3}>
                {addressFields.map((field, index) => (
                    <Grid.Col
                        span={field.span}
                        key={field.key}
                        className="tw-mb-3"
                    >
                        {field.key === "profileAddressCountry" ? (
                            <CountrySelect<CountryDataItem>
                                data={countryDatas}
                                flagKey="countryISOCode2"
                                labelKey="countryZH"
                                valueKey="countryID"
                                placeholder={field.placeholder}
                                searchable={true}
                                value={user[field.key] ?? ""}
                                onChange={(value) => {
                                    setProfileValue(field.key, value);
                                }}
                            />
                        ) : (
                            <TextInput
                                placeholder={field.placeholder}
                                value={user[field.key] ?? ""}
                                onChange={(e) => {
                                    setProfileValue(field.key, e.target.value);
                                }}
                            />
                        )}
                    </Grid.Col>
                ))}
            </Grid>
        </Input.Wrapper>
    );
};

export default AddressInput;
