declare global {
    type TDocumentSearchParams = {
        keyword: string | null;
    } & TPageQueryParams;

    type TDocumentFolderSearchParams = {
        keyword: string | null;
    } & TPageQueryParams;

    type TDocumentFolderStoreParams = {
        foldersNameEN: string;
        foldersNameMS: string;
        foldersNameZH: string;
        foldersNameZT: string;
        parentId?: number;
        order?: number;
    };

    type TDocumentFolder = {
        id: number;
        profileID: number;
        foldersNameEN: string;
        foldersNameMS: string;
        foldersNameZH: string;
        foldersNameZT: string;
        parentId: number;
        created_at: string;
        updated_at: string;
        deleted_at: string | null;
        order: number;
    };

    type TDocumentStoreParams = {
        documentTitle: string;
        documentDescription: string;
        documentLanguage: string;
        documentValidity: string;
        documentFile: File;
        versionid?: number;
        documentVersion: string;
        folderId: number;
        order: number;
    };

    type TDocumentFile = {
        documentID: number;
        documentXID: number;
        documentFile: string;
        documentVersion: string;
        isShow: number;
        created_at: string;
        updated_at: string;
    };

    type TDocument = {
        documentID: number;
        documentTitle: string;
        documentDescription?: string;
        documentLanguage: string;
        documentValidity: string;
        documentVersion: string;
        createUser: number;
        createRole: number;
        createTime: string;
        editUser: number;
        editRole: number;
        editTime: string;
        allFiles: TDocumentFile[];
        created_at: string;
        updated_at: string;
        createUserName: string;
        documentFile: string;
        type: number;
        order: number;
        folderId: number;
    };

    type TDocumentsResponse = {
        items: TDocument[];
        paginate: BasePaginateResponse;
    };

    type TDocumentFoldersResponse = {
        items: TDocumentFolder[];
        paginate: BasePaginateResponse;
    };

    type ThreeItem = {
        department: string;
        email_body: string;
        file: string;
        file_version: string;
        id: number;
        job: string;
        level: number;
        name: string;
        team_industry_id: number;
    }

    type ThreeListData = {
        data: ThreeItem[];
        // paginate: BasePaginateResponse;
        current_page: number;
        per_page: number;
        total: number;
        // totalPage: number;
        // total_page: number;
    }

    type ThreeStoreParams = {
        level: string;
        file: File;
        file_version: string;
    }
}

export {};
