import {
    useEffect,
    forwardRef,
    useImper<PERSON><PERSON><PERSON><PERSON>,
    ForwardRefExoticComponent,
    Ref,
    RefAttributes,
    useMemo,
} from "react";
import { TextInput, Text, Select } from "@mantine/core";
import { zodResolver } from "@hookform/resolvers/zod";

import dayjs from "dayjs";
import { z } from "zod";
import { Controller, SubmitHandler, useForm, useWatch } from "react-hook-form";
import { PasswordResetFormNoCurrentRef } from "@code.8cent/react/components/PasswordResetFormNoCurrent";
import { useSetState } from "ahooks";

type DateInput = {
    day: string;
    month: string;
    year: string;
};

export type DatePickerRef = {
    validate: () => void;
};

const schema = z.object({
    day: z.string().regex(/^(0?[1-9]|[12][0-9]|3[01])$/, "日必须是数字，且范围在1-31之间"),
    month: z.string().regex(/^(0?[1-9]|1[0-2])$/, "月必须是数字，且范围在1-12之间"),
    year: z
        .string()
        .regex(/^\d{4}$/, "年必须是4位数字")
        .refine((value) => parseInt(value) <= 2024, {
            message: "年不能超过2024",
        }),
});

type DatePickerProps = {
    defaultValue: DateInput;
    onChange: (value: string) => void;
    label: string;
    placeholder: {
        year: string;
        month: string;
        day: string;
    };
    ref: DatePickerRef;
};

const DatePicker: ForwardRefExoticComponent<
    Omit<DatePickerProps, "ref"> & RefAttributes<DatePickerRef>
> = forwardRef((props, ref) => {
    const { defaultValue, onChange, label, placeholder } = props;
    
    const { trigger, control, getValues, setValue, watch, resetField } = useForm<DateInput>({
        defaultValues: {
            day: "",
            month: "",
            year: "",
        },
        resolver: zodResolver(schema),
    });

    const [searchState, setSearchState] = useSetState({
        day: "",
        month: "",
        year: ""
    })

    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 100 }, (_, i) => (currentYear - i).toString());
    const months = Array.from({ length: 12 }, (_, i) => (i + 1).toString().padStart(2, "0"));
    const getDaysInMonth = (year: number, month: number) => {
        return new Date(year, month, 0).getDate();
    };

    const year = watch("year");
    const month = watch("month");

    const days = useMemo(() => {
        if (!year || !month) return [];
        const daysInMonth = getDaysInMonth(parseInt(year), parseInt(month));
        return Array.from({ length: daysInMonth }, (_, i) => (i + 1).toString().padStart(2, "0"));
    }, [year, month]);

    useEffect(() => {
        setValue("month", "");
    }, [year]);

    const formValue = useWatch({
        control,
    });

    const validate = () => {
        console.log("validate");
        const isValid = trigger(); // 触发校验
        // return isValid; // 返回校验结果
    };

    // 使用 useImperativeHandle 将 validate 方法暴露给父组件
    useImperativeHandle(ref, () => ({
        validate,
    }));

    useEffect(() => {
        if (typeof onChange === "function") {
            try {
                let value = dayjs(`${formValue.year}-${formValue.month}-${formValue.day}`).format(
                    "YYYY-MM-DD"
                );
                onChange(value);
            } catch (err) {
                console.log(err);
            }
        }
    }, [formValue]);

    useEffect(() => {
       
        setValue("year", defaultValue.year);
        setValue("month", defaultValue.month);
        setValue("day", defaultValue.day);
    }, [defaultValue]);

    return (
        <div className="tw-flex  tw-gap-1">
            <Controller
                name="year"
                control={control}
                render={({ field, fieldState }) => (
                    <Select
                        searchable
                        {...field}
                        label={label}
                        data={years}
                        placeholder={placeholder.year}
                        error={fieldState.error ? true : false}
                        searchValue={searchState.year}
                        onSearchChange={(e)=>{
                            setSearchState({
                                year: e
                            })
                        }}
                        onChange={(e) => {
                            field.onChange(e);
                            setSearchState({
                                "year": e,
                                "month": "",
                                "day": ""
                            });
                            setValue("month", "");
                            setValue("day", "");
                            trigger("year");
                        }}
                        rightSection={<Text>年</Text>}
                    />
                )}
            />
            <Controller
                name="month"
                control={control}
                render={({ field, fieldState }) => (
                    <Select
                        searchable
                        label=" "
                        data={months}
                        placeholder={placeholder.month}
                        {...field}
                        searchValue={searchState.month}
                        onSearchChange={(e)=>{
                            setSearchState({
                                month: e
                            })
                        }}
                        error={fieldState.error ? true : false}
                        onChange={(e) => {
                            field.onChange(e);
                            setValue("day", "");
                            setSearchState({
                                "month": e,
                                "day": ""
                            }); // 当月份改变时，清空日
                            trigger("month");
                        }}
                        rightSection={<Text>月</Text>}
                    />
                )}
            />
            <Controller
                name="day"
                control={control}
                render={({ field, fieldState }) => (
                    <Select
                        label=" "
                        searchable
                        data={days}
                        placeholder={placeholder.day}
                        {...field}
                        error={fieldState.error ? true : false}
                        searchValue={searchState.day}
                        onSearchChange={(e)=>{
                            setSearchState({
                                day: e
                            })
                        }}
                        onChange={(e) => {
                            setSearchState({
                                day: e
                            })
                            field.onChange(e);
                            trigger("day");
                        }}
                        rightSection={<Text>日</Text>}
                    />
                )}
            />
        </div>
    );
});

export default DatePicker;
