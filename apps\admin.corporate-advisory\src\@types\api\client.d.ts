declare global {
    type TClientSearchParams = {
        status?: string;
        keyword: string | null;
        profileID?: number;
    } & TPageQueryParams;

    type TClient = {
        companyID: number;
        companyName: string;
        companyEmail: string;
        companyMobile: string;
        companyState: string;
        companyCountryID: number;
        companyRegisterCode: string;
        companyCategoriesID: number;
        companyProjectID: number;
    }

    type TClientUpdateParams = {
        companyName: string;
        companyRegisterCode: string;
        companyCategoriesID: number;
    }

    type TClientsResponse = {
        items: TClient[];
        paginate: BasePaginateResponse;
    };
}

export {};
