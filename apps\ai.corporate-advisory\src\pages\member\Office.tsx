import useProfileStore from "@/store/profile";
import { t } from "@code.8cent/i18n";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, PageHeader, ProfileAvatar } from "@code.8cent/react/components";
import noty from "@code.8cent/react/noty";
import useSettingStore from "@code.8cent/store/setting";
import { cnaRequest } from "@code.8cent/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    ActionIcon,
    Box,
    Grid,
    Group,
    Popover,
    Progress,
    Stack,
    Tabs,
    Text,
    Textarea,
    TextInput,
    Title,
} from "@mantine/core";
import { modals } from "@mantine/modals";
import { Copy, PaperPlaneTilt } from "@phosphor-icons/react";
import { useRequest } from "ahooks";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { useShallow } from "zustand/react/shallow";

type TeamMember = {
    profileAvatar: string;
    profileID: number;
    profileName: string;
    profilePartnerCode: string;
};

type InviteSentInput = {
    type: "email" | "phone";
    email: string;
    phone: string;
};

const TeamItem = ({ member }: { member: TeamMember }) => {
    const { lang } = useSettingStore(
        useShallow((state) => ({
            lang: state.lang,
        }))
    );
    return (
        <Grid
            key={member.profileID}
            align="center"
            className="tw-mb-5 last:tw-mb-0"
            columns={24}
        >
            <Grid.Col span={{ base: 8, xs: 4, sm: 4, md: 4 }}>
                <ProfileAvatar
                    className="tw-max-w-[60px]"
                    src={member.profileAvatar}
                />
            </Grid.Col>

            <Grid.Col span={{ base: 20, xs: 20, sm: 20, md: 18, lg: 14, xl: 12 }}>
                <Grid align="stretch">
                    <Grid.Col span={{ md: 6, base: 12 }}>
                        <Text size="sm">名字: {member.profileName}</Text>
                        {/* <Text size="sm">业绩金额: 10,000,000人民币</Text> */}
                        <Text size="sm">
                            {t("introduction.partner_code", lang)}: {member.profilePartnerCode}
                        </Text>
                    </Grid.Col>
                    <Grid.Col span={{ md: 6, base: 12 }}>
                        {/* <Text size="sm">合伙人编码: {member.profilePartnerCode}</Text> */}
                    </Grid.Col>
                </Grid>
            </Grid.Col>
        </Grid>
    );
};

const sendInviteSchema = z.discriminatedUnion("type", [
    z.object({
        type: z.literal("email"),
        email: z.string().email("form.email.incorrect"),
    }),
    z.object({
        type: z.literal("phone"),
        phone: z.string().regex(/^1[3-9]\d{9}$/g, "form.phone.number.incorrect"),
    }),
]);

const OfficePage = () => {
    const { lang } = useSettingStore();

    const { team_group, team_rank } = useProfileStore();

    const { control, handleSubmit, reset, getValues } = useForm<InviteSentInput>({
        resolver: zodResolver(sendInviteSchema),
        defaultValues: {
            type: "email",
            email: "",
            phone: "",
        },
    });

    const { data: threeThreeMembers = [] } = useRequest(async () => {
        const { result, error } = await cnaRequest<{ data: TeamMember[] }>(
            "/api/v1/team/list",
            "GET",
            {
                is_team: 1,
            }
        );

        console.log(result);

        if (!error) {
            return result.data.data;
        } else {
            return [];
        }
    });

    const { data: inviteToken = null } = useRequest(async () => {
        const { result, error } = await cnaRequest<{ token: string }>(
            "/api/v1/team/inviteToken",
            "POST"
        );

        if (!error) {
            return result.data.token;
        } else {
            return null;
        }
    });

    const { data: teamMembers = [] } = useRequest(async () => {
        const { result, error } = await cnaRequest<BasePaginateResponse & { data: TeamMember[] }>(
            "/api/v1/team/list",
            "GET",
            {
                is_team: 0,
                pageSize: 1000,
            }
        );

        console.log(result);

        if (!error) {
            return result.data.data;
        } else {
            return [];
        }
    }, {});

    const { run: sendInvite, loading } = useRequest(
        async (data: InviteSentInput) => {
            let apiParams = {
                url: `${location.origin}/team?refer=${inviteToken}`,
            };

            if (data.type === "phone") {
                apiParams["phone"] = data.phone;
            } else {
                apiParams["email"] = data.email;
            }

            const { result, error } = await cnaRequest<string>(
                "/api/v1/team/inviteSend",
                "POST",
                apiParams
            );

            if (!error) {
                noty.success("发送邀请成功");
                reset();
            } else {
                noty.error("发送邀请失败", error.message);
            }
        },
        {
            manual: true,
        }
    );

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title={"事务所"}
                desc={
                    "此版块展示团队的最新动态，帮助会员了解合伙人的专业选择与团队成员信息。同时发布业绩报告，让会员直观掌握团队的最新成果与进展。"
                }
            />

            <Group
                justify="flex-end"
                className="tw-mb-8"
            >
                {/* <CnaButton
                    size="sm"
                    onClick={() =>
                        modals.openConfirmModal({
                            title: "邀请链接",
                            closeOnConfirm: false,
                            labels: { confirm: "复制", cancel: "取消" },
                            onConfirm: () => {
                                navigator.clipboard?.writeText?.(
                                    `你被邀请成为加盟合伙人，邀请链接: \n${location.origin}/team?refer=${inviteToken}`
                                );
                            },
                            groupProps: {
                                className: "tw-flex-row-reverse tw-justify-start",
                            },
                            children: (
                                <Textarea
                                    value={`${location.origin}/team?refer=${inviteToken}`}
                                    autosize
                                    readOnly
                                />
                            ),
                        })
                    }
                >
                    邀请合伙人加入
                </CnaButton> */}
                {/* <Popover
                    classNames={{
                        dropdown: "!tw-w-[400px] tw-max-w-full",
                    }}
                    trapFocus
                    position="bottom"
                    withArrow
                    shadow="md"
                >
                    <Popover.Target>
                        <CnaButton size="sm">邀请合伙人加入</CnaButton>
                    </Popover.Target>
                    <Popover.Dropdown>
                        <Box className="tw-w-full">
                            <TextInput
                                label="邀请链接"
                                value={`${location.origin}/team?refer=${inviteToken}`}
                                rightSection={
                                    <ActionIcon
                                        onClick={() =>
                                            navigator.clipboard.writeText(
                                                `你被邀请成为合伙人，邀请链接: \n${location.origin}/team?refer=${inviteToken}`
                                            )
                                        }
                                    >
                                        <Copy />
                                    </ActionIcon>
                                }
                                readOnly
                            />

                            <Tabs
                                value={getValues("type")}
                                classNames={{
                                    list: "tw-mt-3",
                                }}
                                onChange={(value: "email" | "phone") => {
                                    reset({
                                        type: value,
                                    });
                                }}
                            >
                                <Tabs.List>
                                    <Tabs.Tab value="email">发送邮件</Tabs.Tab>
                                    <Tabs.Tab value="phone">发送短信</Tabs.Tab>
                                </Tabs.List>

                                <Tabs.Panel
                                    value="email"
                                    pt="xs"
                                >
                                    <Controller
                                        control={control}
                                        name="email"
                                        render={({ field, fieldState }) => (
                                            <TextInput
                                                {...field}
                                                label="邀请目标邮箱地址"
                                                placeholder="请输入目标邮箱地址"
                                                error={
                                                    fieldState.error
                                                        ? t(fieldState.error.message, lang)
                                                        : false
                                                }
                                            />
                                        )}
                                    />

                                    <Group className="tw-mt-3">
                                        <CnaButton
                                            size="sm"
                                            onClick={handleSubmit(sendInvite, (i) => {
                                                console.log(i);
                                            })}
                                            loading={loading}
                                            leftSection={<PaperPlaneTilt />}
                                        >
                                            发送邮件
                                        </CnaButton>
                                    </Group>
                                </Tabs.Panel>

                                <Tabs.Panel
                                    value="phone"
                                    pt="xs"
                                >
                                    <Controller
                                        control={control}
                                        name="phone"
                                        render={({ field, fieldState }) => (
                                            <TextInput
                                                {...field}
                                                label="邀请目标电话号码"
                                                placeholder="请输入目标电话号码"
                                                error={
                                                    fieldState.error
                                                        ? t(fieldState.error.message, lang)
                                                        : false
                                                }
                                            />
                                        )}
                                    />

                                    <Group className="tw-mt-3">
                                        <CnaButton
                                            size="sm"
                                            onClick={handleSubmit(sendInvite, (i) => {
                                                console.log(i);
                                            })}
                                            loading={loading}
                                            leftSection={<PaperPlaneTilt />}
                                        >
                                            发送短信
                                        </CnaButton>
                                    </Group>
                                </Tabs.Panel>
                            </Tabs>
                        </Box>
                    </Popover.Dropdown>
                </Popover> */}
            </Group>

            {/* <Stack
                className="tw-border tw-p-5 tw-rounde tw-mb-8"
                gap={5}
            >
                <Text>总业绩金额：352,045,000.00人民币</Text>
                <Text className="tw-mb-10">总合伙人人数：15人</Text>

                <Text>三三制人数：{threeThreeMembers.length}/3人</Text>
                <Progress
                    value={(threeThreeMembers.length / 3) * 100}
                    size={"lg"}
                />
            </Stack> */}

            {team_group !== 0 && (
                <Stack
                    className="tw-border tw-p-5 tw-rounde tw-mb-8"
                    gap={3}
                >
                    <Title
                        order={5}
                        className="tw-mb-2"
                    >
                        三三制合伙人
                    </Title>
                    {threeThreeMembers.map((member) => (
                        <TeamItem
                            key={member.profileID}
                            member={member}
                        />
                    ))}
                    {!threeThreeMembers.length && (
                        <Box className="tw-text-center tw-py-12">
                            <Text>当前没有三三制合伙人</Text>
                        </Box>
                    )}
                </Stack>
            )}

            <Stack
                className="tw-border tw-p-5 tw-rounded tw-min-h-[400px] tw-flex-1"
                gap={3}
            >
                <Title
                    order={5}
                    className="tw-mb-2"
                >
                    管理联盟合伙人
                </Title>
                {teamMembers.map((member) => (
                    <TeamItem
                        key={member.profileID}
                        member={member}
                    />
                ))}
                {!teamMembers.length && (
                    <Box className="tw-text-center tw-py-12">
                        <Text>当前没有联盟合伙人</Text>
                    </Box>
                )}
            </Stack>
        </Stack>
    );
};

export default OfficePage;
