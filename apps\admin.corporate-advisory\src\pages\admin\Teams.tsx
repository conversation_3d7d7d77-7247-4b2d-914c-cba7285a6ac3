import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Plus } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import Profile from "@/components/modals/team/Profile";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";

const columnHelper = createColumnHelper<TTeam>();

const AdminTeamsPage = () => {
    const lang = useSettingStore.use.lang();
    const openModal = useModalStore.use.open();

    const [profile, setProfile] = useState<TTeam | null>(null);

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const tableColumns = [
        columnHelper.accessor("id", {
            header: "三三制编码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("rankName", {
            header: "级别",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("name", {
            header: "合伙人名字",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("email", {
            header: "邮件地址",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("phone", {
            header: "手机号码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("created_at", {
            header: "创建日期",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        }),
    ];

    const handleFetch = useMemoizedFn(async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.team.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    });

    const pageButtons = [
        {
            key: "create",
            label: "创建",
            leftSection: <Plus size={14} />,
            onClick: () => {
                setProfile(null);
                openModal("teamProfileModal");
            },
        },
    ];

    const rowActions = (row) => [
        {
            key: "profile",
            label: "简介信息",
            onClick: () => {
                setProfile(row);
                openModal("teamProfileModal");
            },
        },
        {
            key: "remark",
            label: "备注信息",
            onClick: () => {},
        },
        {
            key: "billing",
            label: "付款记录",
            onClick: () => {},
        },
        {
            key: "report",
            label: "尽调报告",
            onClick: () => {},
        },
    ];

    const refreshTable = useMemoizedFn(() => {
        if (tableRef.current) {
            tableRef.current.refresh();
        }
    });

    const handleUpdateSuccess = useMemoizedFn(() => {
        setProfile(null);
        // 刷新表格数据
        refreshTable();
    });

    const handleCloseSuccess = useMemoizedFn(() => {
        setProfile(null);
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="三三制"
                desc="三三制列表"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                columns={tableColumns as any}
                data={data}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
            />

            <Profile
                profile={profile}
                onUpdateSuccess={handleUpdateSuccess}
                onCloseSuccess={handleCloseSuccess}
            />
        </Stack>
    );
};

export default AdminTeamsPage;
