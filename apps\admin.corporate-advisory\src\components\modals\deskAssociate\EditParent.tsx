import { Modal, Select, Stack } from "@mantine/core";
import ModalFooter from "@/components/common/ModalFooter";
import { useShallow } from "zustand/react/shallow";
import useModalStore from "@/store/modal";
import { useMemoizedFn, useRequest } from "ahooks";
import { Controller } from "react-hook-form";
import { useForm } from "react-hook-form";
import { X } from "@phosphor-icons/react";
import noty from "@code.8cent/react/noty";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { cnaRequest } from "@code.8cent/utils";
import deskApi from "@/apis/desk";

const editParentSchema = z.object({
    pre_id: z
        .string({
            required_error: "请选择上级推荐人",
            invalid_type_error: "请选择上级推荐人",
        })
        .min(1, "请选择上级推荐人"),
    is_team: z
        .string({
            required_error: "请选择是否三三制",
        })
        .min(1, "请选择是否三三制"),
});

const EditParent = ({ onSubmitSuccess }: { onSubmitSuccess: () => void }) => {
    // 获取传入的参数
    const editParentParams = useModalStore((state) => state.modalParams.associatePreNameModal);
    const editProfile = editParentParams?.profile;

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.associatePreNameModal,
            close: state.close,
        }))
    );

    // 获取 pre_id 的选项
    const { data: associates } = useRequest(
        async () => {
            const res = await deskApi.associate.options();
            return res?.data || [];
        },
        {
            refreshDeps: [isVisible],
        }
    );

    const associatesArr = Array.isArray(associates) ? associates : [];
    const preIdOptions = associatesArr.map((associate) => ({
        label: associate.profileName,
        value: associate.profileID.toString(),
        disabled: associate.profileID === editProfile?.profileID,
    }));

    const {
        control,
        handleSubmit,
        formState: { errors },
        reset,
        setValue,
    } = useForm({
        defaultValues: {
            pre_id: "",
            is_team: "",
        },
        resolver: zodResolver(editParentSchema),
    });

    // 在 Modal 打开和数据ok时，再设置值
    useEffect(() => {
        if (isVisible && editProfile?.pre_id) {
            setValue("pre_id", editProfile.pre_id.toString());
        }
    }, [isVisible, editProfile, setValue]);

    // 修改 closeModal，确保关闭时重置
    const closeModal = useMemoizedFn(() => {
        reset({
            pre_id: "",
            is_team: "",
        });
        close("associatePreNameModal");
    });

    const modalFooterButtons = [
        {
            key: "save",
            label: "确定",
            type: "submit" as const,
        },
        {
            key: "close",
            label: "关闭",
            style: "outline",
            leftSection: <X />,
            onClick: closeModal,
        },
    ];

    const onSubmit = useMemoizedFn(async (data: z.infer<typeof editParentSchema>) => {
        try {
            const { result, error } = await cnaRequest(
                `/api/v1/admin/partner/changePre`,
                "POST",
                {
                    profileID: editProfile?.profileID,
                    pre_id: data.pre_id,
                    is_team: data.is_team,
            });

            if (error) {
                noty.error(error.message);
            }

            if (result) {
                noty.success("修改成功");
                onSubmitSuccess();
                closeModal();
            }
        } catch (error) {
            console.error(error);
        }
    });

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title="修改上级推荐人"
            size="lg"
        >
            <form
                onSubmit={handleSubmit(onSubmit, (errors) => {
                    console.log(errors);
                })}
            >
                <Stack mb={40}>
                    <Controller
                        name="pre_id"
                        control={control}
                        render={({ field }) => (
                            <Select
                                {...field}
                                label="上级"
                                placeholder="请选择"
                                withAsterisk
                                data={preIdOptions}
                                error={errors.pre_id?.message as string}
                            />
                        )}
                    />
                    <Controller
                        name="is_team"
                        control={control}
                        render={({ field }) => (
                            <Select
                                {...field}
                                label="是否三三制"
                                placeholder="请选择"
                                withAsterisk
                                data={[
                                    {
                                        label: "非三三制",
                                        value: "0",
                                    },
                                    {
                                        label: "三三制",
                                        value: "1",
                                    },
                                ]}
                                error={errors.is_team?.message as string}
                            />
                        )}
                    />
                </Stack>

                <ModalFooter buttons={modalFooterButtons} />
            </form>
        </Modal>
    );
};

export default EditParent;
