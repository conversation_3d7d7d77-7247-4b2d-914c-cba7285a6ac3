import { Group, Modal, Stack, Textarea } from "@mantine/core";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMemoizedFn } from "ahooks";
import useModalStore from "@/store/modal";
import interviewQuestion from "@/apis/interviewQuestion";
import { useEffect, useState } from "react";
import { CnaButton } from "@code.8cent/react/components";
import noty from "@code.8cent/react/noty";

// 追问问题表单验证模式
const formSchema = z.object({
    next: z.string().min(1, "请输入追问问题内容"),
});

type FormValues = z.infer<typeof formSchema>;

const InterviewQuestionNextForm = ({ refreshNextList }: { refreshNextList: () => void }) => {
    const [loading, setLoading] = useState(false);

    const {
        interviewQuestionNextForm: opened,
        modalParams,
        close,
    } = useModalStore((state) => ({
        interviewQuestionNextForm: state.interviewQuestionNextForm,
        modalParams: state.modalParams,
        close: state.close,
    }));

    // 当前操作的基础问题ID
    const baseId = modalParams?.interviewQuestionNextForm?.baseId as number;
    // 当前编辑的追问问题
    const currentNext = modalParams?.interviewQuestionNextForm?.data as
        | TInterviewQuestionNext
        | undefined;
    const isEdit = !!currentNext?.id;

    // 表单
    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        setValue,
    } = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            next: "",
        },
    });

    // 初始化表单数据
    useEffect(() => {
        if (opened && currentNext) {
            setValue("next", currentNext.next);
        } else {
            reset();
        }
    }, [opened, currentNext, setValue, reset]);

    // 关闭弹窗
    const handleClose = useMemoizedFn(() => {
        close("interviewQuestionNextForm");
        reset();
    });

    // 提交表单
    const onSubmit = useMemoizedFn(async (data: FormValues) => {
        if (!baseId) {
            noty.error("缺少基础问题ID");
            return;
        }

        setLoading(true);
        try {
            let success;
            const params: TStoreOrUpdateInterviewQuestionNextParams = {
                id: isEdit ? currentNext!.id : baseId,
                next: data.next,
            };

            if (isEdit) {
                success = await interviewQuestion.next.update(params);
            } else {
                success = await interviewQuestion.next.store(params);
            }

            if (success) {
                noty.success(isEdit ? "更新成功" : "创建成功");
                handleClose();
                refreshNextList();
            }
        } finally {
            setLoading(false);
        }
    });

    return (
        <Modal
            opened={opened}
            onClose={handleClose}
            title={isEdit ? "编辑追问问题" : "添加追问问题"}
            size="md"
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack gap="md">
                    <Textarea
                        label="问题内容"
                        placeholder="请输入追问问题内容"
                        error={errors.next?.message}
                        required
                        {...register("next")}
                    />

                    <Group
                        justify="flex-end"
                        mt="md"
                    >
                        <CnaButton
                            variant="default"
                            onClick={handleClose}
                        >
                            取消
                        </CnaButton>
                        <CnaButton
                            type="submit"
                            loading={loading}
                        >
                            {isEdit ? "更新" : "保存"}
                        </CnaButton>
                    </Group>
                </Stack>
            </form>
        </Modal>
    );
};

export default InterviewQuestionNextForm;
