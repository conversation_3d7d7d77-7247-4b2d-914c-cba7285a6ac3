import api from "@/apis";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Modal, Stack, TextInput, Select, PasswordInput } from "@mantine/core";
import { useState, useEffect } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";
import { useMemoizedFn } from "ahooks";
import { SHA256 } from "crypto-js";
import { useShallow } from "zustand/react/shallow";

const accountSchema = z
    .object({
        profileRole: z.string().min(1, "请选择用户类型"),
        profileName: z.string().min(1, "请输入用户名"),
        profileEmail: z.string().email("请输入有效的邮箱地址"),
        profileContact: z.string().min(1, "请输入联系电话"),
        profilePassword: z.string().min(1, "请输入密码"),
        profileID: z.string().optional(),
        // 行政部分填写信息
        department_id: z.string().optional(),
        // 律师部分填写信息
        lawyerOffice: z.string().optional(),
        profileAddressCountry: z.string().optional(),
        profileNRIC: z.string().optional(),
    })
    .refine(
        (data) => {
            if (data.profileRole == "3" && !data.lawyerOffice) {
                return false;
            }
            return true;
        },
        {
            message: "请填写律师事务所名称",
            path: ["lawyerOffice"],
        }
    )
    .refine(
        (data) => {
            if (data.profileRole == "3" && !data.profileAddressCountry) {
                return false;
            }
            return true;
        },
        {
            message: "请填写律师国籍信息",
            path: ["profileAddressCountry"],
        }
    )
    .refine(
        (data) => {
            if (data.profileRole == "3" && !data.profileNRIC) {
                return false;
            }
            return true;
        },
        {
            message: "请填写律师身份证号",
            path: ["profileNRIC"],
        }
    )
    .refine(
        (data) => {
            if (data.profileRole == "2") {
                return data.department_id;
            }
            return true;
        },
        {
            message: "请选择部门",
            path: ["department_id"],
        }
    );
// .refine(
//     (data) => {
//         if (!data.profileID) {
//             return !!data.profilePassword;
//         }
//         return true;
//     },
//     {
//         message: "请输入密码",
//         path: ["profilePassword"],
//     }
// );

type AccountForm = z.infer<typeof accountSchema>;

const TYPE_MAP = [
    {
        value: "2",
        label: "行政",
    },
    {
        value: "3",
        label: "律师",
    },
];

const Create = ({
    departments = [],
    onUpdateSuccess = () => {},
}: {
    departments: TDepartment[];
    onUpdateSuccess?: () => void;
}) => {
    const { lang } = useSettingStore();
    const [loading, setLoading] = useState(false);
    const openConfirm = useModalStore.use.openConfirm();

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.accountCreateUpdateModal,
            close: state.close,
        }))
    );

    const modalParams = useModalStore((state) => state.modalParams.accountCreateUpdateModal);
    const account = modalParams?.account || {};

    const {
        handleSubmit,
        formState: { errors },
        control,
        reset,
        watch,
    } = useForm<AccountForm>({
        defaultValues: {
            profileRole: account?.profileRole?.toString() || "",
            profileName: account?.profileName || "",
            profileEmail: account?.profileEmail || "",
            profileContact: account?.profileContact || "",
            profilePassword: account?.profilePassword || "",
            department_id: account?.department_id?.toString() || "",
            lawyerOffice: account?.lawyerOffice || "",
            profileAddressCountry: account?.profileAddressCountry || "",
            profileNRIC: account?.profileNRIC || "",
            profileID: account?.profileID?.toString() || "",
        },
        resolver: zodResolver(accountSchema),
    });

    // 当 account 变化时重置表单
    useEffect(() => {
        if (account && Object.keys(account).length > 0) {
            reset({
                profileRole: account?.profileRole?.toString() || "",
                profileName: account?.profileName || "",
                profileEmail: account?.profileEmail || "",
                profileContact: account?.profileContact || "",
                profilePassword: account?.profilePassword || "",
                department_id: account?.department_id?.toString() || "",
                lawyerOffice: account?.lawyerOffice || "",
                profileAddressCountry: account?.profileAddressCountry || "",
                profileNRIC: account?.profileNRIC || "",
                profileID: account?.profileID?.toString() || "",
            });
        }
    }, [account, reset]);

    const submitForm: SubmitHandler<AccountForm> = useMemoizedFn(async (data) => {
        const postData = {
            ...data,
            type: data.profileRole == "3" ? "3" : "2",
        };

        if (data.profilePassword) {
            postData.profilePassword = SHA256(data.profilePassword).toString();
        }

        setLoading(true);
        try {
            let res = false;
            if (account?.profileID) {
                res = await api.account.update(postData as any, account?.profileID);
            } else {
                res = await api.account.store(postData as any);
            }
            if (res) {
                noty.success("操作成功");
                onUpdateSuccess();
                closeModal();
            }
        } catch (error) {
            noty.error("操作失败，请重试");
        } finally {
            setLoading(false);
        }
    });

    const handleSave = useMemoizedFn(() => {
        openConfirm({
            title: "提示",
            message: "您确定更新账户的信息么？",
            onConfirm: handleSubmit(submitForm),
        });
    });

    const closeModal = useMemoizedFn(() => {
        reset({
            profileRole: "",
            profileName: "",
            profileEmail: "",
            profileContact: "",
            profilePassword: "",
            department_id: "",
            lawyerOffice: "",
            profileAddressCountry: "",
            profileNRIC: "",
            profileID: "",
        });
        close("accountCreateUpdateModal");
    });

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title={account?.profileID ? "更新账户" : "创建账户"}
            size="xl"
        >
            <form
                onSubmit={handleSubmit(handleSave, (error) => {
                    console.log(error);
                })}
            >
                <Stack gap={"lg"}>
                    <Controller
                        control={control}
                        name="profileRole"
                        render={({ field }) => (
                            <Select
                                {...field}
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="请选择用户类型"
                                placeholder="请选择用户类型"
                                data={TYPE_MAP}
                                clearable
                                allowDeselect
                                withCheckIcon
                                onChange={(value) => field.onChange(value || "")}
                                error={errors.profileRole?.message}
                            />
                        )}
                    />
                    {/* 如果是行政，则需要选择部门 */}
                    {watch("profileRole") == "2" && (
                        <Controller
                            control={control}
                            name="department_id"
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="请选择部门"
                                    placeholder="请选择部门"
                                    data={departments.map((item) => ({
                                        value: item.id.toString(),
                                        label: item.name,
                                    }))}
                                    clearable
                                    allowDeselect
                                    withCheckIcon
                                    onChange={(value) => field.onChange(value || "")}
                                    error={errors.department_id?.message}
                                />
                            )}
                        />
                    )}
                    <Controller
                        control={control}
                        name="profileName"
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="用户名"
                                placeholder="输入用户名"
                                {...field}
                                error={errors.profileName?.message}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="profileEmail"
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="用户电子邮件"
                                placeholder="输入用户电子邮件"
                                {...field}
                                error={errors.profileEmail?.message}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="profileContact"
                        render={({ field }) => (
                            <TextInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="用户手机号码"
                                placeholder="输入用户手机号码"
                                {...field}
                                error={errors.profileContact?.message}
                            />
                        )}
                    />
                    <Controller
                        control={control}
                        name="profilePassword"
                        render={({ field }) => (
                            <PasswordInput
                                labelProps={{
                                    className: "profile-form-label",
                                }}
                                label="账户密码"
                                placeholder="输入账户密码"
                                {...field}
                                error={errors.profilePassword?.message}
                            />
                        )}
                    />
                    {/* 如果是律师才显示 */}
                    {watch("profileRole") == "3" && (
                        <>
                            <Controller
                                control={control}
                                name="lawyerOffice"
                                render={({ field }) => (
                                    <TextInput
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        label="律师事务所名称"
                                        placeholder="输入律师事务所名称"
                                        {...field}
                                        error={errors.lawyerOffice?.message}
                                    />
                                )}
                            />
                            <Controller
                                control={control}
                                name="profileNRIC"
                                render={({ field }) => (
                                    <TextInput
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        label="律师身份证号"
                                        placeholder="输入律师身份证号"
                                        {...field}
                                        error={errors.profileNRIC?.message}
                                    />
                                )}
                            />
                            <Controller
                                control={control}
                                name="profileAddressCountry"
                                render={({ field }) => (
                                    <TextInput
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        label="律师国籍"
                                        placeholder="输入律师国籍"
                                        {...field}
                                        error={errors.profileAddressCountry?.message}
                                    />
                                )}
                            />
                        </>
                    )}

                    <ModalFooter
                        buttons={[
                            {
                                key: "save",
                                label: account?.profileID ? "更新" : "保存",
                                style: "outline",
                                type: "submit",
                                loading: loading,
                                leftSection: <Check size={16} />,
                            },
                            {
                                key: "cancel",
                                label: "取消",
                                leftSection: <X size={16} />,
                                onClick: closeModal,
                            },
                        ]}
                    />
                </Stack>
            </form>
        </Modal>
    );
};

export default Create;
