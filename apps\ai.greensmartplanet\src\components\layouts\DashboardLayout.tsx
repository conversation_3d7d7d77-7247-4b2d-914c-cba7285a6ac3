import api from "@/apis";
import useProfileStore from "@/store/profile";
import { useMemoizedFn, useMount } from "ahooks";
import React, { cloneElement, ReactElement, useState } from "react";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import { ActionIcon, Badge, Box, Drawer, Group, Image, ScrollArea, Text } from "@mantine/core";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import {
    User,
    Bell,
    LineSegments,
    Handshake,
    Coin,
    Gear,
    UsersThree,
    IconProps,
    Gift,
    Question,
    SignOut,
    List,
    CalendarDots,
    UsersFour,
    AddressBook,
    AddressBookTabs,
} from "@phosphor-icons/react";
import { cnaRequest } from "@code.8cent/utils";
import { modals } from "@mantine/modals";

const SideBarLinks: {
    name: string;
    path: string;
    icon: ReactElement<IconProps>;
}[] = [
    {
        name: "navigation.setting",
        path: "/member/settings",
        icon: <Gear />,
    },
];

const DashboardLayout: React.FC = () => {
    const [setProfile, setInited] = useProfileStore((state) => [state.setProfile, state.setInited]);

    const [minNavOpened, setMinNavOpened] = useState<boolean>(false);

    const { lang, setUpSetting } = useSettingStore();

    const { pathname } = useLocation();

    const navigate = useNavigate();

    const logout = useMemoizedFn(async () => {
        modals.openConfirmModal({
            title: t("logout.title", lang),
            children: <Text>{t("logout.detail", lang)}</Text>,
            onConfirm: async () => {
                await cnaRequest("/api/v1/login/logout", "POST");
                await window.localForage.removeItem("cna-token");
                navigate("/account/login");
            },
        });
    });

    const toAIAssistant = useMemoizedFn(async () => {
        let { result, error } = await cnaRequest<{ link: string }>("/api/v1/ai", "GET");

        if (result) {
            let url = result.data.link;
            const anchor = document.createElement("a");
            anchor.href = url;
            anchor.target = "_blank";
            anchor.rel = "noopener noreferrer";
            anchor.click();
            anchor.remove();
        }
    });

    useMount(async () => {
        let userProfile = await api.user.getUserProfile();

        if (userProfile) {
            setInited(true);

            setProfile(userProfile);

            setUpSetting({
                lang: userProfile.settingLanguage,
                settingCurrency: userProfile.settingCurrency,
                settingTimeFormat: userProfile.settingTimeFormat,
                settingTimezone: userProfile.settingTimezone,
                settingDateFormat: userProfile.settingDateFormat,
                settingLanguage: userProfile.settingLanguage,
                settingNotifyEmergency: userProfile.settingNotifyEmergency,
                settingNotifyImportanceUpdate: userProfile.settingNotifyImportanceUpdate,
                settingNotifyJoinInvestigate: userProfile.settingNotifyJoinInvestigate,
                settingNotifyRecPrivateMsg: userProfile.settingNotifyRecPrivateMsg,
                settingNotifySafeUpdated: userProfile.settingNotifySafeUpdated,
                settingNotifySuspiciousOperation: userProfile.settingNotifySuspiciousOperation,
                settingNotifySystemUpdate: userProfile.settingNotifySystemUpdate,
            });
        } else {
            navigate("/account/login");
        }
    });

    const NavLinks = () => {
        return (
            <>
                <ScrollArea
                    classNames={{
                        scrollbar: "tw-bg-basic-5",
                        thumb: "tw-bg-basic-3",
                    }}
                    type="hover"
                    scrollbars="y"
                    scrollHideDelay={500}
                    scrollbarSize={6}
                    className="tw-flex-1 tw-py-3 tw-overflow-y-auto tw-overflow-x-hidden tw-w-full"
                >
                    {SideBarLinks.map((link, index) => {
                        const icon = cloneElement(link.icon, {
                            size: 32,
                            weight: "thin",
                        } as IconProps);

                        return (
                            <Box
                                className={`tw-flex tw-px-8 tw-text-sm lg:tw-text-md tw-py-2 lg:tw-py-3 tw-items-center tw-justify-start ${
                                    pathname === link.path
                                        ? "tw-text-gray-50 tw-font-extrabold tw-bg-basic-7"
                                        : "tw-text-gray-400"
                                }`}
                                key={index}
                                component={Link}
                                to={{ pathname: link.path }}
                                onClick={() => {
                                    setMinNavOpened(false);
                                }}
                            >
                                {icon}
                                <Text className="tw-flex-1 tw-text-left tw-ml-6 tw-text-sm">
                                    {t(link.name, lang)}
                                </Text>
                                {/* <Badge
                                    color="red"
                                    className="tw-ml-auto tw-text-[9px]"
                                    size="md"
                                    circle
                                >
                                    99
                                </Badge> */}
                            </Box>
                        );
                    })}
                </ScrollArea>
                <div className="tw-pt-3 tw-pb-4">
                    <div
                        className={`tw-mt-2 tw-flex tw-px-4 tw-items-center tw-text-gray-200 tw-cursor-pointer`}
                        onClick={logout}
                    >
                        <SignOut size={20} />
                        <Text className="tw-text-sm tw-ml-4">{t("navigation.log_out", lang)}</Text>
                    </div>
                </div>
            </>
        );
    };

    return (
        <div className="tw-flex tw-h-[100vh] tw-w-[100vw] tw-flex-col md:tw-flex-row">
            {/*
                This is the side navigation bar on the left side of the screen.
                It is hidden on small screens and shows up on larger screens.
                It contains a logo, a heading, and a list of links.
            */}
            <div className="md:tw-w-[200px] tw-transition-all md:tw-flex lg:tw-w-[240px] tw-border-r tw-bg-basic-5 tw-hidden tw-flex-col tw-w-0">
                <div>
                    <Image
                        src="/images/navbar-logo.png"
                        w={"100%"}
                        className="tw-mx-auto"
                    />
                    {/* <Text className="tw-text-center tw-mt-3 tw-text-xl">
                        {t("dashboard.title", lang)}
                    </Text> */}
                </div>
                <NavLinks />
            </div>
            {/*
                This is the top navigation bar that shows up on small screens.
                It is hidden on larger screens and shows up on smaller screens.
                It contains a logo and a button to open or close the side navigation bar.
            */}
            <Group
                className="md:tw-hidden tw-py-2 tw-border-b tw-px-6 tw-bg-basic-5"
                justify="space-between"
            >
                <Image
                    src="/images/navbar-logo-sm.png"
                    w={"60%"}
                />

                <ActionIcon
                    variant="transparent"
                    color="white"
                    onClick={() => {
                        setMinNavOpened(true);
                    }}
                >
                    <List size={36} />
                </ActionIcon>
            </Group>
            {/*
                This is the main content area of the page. It will display
                the content of the currently selected page. It takes up
                the majority of the screen, and is scrollable if the content
                is too large to fit on the screen.
            */}
            <div className="tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden">
                <Outlet />
            </div>
            {/*
                This is the Drawer component from Mantine.
                It is only visible on small screens, and is used
                to display the side navigation bar when the user
                clicks on the hamburger button on the top right
                of the screen.
            */}
            <Drawer
                opened={minNavOpened}
                onClose={() => {
                    setMinNavOpened(false);
                }}
                size={"80%"}
                className="md:tw-hidden"
                classNames={{
                    content: "tw-bg-basic-5 tw-flex tw-flex-col",
                    header: "tw-py-0 tw-bg-basic-5",
                    body: "tw-flex-1 tw-flex tw-flex-col",
                    close: "tw-text-gray-50 hover:tw-bg-basic-6",
                }}
                title={
                    <Group className="tw-py-2">
                        <Image
                            src="/images/navbar-logo-sm.png"
                            w={"80%"}
                        />
                    </Group>
                }
            >
                <NavLinks />
            </Drawer>
        </div>
    );
};

export default DashboardLayout;
