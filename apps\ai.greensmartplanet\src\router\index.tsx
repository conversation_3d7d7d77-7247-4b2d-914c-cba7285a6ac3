import { createBrowser<PERSON>out<PERSON>, json, Navigate } from "react-router-dom";
import RouteError from "@code.8cent/react/components/RouteError";
import RootLayout from "@/components/layouts/RootLayout";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import LoginPage from "@code.8cent/react/pages/LoginPage";
import ForgetPasswordPage from "@code.8cent/react/pages/ForgetPasswordPage";
import SettingPage from "@code.8cent/react/pages/SettingPage";
import withRouteGuard from "@code.8cent/react/hoc/withRouteGuard";

const GuardedRootLayout = withRouteGuard(RootLayout, {
    publicRoutes: ["/account/*"]
});

const router = createBrowserRouter([
    {
        path: "/",
        element: <GuardedRootLayout />,
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        children: [
            {
                path: "account/login",
                element: <LoginPage />,
            },
            {
                path: "account/forget-password",
                element: <ForgetPasswordPage />,
            },
            {
                path: "member",
                element: <DashboardLayout />,
                children: [
                    {
                        path: "settings",
                        element: <SettingPage />,
                    },
                    {
                        path: "*",
                        errorElement: <RouteError className="tw-h-[100%] tw-w-[100%]" />,
                        loader: () => {
                            throw json({}, { status: 404, statusText: "Page Not Found" });
                        },
                    },
                ],
            },
        ],
    },
    {
        path: "*",
        errorElement: <RouteError className="tw-h-[100vh] tw-w-[100vw]" />,
        loader: () => {
            throw json({}, { status: 404, statusText: "Page Not Found" });
        },
    },
]);

export default router;
