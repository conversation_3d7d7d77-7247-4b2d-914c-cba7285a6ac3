import api from "@/apis";
import useProfileStore from "@/store/profile";
import { ActionIcon, AspectRatio, Image } from "@mantine/core";
import { <PERSON>Notch, <PERSON>cilSimpleLine, Spinner, SpinnerGap } from "@phosphor-icons/react";
import React, { useState } from "react";

const ProfileAvatar: React.FC<{ allowUpload?: boolean }> = ({
    allowUpload = true,
}) => {
    const profileAvatar = useProfileStore.use.profileAvatar();

    const setProfileValue = useProfileStore.use.setProfileValue();

    const [uploading, setUploading] = useState<boolean>(false);

    const uploadAvatar = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files) return;

        setUploading(true);

        let files = e.target.files;

        let updatedAvatarUrl = await api.user.updateAvatar(files[0]);

        if (updatedAvatarUrl) {
            setProfileValue("profileAvatar", updatedAvatarUrl);
        }

        e.target.value = "";

        setUploading(false);
    };

    return (
        <AspectRatio
            ratio={1}
            className="tw-max-w-[150px] tw-mx-auto tw-relative"
        >
            <label className="tw-block tw-relative" htmlFor="avatarUploader">
                <Image
                    className="tw-w-full tw-h-full tw-rounded-full tw-border"
                    fit="contain"
                    src={
                        profileAvatar?.length > 0
                            ? `${window.api_base_url}${profileAvatar}`
                            : "/images/default-user.svg"
                    }
                    alt="User Profile Picture"
                />

                {uploading === false && allowUpload === true && (
                    <input
                        className="tw-hidden"
                        type="file"
                        name="avatarUploader"
                        id="avatarUploader"
                        accept="image/jpeg, image/png"
                        onChange={uploadAvatar}
                    />
                )}
                {uploading === true && (
                    <div className="tw-w-full tw-top-0 tw-bottom-0 tw-rounded-full tw-h-full tw-flex tw-absolute tw-opacity-75 tw-bg-neutral-600 tw-items-center tw-justify-center">
                        <Spinner color="#fff" size={36} className="tw-animate-[spin_2s_linear_infinite]" />
                    </div>
                )}
                <ActionIcon
                    component="label"
                    htmlFor="avatarUploader"
                    variant="filled"
                    color="gray.1"
                    size="md"
                    className="tw-absolute tw-bottom-0 tw-right-0 tw-rounded-full"
                    c="dark"
                >
                    <PencilSimpleLine />
                </ActionIcon>
            </label>
        </AspectRatio>
    );
};

export default ProfileAvatar;
