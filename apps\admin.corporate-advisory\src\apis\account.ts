import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const account = {
    list: async (params: TAccountSearchParams, type: string) => {
        const { error, result } = await cnaRequest<TAccountsResponse>(
            `/api/v1/admin/account/index/${type}`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 创建
    store: async (params: UserProfileResponse) => {
        const { error, result } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/account/create",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 更新
    update: async (params: UserProfileResponse, id: number) => {
        const { error, result } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/account/edit/${id}`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },

    // 根据用户ID获取用户权限
    permissions: async (id: number) => {
        const { error, result } = await cnaRequest(
            `/api/v1/admin/power/index?user_id=${id}`,
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 更新用户权限
    updatePermissions: async (data: TStorePermissionParams) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/power/savePower",
            "POST",
            data
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 登录用户的权限
    loginedPermissions: async () => {
        const { error, result } = await cnaRequest<BaseApiResponse & { powerCode: string[] }>(
            "/api/v1/admin/power/userPower",
            "GET"
        );

        if (!error) {
            return result.data.powerCode;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 重置密码 or 修改密码
    updatePassword: async (params: {
        id: number;
        password: string;
        newPassword: string;
        confirmPassword: string;
    }) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/account/updatePassword",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 冻结、解冻 or 删除
};

export default account;
