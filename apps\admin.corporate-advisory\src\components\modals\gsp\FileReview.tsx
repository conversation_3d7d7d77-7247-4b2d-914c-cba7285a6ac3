import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import { Modal, Select, Stack, Textarea } from "@mantine/core";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Checks, X } from "@phosphor-icons/react";
import { SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { t } from "@code.8cent/i18n";
import api from "@/apis";

const schema = z.object({
    status: z.string().min(1, "请选择审核结果"),
});

type FormValues = z.infer<typeof schema>;

const GspFileReview = ({ onSubmitSuccess }: { onSubmitSuccess: () => void }) => {
    const { lang } = useSettingStore();

    const modalParams = useModalStore((state) => state.modalParams.gspReportFileReviewModal);
    const fileId = modalParams?.fileId;

    const statusOptions = [
        { value: "1", label: "通过" },
        { value: "2", label: "驳回" },
    ];

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.gspReportFileReviewModal,
            close: state.close,
        }))
    );

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
    } = useForm<FormValues>({
        defaultValues: {
            status: "",
        },
        resolver: zodResolver(schema),
    });

    const closeModal = useMemoizedFn(() => {
        reset();
        close("gspReportFileReviewModal");
    });

    const onSubmit: SubmitHandler<FormValues> = async (data) => {
        try {
            const reviewParams = {
                status: Number(data.status),
            };

            return false;
            // todo
            closeModal();
            onSubmitSuccess();
        } catch (error) {
            console.error(error);
        }
    };

    const modalFooterButtons = [
        {
            key: "submit",
            label: "提交",
            leftSection: <Checks />,
            onClick: handleSubmit(onSubmit),
        },
        {
            key: "close",
            label: "关闭",
            leftSection: <X />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    return (
        <Modal
            title="尽调文件审核"
            opened={show}
            onClose={closeModal}
        >
            <Stack>
                <Select
                    label="审核状态"
                    data={statusOptions}
                    error={errors.status?.message}
                    {...register("status")}
                    onChange={(value) => setValue("status", value || "")}
                />
            </Stack>

            <div className="tw-mt-10">
                <ModalFooter buttons={modalFooterButtons} />
            </div>
        </Modal>
    );
};

export default GspFileReview;
