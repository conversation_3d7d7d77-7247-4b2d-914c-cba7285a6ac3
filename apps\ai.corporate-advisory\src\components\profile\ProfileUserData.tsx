import api from "@/apis";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Grid, Input, Select, TextInput } from "@mantine/core";
import { useListState } from "@mantine/hooks";
import { useRequest, useDebounceFn } from "ahooks";
import React, { useEffect, useCallback } from "react";

type ProfileUserDataProps = {
    dataKey: "professional" | "skill" | "experience";
    value: any[];
    onChange?: (value: any[]) => void;
    readOnly?: boolean;
};

const ProfileUserData: React.FC<ProfileUserDataProps> = React.memo(
    ({ dataKey, value, onChange, readOnly = false }) => {
        const lang = useSettingStore.use.lang();

        const [_value, { setState, setItem }] = useListState(value);

        // Fetch data once, caching the results
        const { data: selectDatas = [] } = useRequest(
            async () => {
                let key = `${dataKey}s`;
                return await api.profile.getInfoSelectList(key);
            },
            {
                cacheKey: `cache-${dataKey}s`,
                staleTime: 60 * 60 * 1000,
            }
        );

        // Use debounced function for onChange to delay updates to external state
        const { run: debouncedOnChange } = useDebounceFn(
            (updatedValue: any[]) => {
                if (typeof onChange === "function") {
                    onChange(updatedValue);
                }
            },
            { wait: 500 }
        );

        // Sync with external value changes
        useEffect(() => {
            setState(value);
        }, [value, setState]);

        // Call debounced onChange whenever _value changes
        useEffect(() => {
            debouncedOnChange(_value);
        }, [_value, debouncedOnChange]);

        const handleSelectChange = useCallback(
            (index: number, selectedValue: string) => {
                setItem(index, {
                    ..._value[index],
                    [`${dataKey}ID`]: selectedValue,
                });
            },
            [_value, dataKey, setItem]
        );

        const handleTextChange = useCallback(
            (index: number, text: string) => {
                setItem(index, {
                    ..._value[index],
                    [`${dataKey}Description`]: text,
                });
            },
            [_value, dataKey, setItem]
        );

        return (
            <Input.Wrapper
                label={t(`introduction.label.${dataKey}`, lang)}
                labelProps={{ className: "profile-form-label" }}
            >
                {Array.from({ length: 3 }).map((_, index) => {
                    const selectdValue = String(
                        _value?.[index]?.[`${dataKey}ID`] ?? ""
                    );

                    const stringValue = String(
                        _value?.[index]?.[`${dataKey}Description`] ?? ""
                    );

                    if (readOnly === true && selectdValue === "") {
                        return null;
                    }

                    return (
                        <Grid gutter={3} key={`${dataKey}_${index}`}>
                            <Grid.Col
                                span={{ base: 12, md: 6 }}
                                className="tw-mb-3"
                            >
                                <Select
                                    placeholder={`${t(
                                        "introduction.label.select",
                                        lang
                                    )} ${t(
                                        `introduction.label.${dataKey}`,
                                        lang
                                    )}`}
                                    searchable
                                    allowDeselect={false}
                                    value={selectdValue}
                                    data={selectDatas.map((data) => ({
                                        value: `${data[`${dataKey}ID`]}`,
                                        label: `${
                                            data[`${dataKey}Title${lang}`]
                                        }`,
                                    }))}
                                    onChange={(e) =>
                                        handleSelectChange(index, e)
                                    }
                                    error={
                                        stringValue !== "" &&
                                        selectdValue === ""
                                            ? true
                                            : false
                                    }
                                    readOnly={readOnly}
                                />
                            </Grid.Col>
                            <Grid.Col
                                span={{ base: 12, md: 6 }}
                                className="tw-mb-3"
                            >
                                <TextInput
                                    value={stringValue}
                                    placeholder={`${t(
                                        "introduction.label.input",
                                        lang
                                    )} ${t(
                                        `introduction.label.${dataKey}`,
                                        lang
                                    )}`}
                                    onChange={(e) =>
                                        handleTextChange(index, e.target.value)
                                    }
                                    error={
                                        selectdValue !== "" &&
                                        stringValue === ""
                                            ? true
                                            : false
                                    }
                                    readOnly={readOnly}
                                />
                            </Grid.Col>
                        </Grid>
                    );
                })}
            </Input.Wrapper>
        );
    }
);

export default ProfileUserData;
