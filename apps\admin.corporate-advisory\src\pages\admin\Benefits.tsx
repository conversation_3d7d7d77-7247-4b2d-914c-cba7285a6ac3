import api from "@/apis";
import { Space, Stack, Tabs } from "@mantine/core";
import { useMemoizedFn, useMount } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { GearSix, Plus } from "@phosphor-icons/react";
import { useLocation } from "react-router-dom";
import PageActionButtons from "@/components/common/PageActionButtons";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import Info from "@/components/modals/benefit/Info";
import dayjs from "dayjs";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";

const benefitColumnHelper = createColumnHelper<TBenefit>();
const appColumnHelper = createColumnHelper<
    TBenefitApplication & { companyName: string; companyRegisterCode: string }
>();

// 添加 tab 类型定义和常量
type TabValue = "benefits" | "applications";

const TAB_OPTIONS = [
    { value: "benefits", label: "福利列表" },
    { value: "applications", label: "申请列表" },
] as const;

const AdminBenefitsPage = () => {
    const lang = useSettingStore.use.lang();
    const location = useLocation();

    const openModal = useModalStore.use.open();

    const { profileID } = location.state || {};

    const [tab, setTab] = useState<TabValue>("benefits");

    const [benefit, setBenefit] = useState<TBenefit | null>(null);

    const benefitTableRef = useRef<DataTableRef | null>(null);
    const [benefitData, setBenefitData] = useState([]);
    const [benefitTotalCount, setBenefitTotalCount] = useState(0);
    const [benefitLoading, setBenefitLoading] = useState(false);

    const appTableRef = useRef<DataTableRef | null>(null);
    const [appData, setAppData] = useState([]);
    const [appTotalCount, setAppTotalCount] = useState(0);
    const [appLoading, setAppLoading] = useState(false);

    // 定义状态值常量
    const APP_STATUS_MAP = [
        { value: "0", color: "yellow", label: "待审核" },
        { value: "1", color: "green", label: "已通过" },
        { value: "2", color: "red", label: "已驳回" },
    ];

    const appColumns = [
        appColumnHelper.accessor("benefitXID", {
            header: "申请编码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        appColumnHelper.accessor("benefitStatus", {
            header: "状态",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        appColumnHelper.accessor("benefitID", {
            header: "福利项目",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        appColumnHelper.accessor("userID", {
            header: "申请人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        appColumnHelper.accessor("companyName", {
            header: "申请人身份证",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        appColumnHelper.accessor("companyRegisterCode", {
            header: "申请人编码",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        appColumnHelper.accessor("createTime", {
            header: "申请日期",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        appColumnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={appRowActions(info.row.original)} />,
        }),
    ];

    const benifitColumns = [
        benefitColumnHelper.accessor("benefitID", {
            header: "福利编码",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        benefitColumnHelper.accessor(`benefitTitle${lang}`, {
            header: "标题",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        benefitColumnHelper.accessor(`benefitDescription${lang}`, {
            header: "描述",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        benefitColumnHelper.accessor("createUser", {
            header: "创建人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        benefitColumnHelper.accessor("createTime", {
            header: "创建时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        benefitColumnHelper.accessor("editTime", {
            header: "更新时间",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD HH:mm:ss"),
        }),
        benefitColumnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={benefitRowActions(info.row.original)} />,
        }),
    ];

    const handleBenefitFetch = async (params) => {
        setBenefitLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.benefit.list(requestParams);
            setBenefitData(items || []);
            setBenefitTotalCount(paginate?.total || 0);
        } finally {
            setBenefitLoading(false);
        }
    };

    const handleAppFetch = async (params) => {
        setAppLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await api.benefit.applications(requestParams);
            setAppData(items || []);
            setAppTotalCount(paginate?.total || 0);
        } finally {
            setAppLoading(false);
        }
    };

    useMount(() => {
        if (profileID) {
            // todo 筛选特定合伙人公司
            console.log("profileID", profileID);
        }
    });

    const pageButtons = [
        {
            key: "add",
            label: "新增",
            leftSection: <Plus size={14} />,
            onClick: () => openModal("benefitInfoModal"),
        },
        {
            key: "setting",
            label: "设置",
            leftSection: <GearSix size={14} />,
            onClick: () => {},
        },
    ];

    const benefitRowActions = (row) => [
        {
            key: "profile",
            label: "简介信息",
            onClick: () => {
                setBenefit(row);
                openModal("benefitInfoModal");
            },
        },
    ];

    const appRowActions = (row) => [
        {
            key: "profile",
            label: "简介信息",
            onClick: () => {
                openModal("clientProfileModal");
            },
        },
    ];

    const refreshBenefitTable = useMemoizedFn(() => {
        if (benefitTableRef.current) {
            benefitTableRef.current.refresh();
        }
    });

    const refreshAppTable = useMemoizedFn(() => {
        if (appTableRef.current) {
            appTableRef.current.refresh();
        }
    });

    const handleInfoModalClose = useMemoizedFn(() => {
        setBenefit(null);
    });

    const handleInfoModalUpdateSuccess = useMemoizedFn(() => {
        setBenefit(null);
        refreshBenefitTable();
    });

    const handleUpdateSuccess = useMemoizedFn(() => {});

    const handleCloseSuccess = useMemoizedFn(() => {});

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="福利"
                desc="查询福利"
            />

            {tab === "benefits" && (
                <PageActionButtons
                    buttons={pageButtons}
                    className="tw-fixed tw-right-6"
                />
            )}

            <Tabs
                value={tab}
                onChange={(value) => setTab(value as TabValue)}
            >
                <Tabs.List>
                    {TAB_OPTIONS.map(({ value, label }) => (
                        <Tabs.Tab
                            key={value}
                            value={value}
                            className={tab === value ? "tw-bg-basic-5 tw-text-white" : ""}
                        >
                            {label}
                        </Tabs.Tab>
                    ))}
                </Tabs.List>

                <Tabs.Panel value="benefits">
                    <Space h={10} />
                    <DataTable
                        ref={benefitTableRef}
                        data={benefitData}
                        columns={benifitColumns as any}
                        totalCount={benefitTotalCount}
                        loading={benefitLoading}
                        onFetch={handleBenefitFetch}
                        globalFilterFields={[
                            {
                                field: "keyword",
                                label: "搜索关键字",
                                type: "text",
                            },
                            {
                                field: "status",
                                label: "状态",
                                type: "select",
                                options: APP_STATUS_MAP.map((item) => ({
                                    value: item.value,
                                    label: item.label,
                                })),
                            },
                        ]}
                    />

                    <Info
                        benefit={benefit}
                        onClose={handleInfoModalClose}
                        onUpdateSuccess={handleInfoModalUpdateSuccess}
                    />
                </Tabs.Panel>
                <Tabs.Panel value="applications">
                    <Space h={10} />
                    <DataTable
                        ref={appTableRef}
                        data={appData}
                        columns={appColumns as any}
                        totalCount={appTotalCount}
                        loading={appLoading}
                        onFetch={handleAppFetch}
                        globalFilterFields={[
                            {
                                field: "keyword",
                                label: "搜索关键字",
                                type: "text",
                            },
                            {
                                field: "status",
                                label: "状态",
                                type: "select",
                                options: APP_STATUS_MAP.map((item) => ({
                                    value: item.value,
                                    label: item.label,
                                })),
                            },
                        ]}
                    />
                </Tabs.Panel>
            </Tabs>
        </Stack>
    );
};

export default AdminBenefitsPage;
