import { useEffect, useState } from "react";
import { Grid, GridColProps, Input, TextInput } from "@mantine/core";
import CountrySelect from "./CountrySelect";
import { useDebounce, useDebounceFn } from "ahooks";

type AddressFieldKeys = "unit" | "street" | "city" | "state" | "postcode" | "country" | "district";

type AddressInputProps<TAddressData = any, TCountryData = any> = {
    readOnly?: boolean;
    addressData: TAddressData;
    addressFieldMap: {
        [key in AddressFieldKeys]: {
            placeholder?: string;
            key?: keyof TAddressData;
        };
    };
    errors?: {
        [key in AddressFieldKeys]: boolean;
    };
    label?: string;
    wrapperProps?: Omit<React.ComponentProps<typeof Input.Wrapper>, "children" | "label">;
    countrySelectProps: Omit<
        React.ComponentProps<typeof CountrySelect<TCountryData>>,
        "value" | "onChange" | "searchable" | "placeholder"
    >;
    onAddressDataChange?: (addressData: TAddressData) => void;
};

const AddressInput = <TAddressData = any, TCountryData = any>({
    label,
    addressFieldMap,
    wrapperProps,
    countrySelectProps,
    addressData,
    onAddressDataChange,
    errors,
    readOnly,
}: AddressInputProps<TAddressData, TCountryData>) => {
    // 使用本地state来管理输入值
    const [localValues, setLocalValues] = useState<{
        [key in keyof TAddressData]: string;
    }>({} as { [key in keyof TAddressData]: string });

    const debouncedLocalValues = useDebounce(localValues, { wait: 200 });

    useEffect(() => {
        typeof onAddressDataChange === "function" &&
            onAddressDataChange(debouncedLocalValues as TAddressData);
    }, [debouncedLocalValues]);

    const [addressFields] = useState<
        {
            id: AddressFieldKeys;
            placeholder: string;
            key: keyof TAddressData;
            span: GridColProps["span"];
        }[]
    >([
        {
            id: "unit",
            placeholder: addressFieldMap.unit.placeholder,
            key: addressFieldMap.unit.key,
            span: { base: 12, md: 6 },
        },
        {
            id: "street",
            placeholder: addressFieldMap.street.placeholder,
            key: addressFieldMap.street.key,
            span: { base: 12, md: 6 },
        },
        {
            id: "district",
            placeholder: addressFieldMap.district.placeholder,
            key: addressFieldMap.district.key,
            span: { base: 12, md: 6 },
        },
        {
            id: "city",
            placeholder: addressFieldMap.city.placeholder,
            key: addressFieldMap.city.key,
            span: { base: 12, md: 6 },
        },
        {
            id: "state",
            placeholder: addressFieldMap.state.placeholder,
            key: addressFieldMap.state.key,
            span: { base: 12 },
        },
        {
            id: "postcode",
            placeholder: addressFieldMap.postcode.placeholder,
            key: addressFieldMap.postcode.key,
            span: { base: 12, md: 6 },
        },
        {
            id: "country",
            placeholder: addressFieldMap.country.placeholder,
            key: addressFieldMap.country.key,
            span: { base: 12, md: 6 },
        },
    ]);

    return (
        <Input.Wrapper
            label={label}
            {...wrapperProps}
        >
            <Grid gutter={3}>
                {addressFields.map((field, index) => (
                    <Grid.Col
                        span={field.span}
                        key={field.id}
                        className="tw-mb-3"
                    >
                        {field.id === "country" ? (
                            <CountrySelect<TCountryData>
                                {...countrySelectProps}
                                placeholder={field.placeholder}
                                searchable={true}
                                value={
                                    (localValues[field.key] as string) ??
                                    (addressData[field.key] as string) ??
                                    ""
                                }
                                onChange={(value) => {
                                    setLocalValues((prev) => ({
                                        ...prev,
                                        [field.key]: value,
                                    }));
                                }}
                                error={errors?.[field.id] ?? false}
                                readOnly={countrySelectProps.readOnly ?? readOnly}
                            />
                        ) : (
                            <TextInput
                                placeholder={field.placeholder}
                                value={
                                    (localValues[field.key] as string) ??
                                    (addressData[field.key] as string) ??
                                    ""
                                }
                                onChange={(e) => {
                                    // 立即更新本地状态
                                    setLocalValues((prev) => ({
                                        ...prev,
                                        [field.key]: e.target.value,
                                    }));
                                }}
                                error={errors?.[field.id] ?? false}
                                readOnly={readOnly}
                            />
                        )}
                    </Grid.Col>
                ))}
            </Grid>
        </Input.Wrapper>
    );
};

export default AddressInput;
