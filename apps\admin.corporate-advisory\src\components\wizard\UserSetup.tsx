import api from "@/apis";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import { useEventBus } from "@/utils/eventBus";
import { zodResolver } from "@hookform/resolvers/zod";
import { Stack, Center, Group, Text, Image, AspectRatio } from "@mantine/core";
import { Dropzone, MIME_TYPES } from "@mantine/dropzone";
import { Check, X, UserCircle, FileImage } from "@phosphor-icons/react";
import { useMount, useRequest, useUnmount } from "ahooks";
import { filesize } from "filesize";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

type UserSetupFormInput = {
    file: File | null;
};

const accept = [MIME_TYPES.jpeg, MIME_TYPES.png] as string[];

const required = true;

const maxSize = 1024 * 1024 * 2;
const UserSetup = () => {
    const bus = useEventBus();

    const lang = useSettingStore.use.lang();

    const {setState: setWizardState} = useWizardStore();

    const {
        handleSubmit,
        setValue,
        formState: { errors },
        getValues,
    } = useForm<UserSetupFormInput>({
        defaultValues: {
            file: null,
        },
        resolver: zodResolver(
            z.object({
                file: z
                    .custom<File>((v) => v instanceof File)
                    .refine((file) => file.size <= maxSize, {})
                    .refine((file) => accept.includes(file.type), {}),
            })
        ),
    });

    const { run: setupProfile, loading } = useRequest(
        async (data: UserSetupFormInput) => {
            let res = await api.user.setProfile(data.file);

            if (res) {
                // await window.localForage.setItem("cna-token", res.token);

                setWizardState(4);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        bus.emit("wizard.submitting", loading);
    }, [loading]);

    useMount(() => {
        bus.on("wizard.submit.click", handleSubmit(setupProfile));
    });

    useUnmount(() => {
        bus.emit("wizard.submitting", false);
        bus.off("wizard.submit.click");
    });

    return (
        <Stack>
            <Center>
                {getValues("file") ? (
                    <AspectRatio
                        ratio={1}
                        className="tw-max-w-[150px] tw-mx-auto tw-relative"
                    >
                        <Image
                            className="tw-w-full tw-h-full tw-rounded-full tw-border"
                            fit="contain"
                            src={URL.createObjectURL(getValues("file"))}
                            alt="User Profile Picture"
                        />
                    </AspectRatio>
                ) : (
                    <UserCircle
                        size={150}
                        weight="light"
                        className="tw-text-[#666]"
                    />
                )}
            </Center>

            <Dropzone
                onDrop={(_files) => {
                    setValue("file", _files[0], { shouldValidate: true });
                }}
                multiple={false}
                accept={accept}
                className={`${
                    errors?.file && "!tw-border-[var(--mantine-color-red-6)]"
                }`}
            >
                <Center>
                    <Stack>
                        <FileImage
                            size={36}
                            className="tw-text-dimmed tw-mx-auto tw-mb-3"
                        />
                        <Text size="sm" c="dimmed">
                            {getValues("file")
                                ? getValues("file").name
                                : t("upload.files", lang)}
                        </Text>
                    </Stack>
                </Center>
            </Dropzone>

            <Stack gap={2}>
                {required === true && (
                    <Group gap={4}>
                        {getValues("file") instanceof File === true ? (
                            <Check size={24} className="tw-text-green-600" />
                        ) : (
                            <X size={24} className="tw-text-red-600" />
                        )}
                        <Text size="sm" c="dimmed">
                            {t("upload.files.requirement", lang)}
                        </Text>
                    </Group>
                )}
                <Group gap={4}>
                    {getValues("file") instanceof File === true &&
                    getValues("file").size <= maxSize ? (
                        <Check size={24} className="tw-text-green-600" />
                    ) : (
                        <X size={24} className="tw-text-red-600" />
                    )}
                    <Text size="sm" c="dimmed">
                        {t("upload.files.exceed", lang)}
                        {filesize(maxSize, { standard: "jedec" })}
                    </Text>
                </Group>
                <Group gap={4}>
                    {getValues("file") instanceof File === true &&
                    accept.includes(getValues("file").type) === true ? (
                        <Check size={24} className="tw-text-green-600" />
                    ) : (
                        <X size={24} className="tw-text-red-600" />
                    )}
                    <Text size="sm" c="dimmed">
                        {t("upload.files.format", lang)}
                        {accept.join(" ")}
                    </Text>
                </Group>
            </Stack>
        </Stack>
    );
};

export default UserSetup;
