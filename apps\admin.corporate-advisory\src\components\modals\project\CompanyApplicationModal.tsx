import api from "@/apis";
import CnaButton from "@code.8cent/react/components/CnaButton";
import CompanyAccountSetting from "@/components/company/application/AccountSetting";
import CompanyApplicationComplete from "@/components/company/application/ApplicationComplete";
import CompanyApplicationStepper from "@/components/company/application/ApplicationStepper";
import CompanyDocumentSubmission from "@/components/company/application/DocumentSubmission";
import CompanyPaymentSubmission from "@/components/company/application/PaymentSubmission";
import CompanyPreliminarySubmission from "@/components/company/application/PreliminarySubmission";
import { CompanyApplicationContext } from "@/contexts/project";
import useCompanyApplicationFormEnabled from "@/hooks/project/useCompanyApplicationFormEnabled";
import useModalStore from "@/store/modal";
import useSettingStore from "@code.8cent/store/setting";
import { useEventBus, useListener } from "@/utils/eventBus";
import { Button, Group, Modal, Stack } from "@mantine/core";
import { Check } from "@phosphor-icons/react";
import {
    useMemoizedFn,
    useMount,
    useRequest,
    useUnmount,
    useUpdateEffect,
} from "ahooks";
import { useState } from "react";
import { boolean } from "zod";
import { t } from "@code.8cent/i18n";

const ApplicationRenderWidget: React.FC<{ step: number }> = ({ step }) => {
    switch (step) {
        case 1: {
            return <CompanyPreliminarySubmission />;
        }

        case 2: {
            return <CompanyPaymentSubmission />;
        }

        case 3: {
            return <CompanyAccountSetting />;
        }

        case 4: {
            return <CompanyDocumentSubmission />;
        }

        case 5: {
            return <CompanyApplicationComplete />;
        }

        default: {
            return null;
        }
    }
};

const CompanyApplicationModal: React.FC<{ companyID?: number }> = ({
    companyID,
}) => {
    const [step, setStep] = useState<number>(0);

    const [ready, setReady] = useState(false);

    const [submitEnabled, setSubmitEnabled] = useState<boolean>(false);

    const [submitting, setSubmitting] = useState<boolean>(false);

    const bus = useEventBus();

    const lang = useSettingStore.use.lang();

    const opened = useModalStore.use.projectCompanyApplication();

    const close = useModalStore.use.close();

    const { data: companyInfo, mutate } = useRequest(
        async () => {
            let companyInfo = await api.project.getCompanyInfo(companyID);

            // companyInfo = { ...companyInfo, companyProgressState: 5 };

            return companyInfo as Partial<CompanyApplictaionInfo>;
        },
        {
            ready,
        }
    );

    const onEnableUpdate = useMemoizedFn((enabled: boolean) => {
        console.log("form enable: ", enabled);
        setSubmitEnabled(enabled);
    });

    const onApplicationSubmitting = useMemoizedFn((submitting: boolean) => {
        setSubmitting(submitting);
    });

    useUpdateEffect(() => {
        if (typeof companyID === "number" && opened === true) {
            setReady(true);
        } else {
            setReady(false);
            mutate({});
        }
    }, [companyID, opened]);

    useUpdateEffect(() => {
        if (companyInfo) {
            setStep(companyInfo.companyProgressState);
        }
    }, [companyInfo]);

    useMount(() => {
        bus.on("project.company.application.submit.enabled", onEnableUpdate);
        bus.on(
            "project.company.application.submitting",
            onApplicationSubmitting
        );
        bus.on("project.company.application.close", () => {
            close("projectCompanyApplication");
        });
    });

    useUnmount(() => {
        bus.off("project.company.application.submit.enabled");
        bus.off("project.company.application.submitting");
        bus.off("project.company.application.close");
    });

    return (
        <Modal
            size="xl"
            opened={opened}
            onClose={() => close("projectCompanyApplication")}
            title={companyInfo?.companyName}
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <CompanyApplicationContext.Provider value={{ companyInfo }}>
                <Stack className="tw-my-5" gap={10}>
                    <CompanyApplicationStepper
                        step={step}
                        onStepChange={(step) => {
                            if (step <= companyInfo.companyProgressState) {
                                setStep(step);
                            }
                        }}
                    />

                    <ApplicationRenderWidget step={step} />
                </Stack>
                <Group justify="end">
                    {step !== 5 && (
                        <CnaButton
                            loading={submitting}
                            disabled={!submitEnabled}
                            color="basic"
                            leftSection={<Check />}
                            onClick={() => {
                                bus.emit(
                                    "project.company.application.submit.click"
                                );
                            }}
                        >
                            {t("common.submit", lang)}
                        </CnaButton>
                    )}

                    <Button
                        variant="default"
                        onClick={() => close("projectCompanyApplication")}
                    >
                        {t("common.close", lang)}
                    </Button>
                </Group>
            </CompanyApplicationContext.Provider>
        </Modal>
    );
};

export default CompanyApplicationModal;
