import api from "@/apis";
import useRegisterStore from "@/store/register";
import { CnaButton } from "@code.8cent/react/components";
import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";
import { Stack } from "@mantine/core";
import { useMount, useRequest } from "ahooks";
import { useNavigate } from "react-router-dom";

interface PayParam {
    /**
     * 应用ID
     */
    appid: string;
    /**
     * 订单详情扩展字符串
     */
    prepayId: string;
    /**
     * 随机字符串
     */
    random: string;
    /**
     * 签名
     */
    signature: string;
    /**
     * 时间戳
     */
    time: string;
}

const WechatMPPay = ({ token }: { token: string }) => {
    const navigate = useNavigate();

    const { setRegisterInfoValue } = useRegisterStore();

    const { run: wxAuth } = useRequest(
        async (code: string) => {
            const payToken = await window.localForage.getItem<string>("pay-token");

            let { result, error } = await cnaRequest<{ openid: string }>(
                "/api/v1/wechat/oauth",
                "POST",
                {
                    state: payToken,
                    code,
                }
            );

            if (error) {
                noty.error("缺失支付参数，正在返回...");

                navigate("/account/register", { replace: true });
            }
        },
        {
            manual: true,
        }
    );

    const { run: invokeWechatPay } = useRequest(
        async () => {
            const payToken = await window.localForage.getItem<string>("pay-token");

            let { error, result } = await cnaRequest<PayParam>("/api/v1/wechat/jsapi", "POST", {
                token: payToken,
            });

            if (!error) {
                const payParam = result.data;

                window.WeixinJSBridge.invoke(
                    "getBrandWCPayRequest",
                    {
                        appId: payParam.appid, //公众号ID，由商户传入
                        timeStamp: payParam.time, //时间戳，自1970年以来的秒数
                        nonceStr: payParam.random, //随机串
                        package: `prepay_id=${payParam.prepayId}`,
                        signType: "RSA", //微信签名方式：
                        paySign: payParam.signature, //微信签名
                    },
                    function (res) {
                        if (res.err_msg == "get_brand_wcpay_request:ok") {
                            window.localForage.removeItem("pay-token");
                        } else {
                            noty.error("微信支付失败，请重试");
                        }
                    }
                );
            } else {
                noty.error(error.message);
            }
        },
        {
            manual: true,
        }
    );

    useMount(async () => {
        const savedPayToken = await window.localForage.getItem<string>("pay-token");

        if (!token?.length && !savedPayToken?.length) {
            noty.error("缺失支付参数，正在返回...");
            navigate("/account/register", { replace: true });
        }

        if (!savedPayToken || savedPayToken.length === 0) {
            await window.localForage.setItem("pay-token", token);
        }

        setRegisterInfoValue("token", savedPayToken);

        const url = new URL(window.location.href);

        const code = url.searchParams.get("code");

        const state = url.searchParams.get("state");

        if (!code || code.length === 0 || state !== "cnaWePay") {
            window.location.href =
                "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx944d0f3709b4b7cd&redirect_uri=https://ai.corporate-advisory.cn/account/register-payment&response_type=code&scope=snsapi_userinfo&state=cnaWePay#wechat_redirect";
        } else {
            await wxAuth(code);
        }
    });

    return (
        <Stack>
            <CnaButton onClick={invokeWechatPay}>微信支付</CnaButton>
        </Stack>
    );
};

export default WechatMPPay;
