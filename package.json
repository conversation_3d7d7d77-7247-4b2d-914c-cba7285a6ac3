{"name": "code.8cent", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@code.8cent/config-tailwind": "workspace:*", "@code.8cent/config-typescript": "workspace:*", "@code.8cent/shared-types": "workspace:*", "@types/crypto-js": "^4.2.2", "@types/node": "^22.1.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.41", "tailwindcss": "^3.4.9", "turbo": "^2.2.3", "typescript": "^5.6.3", "vite": "^5.4.5", "vite-bundle-analyzer": "^0.10.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-static-copy": "^1.0.6", "vite-plugin-svgr": "^4.2.0"}, "packageManager": "pnpm@8.15.6", "engines": {"node": ">=18"}, "dependencies": {"@code.8cent/i18n": "workspace:*", "@code.8cent/react": "workspace:*", "@code.8cent/store": "workspace:*", "@code.8cent/theme": "workspace:*", "@code.8cent/utils": "workspace:*", "@hello-pangea/dnd": "^17.0.0", "@hookform/resolvers": "^3.9.0", "@mantine/core": "^7.13.4", "@mantine/dates": "^7.13.4", "@mantine/dropzone": "^7.13.4", "@mantine/hooks": "^7.13.4", "@mantine/modals": "^7.13.4", "@mantine/notifications": "^7.13.4", "@phosphor-icons/react": "^2.1.7", "@schedule-x/calendar": "^2.4.1", "@schedule-x/event-modal": "^2.4.1", "@schedule-x/events-service": "^2.4.1", "@schedule-x/react": "^2.4.1", "@schedule-x/theme-default": "^2.4.1", "@tanstack/react-table": "^8.20.5", "ahooks": "^3.8.0", "axios": "^1.7.3", "bowser": "^2.11.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "docx-preview": "^0.3.3", "es-toolkit": "^1.15.1", "filesize": "^10.1.6", "jspdf": "^3.0.1", "localforage": "^1.10.0", "mitt": "^3.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "react-pdf": "^9.1.0", "react-router-dom": "^6.26.0", "react-signature-pad-wrapper": "^4.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "zod": "^3.23.8", "zustand": "^4.5.4"}}