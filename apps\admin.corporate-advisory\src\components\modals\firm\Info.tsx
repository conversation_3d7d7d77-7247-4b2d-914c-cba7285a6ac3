import useSettingStore from "@code.8cent/store/setting";
import useModalStore from "@/store/modal";
import {
    Divider,
    Group,
    Modal,
    Select,
    SimpleGrid,
    Stack,
    Text,
    Textarea,
    TextInput,
} from "@mantine/core";
import { useState } from "react";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn, useRequest } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { CnaButton } from "@code.8cent/react/components";
import { Check, Trash, X } from "@phosphor-icons/react";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import api from "@/apis";

type FirmInfoProps = {
    onUpdateSuccess: () => void;
};

const fileConfig = {
    1: {
        name: "营业执照/执业许可证",
    },
    2: {
        name: "征信报告",
    },
    3: {
        name: "主要管理制度",
    },
    4: {
        name: "主要办公场所产权证书/租赁合同",
    },
    5: {
        name: "最近一年的审计报告/年度财务报表",
    },
    6: {
        name: "资质及荣誉证明",
    },
    7: {
        name: "主要客户合作合同",
    },
};

const FirmInfo = ({ onUpdateSuccess }: FirmInfoProps) => {
    const { lang } = useSettingStore();

    const { openFileView } = useFileViewer();
    const openConfirm = useModalStore.use.openConfirm();

    const [loading, setLoading] = useState(false);

    const firmInfoParams = useModalStore((state) => state.modalParams.firmInfoModal);
    const firm = firmInfoParams?.firmInfo;

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.firmInfoModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        close("firmInfoModal");
    });

    const { run: reviewFirm } = useRequest(
        async (status: 1 | 2) => {
            const res = await api.firm.review({
                id: firm?.id,
                status,
            });

            if (!res) {
                onUpdateSuccess();
            }
        },
        {
            manual: true,
        }
    );

    const modalFooterButtons = [
        {
            key: "close",
            label: "关闭",
            leftSection: <Trash />,
            style: "outline",
            onClick: closeModal,
        },
    ];

    // firm status = 0 时候，才显示
    if (firm?.status === 0) {
        modalFooterButtons.push({
            key: "review_pass",
            label: "通过",
            style: "primary",
            leftSection: <Check />,
            onClick: () => {
                openConfirm({
                    title: "提示",
                    message: "您确定通过该申请吗？",
                    onConfirm: async () => {
                        reviewFirm(1);
                    },
                });
            },
        });
        modalFooterButtons.push({
            key: "review_reject",
            label: "不通过",
            style: "default",
            leftSection: <X />,
            onClick: () => {
                openConfirm({
                    title: "提示",
                    message: "您确定不通过该申请吗？",
                    onConfirm: async () => {
                        reviewFirm(2);
                    },
                });
            },
        });
    }

    return (
        <Modal
            title="联号事务所申请信息"
            opened={show}
            onClose={closeModal}
            size="xl"
        >
            <Text className="tw-font-bold tw-bg-[#060d3d] tw-text-white">企业基础信息</Text>
            <Divider my={10} />
            <TextInput
                label="企业名称"
                defaultValue={firm?.company_name}
                readOnly
            />
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="统一社会信用代码"
                    defaultValue={firm?.credit_code}
                    readOnly
                />
                <TextInput
                    label="员工人数"
                    defaultValue={firm?.employees}
                    readOnly
                />
            </SimpleGrid>
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="注册资本（元）"
                    defaultValue={firm?.registered_capital}
                    readOnly
                />
                <TextInput
                    label="实缴资本（元）"
                    defaultValue={firm?.paid_capital}
                    readOnly
                />
            </SimpleGrid>
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="主要办公场所地址"
                    defaultValue={firm?.office_address}
                    readOnly
                />
                <Select
                    label="办公场所地址类型"
                    data={[
                        { value: "1", label: "自有" },
                        { value: "2", label: "租赁" },
                    ]}
                    defaultValue={String(firm?.office_type)}
                    readOnly
                />
            </SimpleGrid>
            <Textarea
                label="企业简介（300字以内，简述历史沿革及关键事件）"
                defaultValue={firm?.intro}
                readOnly
            />
            <Textarea
                label="经营范围"
                defaultValue={firm?.range}
                readOnly
            />
            <Textarea
                label="核心业务介绍"
                defaultValue={firm?.main_business}
                readOnly
            />
            <Textarea
                label="主要人员简介（企业负责人及骨干）"
                defaultValue={firm?.leader_intro}
                readOnly
            />
            <Textarea
                label="资质及荣誉"
                defaultValue={firm?.honors}
                readOnly
            />
            <Textarea
                label="主要客户名称"
                defaultValue={firm?.customer_name}
                readOnly
            />
            <Textarea
                label="涉诉或行政处罚记录（填表之日起3年内）"
                defaultValue={firm?.lawsuit}
                readOnly
            />
            <Text className="tw-font-bold tw-mt-2 tw-bg-[#060d3d] tw-text-white">
                企业财务信息（以最近一期审计报告/财务报表数据填写）
            </Text>
            <Divider my={10} />
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="年营业收入（人民币：元）"
                    defaultValue={firm?.income}
                    readOnly
                />
                <TextInput
                    label="年营业成本（人民币：元）"
                    defaultValue={firm?.cost}
                    readOnly
                />
            </SimpleGrid>
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="总资产（人民币：元）"
                    defaultValue={firm?.total_assets}
                    readOnly
                />
                <TextInput
                    label="总负债（人民币：元）"
                    defaultValue={firm?.total_liabilities}
                    readOnly
                />
            </SimpleGrid>
            <Text className="tw-font-bold tw-mt-2 tw-bg-[#060d3d] tw-text-white">企业开票信息</Text>
            <Divider my={10} />
            <Select
                label="纳税人资质"
                data={[
                    { value: "1", label: "一般纳税人" },
                    { value: "2", label: "小规模纳税人" },
                ]}
                defaultValue={String(firm?.taxpayer_type)}
                readOnly
            />
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="银行账号"
                    defaultValue={firm?.bank_account}
                    readOnly
                />
                <TextInput
                    label="银行名称"
                    defaultValue={firm?.bank_name}
                    readOnly
                />
            </SimpleGrid>
            <SimpleGrid cols={{ base: 1, md: 2 }}>
                <TextInput
                    label="开票地址"
                    defaultValue={firm?.tax_address}
                    readOnly
                />
                <TextInput
                    label="联系电话"
                    defaultValue={firm?.tax_phone}
                    readOnly
                />
            </SimpleGrid>
            <Text className="tw-font-bold tw-mt-2 tw-bg-[#060d3d] tw-text-white">
                上传申请的文件
            </Text>
            <Divider my={10} />
            <Stack gap={4}>
                {Object.entries(fileConfig).map(([type, file]) => (
                    <Group
                        key={type}
                        justify="space-between"
                    >
                        <Text size="sm">{file.name}</Text>
                        <CnaButton
                            variant="outline"
                            size="xs"
                            onClick={() => {
                                if (firm?.[`file${type}`]) {
                                    openFileView(
                                        `${window.api_base_url}/api/v1/admin/firm/files?path=${
                                            firm?.[`file${type}`]
                                        }&type=oss`,
                                        { title: file.name }
                                    );
                                }
                            }}
                        >
                            查看
                        </CnaButton>
                    </Group>
                ))}
            </Stack>

            <div className="tw-mt-10">
                <ModalFooter buttons={modalFooterButtons} />
            </div>
        </Modal>
    );
};

export default FirmInfo;
