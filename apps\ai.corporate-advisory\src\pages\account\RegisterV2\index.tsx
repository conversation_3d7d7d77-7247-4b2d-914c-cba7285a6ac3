import { t } from "@code.8cent/i18n";
import { AuthFormCard, CnaButton, CountrySelect } from "@code.8cent/react/components";
import { AuthenticationLayout } from "@code.8cent/react/layouts";
import noty from "@code.8cent/react/noty";
import useDataStore from "@code.8cent/store/data";
import useSettingStore from "@code.8cent/store/setting";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    Grid,
    Group,
    InputWrapper,
    Select,
    Stack,
    Text,
    TextInput,
    Image,
    Divider,
    Checkbox,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import {
    Check,
    CheckCircle,
    CircleNotch,
    Divide,
    Download,
    X,
    XCircle,
} from "@phosphor-icons/react";
import dayjs from "dayjs";
import { useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import AddressInfoSection from "./components/AddressInfoSection";
import PhoneEmailSection from "./components/PhoneEmailSection";
import { RegisterInput, ValidateStatus } from "./types";
import api from "@/apis";
import { useDebounce, useRequest } from "ahooks";
import { cnaRequest, sleepMs } from "@code.8cent/utils";
import useRegisterStore from "@/store/register";
import { useNavigate } from "react-router-dom";
import AddressSelectSection from "./components/AddressSelectSection";

import DatePicker from "./components/DatePicker";
const registerSchema = z.object({
    email: z.string().email("form.email.incorrect"),
    prefixID: z.string().min(1, "form.country.code"),
    phone: z.string().regex(/^1[3-9]\d{9}$/g, "form.phone.number.incorrect"),
    name: z.string().min(1, "form.enter.name"),
    // idNumber: z.string().min(1, "form.enter.id"),
    idNumber: z.string().optional(),
    gender: z.string().min(1, "form.enter.gender"),
    birthDate: z.string().date("form.enter.date.of.birth"),
    nationalityID: z.string().min(1, "form.country.code"),
    // addressCity: z.string().min(1, "form.enter.city"),
    addressCountry: z.string().min(1, "form.enter.country"),
    addressPostcode: z.string().min(1, "form.enter.postal.code"),
    addressStreet: z.string().min(1, "form.enter.street"),
    addressUnit: z.string().min(1, "form.enter.unit"),
    // addressState: z.string().min(1, "form.enter.state"),
    // addressDistrict: z.string().min(1, "form.enter.district"),
    addressCityId: z.string().min(1, "form.enter.city"),
    addressStateId: z.string().min(1, "form.enter.state"),
    addressDistrictId: z.string().min(1, "form.enter.district"),
});

const AccountRegisterPage = () => {
    const [validate, setValidate] = useState<ValidateStatus>({
        phone: false,
        email: false,
    });

    const { refer, setRegisterInfoValue } = useRegisterStore();

    const navigate = useNavigate();

    const {
        handleSubmit,
        setValue,
        control,
        getValues,
        trigger,
        formState: { errors },
        setError,
        reset,
        clearErrors,
    } = useForm<RegisterInput>({
        defaultValues: {
            name: "",
            gender: "",
            idNumber: "",
            birthDate: "1979-06-15",
            phone: "",
            email: "",
            prefixID: "",
            nationalityID: "",
            // addressCity: "",
            addressCountry: "44",
            addressPostcode: "528200",
            addressStreet: "",
            addressUnit: "",
            // addressState: "",
            // addressDistrict: "",
            addressCityId: "",
            addressStateId: "",
            addressDistrictId: "",
        },
        resolver: zodResolver(
            registerSchema
                .refine(() => validate.phone === true, {
                    params: {
                        type: "need_validate",
                    },
                    message: "请先验证手机号",
                    path: ["phone"],
                })
                .refine(() => validate.email === true, {
                    params: {
                        type: "need_validate",
                    },
                    message: "请先验证邮箱地址",
                    path: ["email"],
                })
        ),
    });

    const [reffererInput, setReffererInput] = useState<string>("");

    const reffererNameRef = useRef<HTMLParagraphElement>(null);
    const DatePickerRef = useRef<{ validate: () => Promise<boolean> }>(null);

    const [paddingLeft, setPaddingLeft] = useState(10);

    const [birthDate, setBirthDate] = useState({
        year: "",
        month: "",
        day: "",
    });

    const { lang, countryID } = useSettingStore();

    const { countryDatas } = useDataStore();

    const debouncedReffererInput = useDebounce(reffererInput, {
        wait: 500,
    });

    const [agreementChecked, setAgreementChecked] = useState(false);

    useEffect(() => {
        if (debouncedReffererInput?.length >= 4) {
            // 只有当输入超过4个字符才验证
            if (refer?.length <= 0) {
                checkRefferer(debouncedReffererInput);
            }
        }
    }, [debouncedReffererInput]);

    const {
        run: checkRefferer,
        data: refferer = null,
        mutate,
        loading: searching,
    } = useRequest(
        async (input: string) => {
            const { result, error } = await cnaRequest<ReffererInfo>(
                "/api/v1/team/getRecommendInfo",
                "POST",
                {
                    input,
                }
            );

            if (!error) {
                return result.data;
            } else {
                return null;
            }
        },
        {
            manual: true,
        }
    );

    const { run: getReffererByToken } = useRequest(
        async () => {
            const { result, error } = await cnaRequest<ReffererInfo>(
                "/api/v1/team/getRecommendInfo",
                "POST",
                {
                    token: refer,
                }
            );

            if (!error) {
                mutate(result.data);
                setReffererInput(result.data.profilePartnerCode ?? "");
            }
        },
        {
            manual: false,
            ready: refer?.length > 0,
        }
    );

    const { run: handleFileBtnClick } = useRequest(
        async (type: 4) => {
            let token = await api.team.gerneateDocToken({
                type,
                profileID: refferer?.profileID,
            });

            if (token) {
                window.open(`${window.api_base_url}/api/v1/team/downloadDoc/${token}`, "_blank");
            }
        },
        {
            manual: true,
        }
    );

    const { run: confirmRegister, loading: confirming } = useRequest(
        async (data: RegisterInput) => {
            const submitData: ConfirmRegisterParams = data;

            if (refferer) {
                submitData.is_team = refferer.is_team;
                submitData.recommend = refferer.profileID;
            }

            DatePickerRef?.current?.validate?.();

            if (!agreementChecked) {
                noty.error("请勾选确认您填写信息准确无误");
                return;
            }

            const submit_res = await api.register.confirm(submitData);

            if ("error" in submit_res) {
                noty.error(t("form.sign.up.information", lang), submit_res.message);
                return;
            }

            setRegisterInfoValue("token", submit_res.token);

            await sleepMs(500);

            reset();

            navigate("/account/register-payment", { replace: true });
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        setValue("prefixID", String(countryID));
        setValue("nationalityID", String(countryID));
        setValue("addressCountry", String(countryID));
    }, [countryID]);

    useEffect(() => {
        if (!refferer) {
            setPaddingLeft(10);
        } else {
            setReffererInput(refferer?.profileName?.toString());
            setPaddingLeft(reffererNameRef.current?.offsetWidth + 10);
        }
        console.log("refferer", refferer);
    }, [refferer]);

    return (
        <div className="tw-relative boot-bg">
            <AuthenticationLayout>
                <AuthFormCard className="tw-w-[1000px] tw-mx-auto">
                    <form
                        onSubmit={handleSubmit(confirmRegister, (errors) => {
                            console.log(errors);
                            DatePickerRef?.current?.validate?.();
                        })}
                    >
                        <Grid>
                            <Grid.Col span={{ base: 12 }}>
                                <TextInput
                                    label="推荐人(如有)"
                                    leftSectionWidth={"auto"}
                                    leftSection={
                                        <Text
                                            className="tw-px-4 tw-border-r tw-border-gray-300"
                                            ref={reffererNameRef}
                                            size="sm"
                                        >
                                            {refferer?.profilePartnerCode || ""}
                                        </Text>
                                    }
                                    rightSection={
                                        searching ? (
                                            <CircleNotch
                                                size={18}
                                                className="tw-animate-spin"
                                            />
                                        ) : refferer ? (
                                            <CheckCircle
                                                size={18}
                                                className="tw-text-green-600"
                                            />
                                        ) : reffererInput.length > 0 ? (
                                            <XCircle
                                                size={18}
                                                className="tw-text-red-600"
                                            />
                                        ) : null
                                    }
                                    placeholder="请输入推荐人手机号或合伙人编码"
                                    styles={{
                                        input: {
                                            paddingLeft,
                                        },
                                    }}
                                    value={reffererInput}
                                    onChange={(e) => {
                                        setReffererInput(e.target.value);
                                    }}
                                    readOnly={refer?.length > 0 ? true : false}
                                />
                            </Grid.Col>
                            <Grid.Col span={{ base: 12 }}>
                                <PhoneEmailSection
                                    {...{
                                        control,
                                        getValues,
                                        trigger,
                                        clearErrors,
                                        validate,
                                        setValidate,
                                    }}
                                />
                            </Grid.Col>
                            <Grid.Col
                                className="-tw-mb-5"
                                span={{ base: 12 }}
                            >
                                <Divider
                                    my="sm"
                                    variant="dashed"
                                />
                            </Grid.Col>

                            <Grid.Col span={{ base: 12, md: 6 }}>
                                <Stack>
                                    {/* {refferer?.team_file === 1 && (
                                        <InputWrapper label="三三制团队简介">
                                            <CnaButton
                                                fullWidth
                                                variant="outline"
                                                leftSection={<Download />}
                                                onClick={() => {
                                                    handleFileBtnClick(4);
                                                }}
                                            >
                                                下载
                                            </CnaButton>
                                        </InputWrapper>
                                    )} */}
                                    {/* <Controller
                                        name="nationalityID"
                                        control={control}
                                        render={({ field }) => (
                                            <CountrySelect
                                                flagKey="countryISOCode2"
                                                labelKey={`country${lang}` as keyof CountryDataItem}
                                                valueKey="countryID"
                                                data={countryDatas}
                                                label={t("introduction.nationality", lang)}
                                                allowDeselect={false}
                                                {...field}
                                                readOnly
                                            />
                                        )}
                                    /> */}

                                    <Controller
                                        name="name"
                                        control={control}
                                        render={({ field, fieldState }) => (
                                            <TextInput
                                                // placeholder={t(
                                                //     "introduction.label.last_name",
                                                //     lang
                                                // )}
                                                placeholder="请输入身份证名字"
                                                label={t("introduction.label.last_name", lang)}
                                                {...field}
                                                error={fieldState.error ? true : false}
                                            />
                                        )}
                                    />

                                    {/* <Controller
                                        name="idNumber"
                                        control={control}
                                        render={({ field, fieldState }) => (
                                            <TextInput
                                                label={t("form.id.number", lang)}
                                                {...field}
                                                error={fieldState.error ? true : false}
                                                leftSection={
                                                    <Image
                                                        className=""
                                                        src="/images/icons/china.svg"
                                                        alt=""
                                                        w={22}
                                                    />
                                                }
                                                onChange={(e) => {
                                                    if (e.target.value.length === 18) {
                                                        const year = e.target.value.slice(6, 10);
                                                        const month = parseInt(
                                                            e.target.value.slice(10, 12)
                                                        ).toString();
                                                        const day = parseInt(
                                                            e.target.value.slice(12, 14)
                                                        ).toString();
                                                        setBirthDate({
                                                            year,
                                                            month,
                                                            day,
                                                        });
                                                    } else {
                                                        setBirthDate({
                                                            year: "",
                                                            month: "",
                                                            day: "",
                                                        });
                                                    }

                                                    field.onChange(e.target.value);
                                                }}
                                            />
                                        )}
                                    /> */}

                                    <Controller
                                        name="gender"
                                        control={control}
                                        render={({ field, fieldState }) => (
                                            <Select
                                                label={t("form.gender", lang)}
                                                placeholder={t("form.select.gender", lang)}
                                                data={[
                                                    {
                                                        label: t("form.gender.male", lang),
                                                        value: "M",
                                                    },
                                                    {
                                                        label: t("form.gender.female"),
                                                        value: "F",
                                                    },
                                                ]}
                                                {...field}
                                                error={fieldState.error ? true : false}
                                            />
                                        )}
                                    />
                                    <DatePicker
                                        ref={DatePickerRef}
                                        label={t("form.birth.date", lang)}
                                        defaultValue={birthDate}
                                        placeholder={{
                                            day: t("form.enter.birth.day", lang),
                                            month: t("form.enter.birth.month", lang),
                                            year: t("form.enter.birth.year", lang),
                                        }}
                                        onChange={(e) => {
                                            console.log(e); //2024-01-01

                                            setValue("birthDate", e);
                                        }}
                                    />
                                </Stack>
                            </Grid.Col>
                            <Grid.Col span={{ base: 12, md: 6 }}>
                                <Stack>
                                    {/* <Controller
                                <Controller
                                    name="idNumber"
                                    control={control}
                                    render={({ field, fieldState }) => (
                                        <TextInput
                                            label={t("form.id.number", lang)}
                                            {...field}
                                            error={fieldState.error ? true : false}
                                            onChange={(e) => {
                                                if (e.target.value.length === 18) {
                                                    const year = e.target.value.slice(6, 10);
                                                    const month = parseInt(e.target.value.slice(10, 12)).toString();
                                                    const day = parseInt(e.target.value.slice(12, 14)).toString();
                                                    setBirthDate({
                                                        year,
                                                        month,
                                                        day,
                                                    });
                                                }

                                                field.onChange(e.target.value);
                                            }}
                                        />
                                    )}
                                />
                            </Stack>
                        </Grid.Col>
                        <Grid.Col span={{ base: 12, md: 6 }}>
                            <Stack>
                                <Controller
                                    name="gender"
                                    control={control}
                                    render={({ field, fieldState }) => (
                                        <Select
                                            label={t("form.gender", lang)}
                                            placeholder={t("form.select.gender", lang)}
                                            data={[
                                                {
                                                    label: t("form.gender.male", lang),
                                                    value: "M",
                                                },
                                                {
                                                    label: t("form.gender.female"),
                                                    value: "F",
                                                },
                                            ]}
                                            {...field}
                                            error={fieldState.error ? true : false}
                                        />
                                    )}
                                />
                                {/* <Controller
                                    name="birthDate"
                                    control={control}
                                    render={({ field, fieldState }) => (
                                        <DatePickerInput
                                            label={t("form.birth.date", lang)}
                                            placeholder={t("form.enter.birth.date", lang)}
                                            valueFormat="YYYY-MM-DD"
                                            {...field}
                                            value={field.value ? new Date(field.value) : null}
                                            onChange={(e) => {
                                                field.onChange(dayjs(e).format("YYYY-MM-DD"));
                                                console.log(e);

                                            }}
                                            error={fieldState.error ? true : false}
                                        />
                                    )}
                                /> */}
                                    {/* <DatePicker
                                        label={t("form.birth.date", lang)}
                                        placeholder={{
                                            day: t("form.enter.birth.day", lang),
                                            month: t("form.enter.birth.month", lang),
                                            year: t("form.enter.birth.year", lang),
                                        }}
                                        onChange={(e) => {
                                            console.log(e);
                                            setValue("birthDate", e);
                                        }}
                                    ></DatePicker> */}

                                    {/* <AddressInfoSection
                                    errors={errors}
                                    getValues={getValues}
                                    setValue={setValue}
                                /> */}
                                    <AddressSelectSection
                                        errors={errors}
                                        getValues={getValues}
                                        setValue={setValue}
                                    />
                                </Stack>
                            </Grid.Col>
                            <Grid.Col span={{ base: 12 }}>
                                <Checkbox
                                    label="本人保证以上填写信息准确无误。"
                                    checked={agreementChecked}
                                    onChange={(e) => {
                                        setAgreementChecked(e.currentTarget.checked);
                                    }}
                                />
                            </Grid.Col>
                            <Grid.Col span={{ base: 12 }}>
                                <CnaButton
                                    color="basic"
                                    leftSection={<Check />}
                                    type="submit"
                                    loading={confirming}
                                    disabled={searching}
                                    className="tw-w-full"
                                >
                                    {t("common.submit", lang)}
                                </CnaButton>
                            </Grid.Col>
                        </Grid>
                    </form>
                </AuthFormCard>
            </AuthenticationLayout>
            <div className="sm:tw-fixed tw-bottom-5 tw-w-full tw-text-center tw-text-white tw-py-2 tw-text-sm">
                陈玮伦合伙人事务所 版权所有 © 2009 - 2024
            </div>
        </div>
    );
};

export default AccountRegisterPage;
