import api from "@/apis";
import { t } from "@code.8cent/i18n";
import useDataStore from "@code.8cent/store/data";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import { useEventBus } from "@/utils/eventBus";
import { zodResolver } from "@hookform/resolvers/zod";
import { Select } from "@mantine/core";
import { useMount, useRequest, useUnmount } from "ahooks";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

type LanguageSetupFormInput = {
    language: string;
};

const LanguageSetup = () => {
    const bus = useEventBus();

    const { lang, setLang } = useSettingStore();

    const { setState: setWizardState, state } = useWizardStore();

    const { languages } = useDataStore();

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
    } = useForm<LanguageSetupFormInput>({
        defaultValues: { language: "" },
        resolver: zodResolver(z.object({ language: z.string().min(1) })),
    });

    const { run: setupLanguage, loading } = useRequest(
        async (data: LanguageSetupFormInput) => {
            let res = await api.register.setLanguage(data.language);

            if (res === true) {
                setWizardState(state + 1);

                setLang(data.language as LangCode);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        bus.emit("wizard.submitting", loading);
    }, [loading]);

    useMount(() => {
        bus.on("wizard.submit.click", handleSubmit(setupLanguage));
    });

    useUnmount(() => {
        bus.emit("wizard.submitting", false);
        bus.off("wizard.submit.click");
    });

    return (
        <Select
            description="为此账号选择标题、按钮和合伙人平台其他文本的首选语言"
            data={languages.map((lang) => ({
                value: String(lang.languageCode),
                label: String(lang.languageType),
            }))}
            label={t("setting.lang_area.label.system_language", lang)}
            {...register("language")}
            onChange={(e) => {
                setValue("language", e, { shouldValidate: true });
            }}
            placeholder="请选择语言"
            error={errors.language ? true : false}
        />
    );
};

export default LanguageSetup;
