import cnaRequest from "@code.8cent/utils/cnaRequest";

const project = {
    getProjectList: async () => {
        let { result, error } = await cnaRequest<ProjectItem[]>(
            "/api/v1/projects",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return [];
        }
    },
    getProjectServiceList: async () => {
        let { result, error } = await cnaRequest<ProjectServiceItem[]>(
            "/api/v1/company/progressOne/valueAddedServices",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return [];
        }
    },
    createProjectCompany: async (params: ProjectCompanyCreateParams) => {
        let { result, error } = await cnaRequest(
            "/api/v1/company/createCompany",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            return false;
        }
    },
    getCompanyInfo: async (companyID: number) => {
        let { result, error } = await cnaRequest<CompanyApplictaionInfo>(
            "/api/v1/company/getCompanyInfo",
            "GET",
            { companyID }
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },
    downloadCompanyApplicationPDF: async (companyID: number) => {
        const { result: pdfToken, error: getPDFTokenError } =
            await cnaRequest<string>(
                "/api/v1/company/progressOne/generateDownloadFileKey",
                "POST",
                { companyID }
            );

        if (getPDFTokenError) {
            return null;
        } else {
            return pdfToken.data;
        }
    },
    getPreliminarySubmissonData: async (companyID: number) => {
        let { result, error } = await cnaRequest<PreliminarySubmissionData>(
            "/api/v1/company/progressOne/getProgressOneInfo",
            "GET",
            { companyID }
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },
    updatePreliminarySubmission: async (params: {
        companyID: number;
        companyServices: string;
        preAuditForm: File;
    }) => {
        const formData = new FormData();

        formData.append("companyID", params.companyID.toString());
        formData.append("companyServices", params.companyServices);
        formData.append("preAuditForm", params.preAuditForm);

        let { error } = await cnaRequest(
            "/api/v1/company/progressOne/commit",
            "POST",
            formData
        );

        if (!error) {
            return true;
        } else {
            return false;
        }
    },

    getUploadedPreliminarySubmissionForm: async (companyID: number) => {
        const { result, error: verifyError } = await cnaRequest<string>(
            "/api/v1/company/progressOne/generateViewFileKey",
            "POST",
            {
                companyID,
            }
        );

        if (verifyError) {
            return null;
        }

        const token = result.data;

        const { result: pdf_file, error: file_error } = await cnaRequest<Blob>(
            `/api/v1/company/progressOne/preview/${token}`,
            "GET",
            {},
            {
                responseType: "blob",
            }
        );

        console.log(pdf_file, file_error);

        if (file_error) {
            return null;
        }

        const file = new File(
            [pdf_file.data],
            `filename_${new Date().getTime()}`,
            { type: pdf_file.data.type }
        );

        return file;
    },

    getPaymentSubmissionData: async (companyID: number) => {
        let { result, error } = await cnaRequest<PaymentSubmissionData>(
            "/api/v1/company/progressTwo/getProgressTwoInfo",
            "GET",
            { companyID }
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },

    updatePaymentSubmissionData: async (params: {
        companyID: number;
        bankID: string;
        paymentCategoryID: string;
        paymentCode: string;
        file: File;
    }) => {
        const formData = new FormData();

        formData.append("companyID", params.companyID.toString());
        formData.append("bankID", params.bankID);
        formData.append("paymentCategoryID", params.paymentCategoryID);
        formData.append("paymentCode", params.paymentCode);
        formData.append("file", params.file);

        let { error } = await cnaRequest(
            "/api/v1/company/progressTwo/commit",
            "POST",
            formData
        );

        if (!error) {
            return true;
        } else {
            return false;
        }
    },

    getCompanyAccountInfo: async (companyID: number) => {
        const { result, error } = await cnaRequest<{
            username: string;
            password: string;
            loginState: string;
            url: string;
        }>("/api/v1/company/progressThree/getProgressThreeInfo", "GET", {
            companyID,
        });

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    },

    getUploadedPaymentSubmissionFile: async (companyID: number) => {
        const { result, error: verifyError } = await cnaRequest<string>(
            "/api/v1/company/progressTwo/generateViewFileKey",
            "POST",
            {
                companyID,
            }
        );

        if (verifyError) {
            return null;
        }

        const token = result.data;

        const { result: receipt, error: file_error } = await cnaRequest<Blob>(
            `/api/v1/company/progressTwo/preview/${token}`,
            "GET",
            {},
            {
                responseType: "blob",
            }
        );

        console.log(receipt, file_error);

        if (file_error) {
            return null;
        }

        const file = new File(
            [receipt.data],
            `filename_${new Date().getTime()}`,
            { type: receipt.data.type }
        );

        return file;
    },

    getApplicationDocumentsInfo: async (companyID: number) => {
        type DocumentInfo = {
            /**
             * File ID
             */
            fileID: number;
            /**
             * File name
             */
            fileName: string;
            /**
             * Is the document required
             */
            isRequired: number;
            /**
             * Is the document uploaded
             */
            isUpload: number;
            /**
             * State of the document
             * 1: Waiting for review
             * 2: Review passed
             * 3: Review failed
             */
            state: number;
            /**
             * Reason if the state is 3
             */
            reason: null | string;
        };

        const { result, error } = await cnaRequest<DocumentInfo[]>(
            "/api/v1/company/progressFour/getProgressFourInfo",
            "GET",
            { companyID }
        );

        if (error) {
            return [];
        } else {
            return result.data;
        }
    },

    uploadDocument: async ({
        companyID,
        file,
        fileID,
    }: {
        companyID: number;
        file: File;
        fileID: number;
    }) => {
        const formData = new FormData();

        formData.append("companyID", companyID.toString());
        formData.append("fileID", fileID.toString());
        formData.append("file", file);

        const { result, error } = await cnaRequest(
            "/api/v1/company/progressFour/upload",
            "POST",
            formData
        );

        if (error) {
            return false;
        } else {
            return true;
        }
    },
    getUploadedDocument: async ({
        companyID,
        fileID,
        mode = "view",
    }: {
        companyID: number;
        fileID: number;
        mode?: "view" | "download"
    }) => {
        const { result, error: verifyError } = await cnaRequest<string>(
            "/api/v1/company/progressFour/generateFileKey",
            "POST",
            {
                companyID,
                fileID,
            }
        );

        if (verifyError) {
            return null;
        }

        const token = result.data;

        if (mode === "view") {
            return `${window.api_base_url}/api/v1/company/progressFour/preview/${token}`;
        } else if (mode === "download") {
            window.open(
                `${window.api_base_url}/api/v1/company/progressFour/download/${token}`,
                "_blank"
            );
        }
    },
};

export default project;
