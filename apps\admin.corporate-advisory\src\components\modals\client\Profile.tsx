import api from "@/apis";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { Modal, Stack, TextInput, Select, SimpleGrid } from "@mantine/core";
import React, { useCallback, useState, useEffect } from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm, Controller } from "react-hook-form";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useShallow } from "zustand/react/shallow";
import { useMemoizedFn, useMount } from "ahooks";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";

// todo 确认字段
const clientSchema = z.object({
    status: z.string().min(1, "请选择企业状态"),
    project_progress: z.string().min(1, "请选择项目进度"),
    project_id: z.string().min(1, "请选择项目名字"),
    companyName: z.string().min(1, "请输入企业名字"),
    companyRegisterCode: z.string().min(1, "请输入企业注册号码"),
    companyCategoriesID: z.string().min(1, "请选择企业类别"),
    companyMobile: z.string().min(1, "请输入手机号"),
    companyEmail: z.string().min(1, "请输入邮件地址"),
    referee: z.string().optional(),
});

type ClientForm = z.infer<typeof clientSchema>;

interface ProfileProps {
    profile: TClient;
    onUpdateSuccess?: () => void;
    onCloseSuccess?: () => void;
}

const Profile: React.FC<ProfileProps> = React.memo(
    ({ profile, onUpdateSuccess = () => {}, onCloseSuccess = () => {} }) => {
        const { lang } = useSettingStore();

        const [loading, setLoading] = useState(false);

        const openConfirm = useModalStore.use.openConfirm();

        const [rankOptions, setRankOptions] = useState<
            { value: string; label: string }[]
        >([]);

        const client_status_map = [
            { value: "0", label: "离线" },
            { value: "1", label: "活跃" },
        ];

        const project_progress_map = [
            { value: "0", label: "进度一：提交预审表格" },
            { value: "1", label: "进度二：提交付款证明" },
            { value: "2", label: "进度三：企业账户设置" },
            { value: "3", label: "进度四：预备尽职调查报告" },
            { value: "4", label: "进度五：完成" },
        ];

        const { show, close } = useModalStore(
            useShallow((state) => ({
                show: state.clientProfileModal,
                close: state.close,
            }))
        );

        useEffect(() => {
            if (show === false) {
                reset();
            }
        }, [show]);

        const closeModal = useMemoizedFn(() => {
            close("clientProfileModal");
            onCloseSuccess();
        });

        const {
            control,
            handleSubmit,
            formState: { errors },
            reset,
        } = useForm<ClientForm>({
            defaultValues: {
                // todo 根据字段确定数据内容
                status: "",
                project_progress: "",
                project_id: "",
                companyName: "",
                companyRegisterCode: "",
                companyCategoriesID: "",
                companyMobile: "",
                companyEmail: "",
                referee: "",
            },
            resolver: zodResolver(clientSchema),
        });

        useEffect(() => {
            if (profile) {
                reset({
                    // todo 根据字段确定数据内容
                    status: "",
                    project_progress: "",
                    project_id: "",
                    companyName: profile.companyName,
                    companyRegisterCode: profile.companyRegisterCode,
                    companyCategoriesID: profile.companyCategoriesID.toString(),
                    companyMobile: profile.companyMobile,
                    companyEmail: profile.companyEmail,
                    referee: "",
                });
            } else {
                reset({
                    // todo 根据字段确定数据内容
                    status: "",
                    project_progress: "",
                    project_id: "",
                    companyName: "",
                    companyRegisterCode: "",
                    companyCategoriesID: "",
                    companyMobile: "",
                    companyEmail: "",
                    referee: "",
                });
            }
        }, [profile, reset]);

        const submitForm: SubmitHandler<ClientForm> = useCallback(
            async (data) => {
                setLoading(true);
                try {
                    // 修改客户信息
                    const res = await api.client.update({
                        companyName: data.companyName,
                        companyRegisterCode: data.companyRegisterCode,
                        companyCategoriesID: parseInt(data.companyCategoriesID),
                    }, profile.companyID);

                    if (res) {
                        noty.success("操作成功");
                        onUpdateSuccess();
                        closeModal();
                    }
                } catch (error) {
                    noty.error("操作失败，请重试");
                } finally {
                    setLoading(false);
                }
            },
            [profile, onUpdateSuccess, closeModal]
        );

        const handleSave = useCallback(() => {
            openConfirm({
                title: "提示",
                message: "您确定此操作么？",
                onConfirm: handleSubmit(submitForm),
            });
        }, [openConfirm, handleSubmit, submitForm]);

        const fetchRankOptions = async () => {
            const response = await api.team.getRankOptions();
            setRankOptions(response);
        };

        useMount(() => {
            fetchRankOptions();
        });

        if (!show) {
            return null;
        }

        return (
            <Modal
                opened={true}
                onClose={closeModal}
                title="简介信息"
                size="xl"
            >
                <form onSubmit={handleSubmit(handleSave, errors => console.log(errors))}>
                    <Stack gap="lg">
                        <SimpleGrid cols={2}>
                            <Controller
                                name="status"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        label="企业状态"
                                        placeholder="请选择企业状态"
                                        data={client_status_map}
                                        allowDeselect={false}
                                        withCheckIcon
                                        error={errors.status?.message}
                                    />
                                )}
                            />
                            <Controller
                                name="project_progress"
                                control={control}
                                render={({ field }) => (
                                    <Select
                                        {...field}
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        label="企业项目进度"
                                        placeholder="请选择企业项目进度"
                                        data={project_progress_map}
                                        allowDeselect={false}
                                        withCheckIcon
                                        error={errors.project_progress?.message}
                                    />
                                )}
                            />
                        </SimpleGrid>

                        <Controller
                            name="project_id"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="项目名字"
                                    placeholder="输入选择项目"
                                    error={errors.project_id?.message}
                                />
                            )}
                        />
                        <Controller
                            name="companyName"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="企业名字"
                                    placeholder="输入企业名字"
                                    error={errors.companyName?.message}
                                />
                            )}
                        />
                        <SimpleGrid cols={2}>
                            <Controller
                                name="companyRegisterCode"
                                control={control}
                                render={({ field }) => (
                                    <TextInput
                                        {...field}
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        label="企业注册号码"
                                        placeholder="输入企业注册号码"
                                        error={errors.companyRegisterCode?.message}
                                    />
                                )}
                            />
                            <Controller
                                name="companyCategoriesID"
                                control={control}
                                render={({ field }) => (
                                    <TextInput
                                        {...field}
                                        labelProps={{
                                            className: "profile-form-label",
                                        }}
                                        label="企业类别"
                                        placeholder="请选择企业类别"
                                        error={errors.companyCategoriesID?.message}
                                    />
                                )}
                            />
                        </SimpleGrid>
                        <Controller
                            name="companyMobile"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="手机号"
                                    placeholder="输入手机号"
                                    error={errors.companyMobile?.message}
                                />
                            )}
                        />
                        <Controller
                            name="companyEmail"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="邮件地址"
                                    placeholder="请输入邮件地址"
                                    error={errors.companyEmail?.message}
                                />
                            )}
                        />
                        <Controller
                            name="referee"
                            control={control}
                            render={({ field }) => (
                                <TextInput
                                    {...field}
                                    labelProps={{
                                        className: "profile-form-label",
                                    }}
                                    label="推荐人"
                                    placeholder="输入推荐人"
                                    error={errors.referee?.message}
                                />
                            )}
                        />

                        <ModalFooter
                            // timelineContent="最近修改: Amos Wu (2024-11-18 12:00:00)"
                            buttons={[
                                {
                                    key: "update",
                                    label: "保存",
                                    leftSection: <Check size={18} />,
                                    loading: loading,
                                    type: "submit",
                                },
                                {
                                    key: "close",
                                    label: "关闭",
                                    style: "outline",
                                    leftSection: <X size={18} />,
                                    onClick: closeModal,
                                },
                            ]}
                        />
                    </Stack>
                </form>
            </Modal>
        );
    }
);

export default Profile;
