import { forwardRef, useImperativeHandle, useState } from "react";
import { Grid, TextInput, NumberInput, Select, Button, Group, Stack } from "@mantine/core";
import { DateInput, DatePickerInput } from "@mantine/dates";
import { MagnifyingGlass, ArrowClockwise, CaretDown, CaretUp } from "@phosphor-icons/react";
import { SearchFormProps, SearchColumn, SearchFormRef } from "./types";
import dayjs from "dayjs";

const SearchForm = forwardRef<SearchFormRef, SearchFormProps>(
    (
        { columns, showNumber = 3, labelWidth = 80, onChange, onSearch, onReset }: SearchFormProps,
        ref
    ) => {
        const [formData, setFormData] = useState<Record<string, any>>({});
        const [expanded, setExpanded] = useState(false);

        // 计算应该显示的行数
        const visibleColumns = expanded ? columns : columns.slice(0, showNumber);

        // 是否显示展开按钮（列数大于showNumber时显示）
        const showExpandButton = columns.length > showNumber;

        // 处理表单值变化
        const handleFieldChange = (field: string, value: any) => {
            const actualValue = value?.target?.value !== undefined ? value.target.value : value;
            const newFormData = { ...formData, [field]: actualValue };
            setFormData(newFormData);
            onChange?.(newFormData);
        };

        // 处理搜索
        const handleSearch = () => {
            onSearch?.(formData);
        };

        // 处理重置
        const [resetKey, setResetKey] = useState(0);
        const handleReset = () => {
            const initialFormData: Record<string, any> = {};
            columns.forEach((column) => {
                if (column.type === "dateRange") {
                    initialFormData[column.field] = null;
                } else if (column.type === "numberRange") {
                    initialFormData[`${column.field}Min`] = null;
                    initialFormData[`${column.field}Max`] = null;
                } else {
                    initialFormData[column.field] = null;
                }
            });

            setFormData(initialFormData);
            setResetKey((prev) => prev + 1);

            // 强制更新 ref 的值
            if (ref && typeof ref === 'object' && ref.current) {
                ref.current.getValues = () => initialFormData;
            }

            onChange?.(initialFormData);
            onReset?.();
        };

        // 处理展开/收起
        const handleToggleExpand = () => {
            setExpanded(!expanded);
        };

        // 渲染搜索项
        const renderField = (column: SearchColumn) => {
            const { type, field, label, tooltip, placeholder, options, ...rest } = column;
            const commonProps = {
                size: "xs" as const,
                label,
                value: formData[field] ?? "",
                onChange: (value: any) => handleFieldChange(field, value),
                style: { label: { width: labelWidth } },
                ...(tooltip ? { description: tooltip } : {}),
            };

            switch (type) {
                case "text":
                    return (
                        <TextInput
                            key={`${field}-${resetKey}`}
                            {...commonProps}
                            placeholder={placeholder || `请输入 ${label}`}
                        />
                    );
                case "select":
                    return (
                        <Select
                            key={`${field}-${resetKey}`}
                            {...commonProps}
                            placeholder={placeholder || `请选择 ${label}`}
                            allowDeselect
                            clearable
                            searchable
                            data={
                                options?.map((opt) => ({
                                    value: opt.value.toString(),
                                    label: opt.label,
                                })) || []
                            }
                        />
                    );
                case "number":
                    return (
                        <NumberInput
                            key={`${field}-${resetKey}`}
                            {...commonProps}
                            placeholder={placeholder || `请输入 ${label}`}
                            decimalScale={rest.precision}
                            step={rest.step}
                            min={rest.min}
                            max={rest.max}
                        />
                    );
                case "numberRange":
                    return (
                        <Group wrap="nowrap">
                            <NumberInput
                                key={`${field}Min-${resetKey}`}
                                size="xs"
                                label={label}
                                value={formData[`${field}Min`] || ""}
                                onChange={(value) => handleFieldChange(`${field}Min`, value)}
                                style={{ label: { width: labelWidth } }}
                                placeholder="最小值"
                                decimalScale={rest.precision}
                                step={rest.step}
                                min={rest.min}
                                max={rest.max}
                            />
                            <NumberInput
                                key={`${field}Max-${resetKey}`}
                                size="xs"
                                label=" "
                                value={formData[`${field}Max`] || ""}
                                onChange={(value) => handleFieldChange(`${field}Max`, value)}
                                style={{ label: { width: 0 } }}
                                placeholder="最大值"
                                decimalScale={rest.precision}
                                step={rest.step}
                                min={rest.min}
                                max={rest.max}
                            />
                        </Group>
                    );
                case "date":
                    return (
                        <DateInput
                            key={`${field}-${resetKey}`}
                            {...commonProps}
                            valueFormat="YYYY-MM-DD"
                            placeholder={placeholder || `请选择 ${label}`}
                            value={
                                column.type === "dateRange"
                                    ? undefined
                                    : column.defaultValue && !Array.isArray(column.defaultValue)
                                    ? dayjs(column.defaultValue).toDate()
                                    : undefined
                            }
                        />
                    );
                case "dateRange":
                    return (
                        <DatePickerInput
                            key={`${field}-${resetKey}`}
                            {...commonProps}
                            type="range"
                            valueFormat="YYYY-MM-DD"
                            placeholder={placeholder || `请选择 ${label}`}
                            value={
                                column.defaultValue &&
                                Array.isArray(column.defaultValue) &&
                                column.defaultValue.length === 2
                                    ? [
                                          dayjs(column.defaultValue[0]).toDate(),
                                          dayjs(column.defaultValue[1]).toDate(),
                                      ]
                                    : undefined
                            }
                        />
                    );
                default:
                    return null;
            }
        };

        useImperativeHandle(ref, () => ({
            getValues: () => ({...formData}),
        }));

        return (
            <Stack>
                <Grid gutter={10}>
                    {visibleColumns.map((column) => (
                        <Grid.Col
                            span={{ base: 12, md: 3 }}
                            key={column.field}
                        >
                            {renderField(column)}
                        </Grid.Col>
                    ))}
                    <Grid.Col
                        span={{ base: 12, md: 3 }}
                        className="tw-flex tw-items-end tw-justify-end"
                    >
                        <Group gap="sm">
                            <Button
                                size="xs"
                                variant="outline"
                                leftSection={<ArrowClockwise />}
                                onClick={handleReset}
                            >
                                重置
                            </Button>
                            <Button
                                size="xs"
                                variant="filled"
                                leftSection={<MagnifyingGlass />}
                                onClick={handleSearch}
                            >
                                搜索
                            </Button>
                            {showExpandButton && (
                                <Button
                                    size="xs"
                                    variant="subtle"
                                    leftSection={expanded ? <CaretUp /> : <CaretDown />}
                                    onClick={handleToggleExpand}
                                >
                                    {expanded ? "收起" : "展开"}
                                </Button>
                            )}
                        </Group>
                    </Grid.Col>
                </Grid>
            </Stack>
        );
    }
);

export default SearchForm;
