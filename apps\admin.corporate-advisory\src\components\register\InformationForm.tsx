import {
    Button,
    DefaultMantineColor,
    Grid,
    GridColProps,
    Group,
    Input,
    Modal,
    Select,
    Stack,
    TextInput,
    Title,
} from "@mantine/core";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { Check, CheckCircle, Warning } from "@phosphor-icons/react";
import {
    FieldErrors,
    useForm,
    UseFormGetValues,
    UseFormRegister,
    UseFormSetValue,
} from "react-hook-form";
import { useMemoizedFn, useRequest } from "ahooks";
import api from "@/apis";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useContext, useState } from "react";
import { DatePickerInput } from "@mantine/dates";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import InfoValidateModal from "../modals/register/InfoValidateModal";
import useRegisterStore, { RegisterInfoState } from "@/store/register";
import { useShallow } from "zustand/react/shallow";
import { useEventBus } from "@/utils/eventBus";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import PhoneInput from "@code.8cent/react/components/PhoneInput";
import useDataStore from "@code.8cent/store/data";
import CountrySelect from "@code.8cent/react/components/CountrySelect";
import AddressInput from "@code.8cent/react/components/AddressInput";

type RegisterInformationFormInput = {
    addressCity: string;
    addressCountry: string;
    addressPostcode: string;
    addressStreet: string;
    addressUnit: string;
    addressState: string;
    addressDistrict: string;
    birthDate: string;
    gender: string;
    idNumber: string;
    name: string;
};

const registerInformationSchema = z.object({
    name: z.string().min(1, "form.enter.name"),
    idNumber: z.string().min(1, "form.enter.id"),
    gender: z.string().min(1, "form.enter.gender"),
    birthDate: z.string().date("form.enter.date.of.birth"),
    addressCity: z.string().min(1, "form.enter.city"),
    addressCountry: z.string().min(1, "form.enter.country"),
    addressPostcode: z.string().min(1, "form.enter.postal.code"),
    addressStreet: z.string().min(1, "form.enter.street"),
    addressUnit: z.string().min(1, "form.enter.unit"),
    addressState: z.string().min(1, "form.enter.state"),
    addressDistrict: z.string().min(1, "form.enter.district"),
});

const initalRegisterInformationFormValues: RegisterInformationFormInput = {
    name: "",
    idNumber: "",
    gender: "",
    birthDate: "",
    addressCity: "",
    addressCountry: "",
    addressPostcode: "",
    addressStreet: "",
    addressUnit: "",
    addressState: "",
    addressDistrict: "",
};

const ValidateStatusSection: React.FC<{
    status: boolean;
    openValidate: () => void;
}> = ({ status, openValidate }) => {
    const lang = useSettingStore.use.lang();

    const resolveValidationStatus = useMemoizedFn((value: boolean) => {
        if (value === true) {
            return {
                icon: (
                    <CheckCircle
                        weight="fill"
                        size={20}
                    />
                ),
                color: "green" as DefaultMantineColor,
                text: t("introduction.label.verify", lang),
            };
        } else {
            return {
                icon: (
                    <Warning
                        weight="fill"
                        size={20}
                    />
                ),
                color: "yellow" as DefaultMantineColor,
                text: t("introduction.label.unverify", lang),
            };
        }
    });

    const { icon, color, text } = resolveValidationStatus(status);

    return (
        <Button
            onClick={() => {
                if (status !== true) {
                    openValidate();
                }
            }}
            variant="transparent"
            color={color}
            leftSection={icon}
        >
            {text}
        </Button>
    );
};

const RegisterInformationForm = () => {
    const lang = useSettingStore.use.lang();

    const [validate, setValidate] = useState<{
        phone: boolean;
        email: boolean;
    }>({
        phone: false,
        email: false,
    });

    const alert = useModalStore.use.openAlert();

    const countryDatas = useDataStore.use.countryDatas();

    const setRegisterInfoValue = useRegisterStore.use.setRegisterInfoValue();

    const bus = useEventBus();

    const { phone, email, prefixID, nationalityID } = useRegisterStore(
        useShallow((state) => ({
            phone: state.phone,
            email: state.email,
            prefixID: state.prefixID,
            nationalityID: state.nationalityID,
        }))
    );

    const openModal = useModalStore.use.open();

    const [validateType, setValidateType] = useState<"email" | "phone">();

    const {
        register,
        formState: { errors },
        handleSubmit,
        setValue,
        getValues,
    } = useForm<RegisterInformationFormInput>({
        defaultValues: initalRegisterInformationFormValues,
        resolver: zodResolver(registerInformationSchema),
    });

    const { run: submit, loading: submitting } = useRequest(
        async (data: RegisterInformationFormInput) => {
            if (validate.email === true && validate.phone === true) {
                let confirmRes = await api.register.confirm({
                    ...data,
                    email,
                    phone,
                    prefixID,
                    nationalityID,
                });

                if ("error" in confirmRes) {
                    alert(t("form.sign.up.information", lang), confirmRes.message, "danger");
                    return;
                }

                setRegisterInfoValue("token", confirmRes.token);

                bus.emit("account.register.step", 3);
            } else {
                alert(
                    t("form.sign.up.information", lang),
                    t("form.verify.email.phone", lang),
                    "danger"
                );
            }
        },
        {
            manual: true,
        }
    );

    return (
        <Stack>
            <form onSubmit={handleSubmit(submit, (err) => console.log(err))}>
                <Grid>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                        <Stack>
                            <CountrySelect
                                flagKey="countryISOCode2"
                                labelKey={`country${lang}` as keyof CountryDataItem}
                                valueKey="countryID"
                                data={countryDatas}
                                label={t("introduction.nationality", lang)}
                                allowDeselect={false}
                                searchable={true}
                                value={String(nationalityID)}
                                readOnly
                            />
                            <TextInput
                                label={t("introduction.label.last_name", lang)}
                                {...register("name")}
                                error={errors.name ? true : false}
                            />
                            <TextInput
                                label={t("form.id.number", lang)}
                                {...register("idNumber")}
                                error={errors.idNumber ? true : false}
                            />
                            <Select
                                label={t("form.gender", lang)}
                                placeholder={t("form.select.gender", lang)}
                                data={[
                                    {
                                        label: t("form.gender.male", lang),
                                        value: "M",
                                    },
                                    {
                                        label: t("form.gender.female"),
                                        value: "F",
                                    },
                                ]}
                                {...register("gender")}
                                onChange={(val) => {
                                    setValue("gender", val, {
                                        shouldValidate: true,
                                    });
                                }}
                                error={errors.gender ? true : false}
                            />
                            <DatePickerInput
                                label={t("form.birth.date", lang)}
                                placeholder={t("form.enter.birth.date", lang)}
                                valueFormat="YYYY-MM-DD"
                                {...register("birthDate")}
                                onChange={(e) => {
                                    console.log(e);
                                    setValue("birthDate", dayjs(e).format("YYYY-MM-DD"), {
                                        shouldValidate: true,
                                    });
                                }}
                                error={errors.birthDate ? true : false}
                            />
                        </Stack>
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                        <Stack>
                            <PhoneInput<CountryDataItem>
                                label={t("project.company_form.label.phone", lang)}
                                data={countryDatas}
                                prefixFlagKey="countryISOCode2"
                                prefixLabelKey="countryCode"
                                prefixValueKey="countryID"
                                prefixProps={{
                                    w: 120,
                                    value: String(prefixID),
                                }}
                                inputProps={{
                                    rightSectionWidth: 120,
                                    rightSection: (
                                        <ValidateStatusSection
                                            status={validate.phone}
                                            openValidate={() => {
                                                setValidateType("phone");
                                                openModal("profileValidate");
                                            }}
                                        />
                                    ),
                                    value: String(phone),
                                }}
                                readOnly
                            />
                            <TextInput
                                label={t("form.email.address", lang)}
                                value={email}
                                rightSectionWidth={120}
                                rightSection={
                                    <ValidateStatusSection
                                        status={validate.email}
                                        openValidate={() => {
                                            setValidateType("email");
                                            openModal("profileValidate");
                                        }}
                                    />
                                }
                                readOnly
                            />
                            <AddressInput<RegisterInformationFormInput, CountryDataItem>
                                label={t("introduction.label.address", lang)}
                                errors={{
                                    unit: errors.addressUnit ? true : false,
                                    street: errors.addressStreet ? true : false,
                                    city: errors.addressCity ? true : false,
                                    district: errors.addressDistrict ? true : false,
                                    state: errors.addressState ? true : false,
                                    postcode: errors.addressPostcode ? true : false,
                                    country: errors.addressCountry ? true : false,
                                }}
                                addressFieldMap={{
                                    unit: {
                                        key: "addressUnit",
                                        placeholder: t("introduction.label.unit", lang),
                                    },
                                    street: {
                                        key: "addressStreet",
                                        placeholder: t("introduction.label.street", lang),
                                    },
                                    city: {
                                        key: "addressCity",
                                        placeholder: t("introduction.label.city", lang),
                                    },
                                    district: {
                                        key: "addressDistrict",
                                        placeholder: t("introduction.label.district", lang),
                                    },
                                    state: {
                                        key: "addressState",
                                        placeholder: t("introduction.label.state", lang),
                                    },
                                    postcode: {
                                        key: "addressPostcode",
                                        placeholder: t("introduction.label.postcode", lang),
                                    },
                                    country: {
                                        key: "addressCountry",
                                        placeholder: t("introduction.label.country", lang),
                                    },
                                }}
                                countrySelectProps={{
                                    data: countryDatas,
                                    flagKey: "countryISOCode2",
                                    labelKey: `country${lang}` as keyof CountryDataItem,
                                    valueKey: "countryID",
                                }}
                                addressData={getValues()}
                                onAddressDataChange={(addressData) => {
                                    for (const key in addressData) {
                                        setValue(
                                            key as keyof RegisterInformationFormInput,
                                            addressData[key],
                                            { shouldValidate: true }
                                        );
                                    }
                                }}
                            />
                            <Group
                                justify="end"
                                className="tw-mt-10"
                            >
                                <CnaButton
                                    color="basic.4"
                                    variant="outline"
                                    loading={submitting}
                                    onClick={() => {
                                        bus.emit("account.register.step", 1);
                                    }}
                                >
                                    {t("common.prev.page", lang)}
                                </CnaButton>

                                <CnaButton
                                    color="basic"
                                    leftSection={<Check />}
                                    type="submit"
                                    loading={submitting}
                                >
                                    {t("common.next.page", lang)}
                                </CnaButton>
                            </Group>
                        </Stack>
                    </Grid.Col>
                </Grid>
            </form>
            <InfoValidateModal
                type={validateType}
                onValidteSuccess={(type) => {
                    if (type === "email") {
                        setValidate((prev) => ({
                            ...prev,
                            email: true,
                        }));
                    }

                    if (type === "phone") {
                        setValidate((prev) => ({
                            ...prev,
                            phone: true,
                        }));
                    }
                }}
            />
        </Stack>
    );
};

export default RegisterInformationForm;
