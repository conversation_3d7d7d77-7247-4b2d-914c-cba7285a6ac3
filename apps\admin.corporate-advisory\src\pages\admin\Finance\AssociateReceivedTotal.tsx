import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Eye } from "@phosphor-icons/react";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import { createColumnHelper } from "@tanstack/react-table";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import { exportToExcel } from "@/utils/xlsx";

const columnHelper = createColumnHelper<TAssociateReceivedTotal>();

const STATUS_MAP = {
    0: "暂存",
    1: "审核中",
    2: "已审核",
} as const;

const GROUP_ID_MAP = {
    1: "联盟合伙人",
    2: "管理合伙人",
    3: "三三制",
} as const;

const AssociateReceivedTotal = () => {
    const { lang } = useSettingStore();

    const tableRef = useRef<DataTableRef | null>(null);
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();

    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [apiTotal, setApiTotal] = useState({
        total_fee: "0",
        total_put_amount: "0",
        total_fee_total: "0",
        total_start_balance: "0",
        total_end_balance: "0",
    });

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                start_date: globalFilters?.dateRange?.[0]
                    ? dayjs(globalFilters.dateRange[0]).format("YYYY-MM-DD 00:00:00")
                    : "",
                end_date: globalFilters?.dateRange?.[1]
                    ? dayjs(globalFilters.dateRange[1]).format("YYYY-MM-DD 23:59:59")
                    : "",
                page,
                pageSize,
                profile_code: globalFilters?.profile_code || "",
            };

            const { items, paginate, total } = await api.finance.associateReceivedTotal(
                requestParams
            );
            setData(items);
            setTotalCount(paginate?.total);
            setApiTotal(total);
        } finally {
            setLoading(false);
        }
    };

    const refreshData = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    const rowActions = (row) => [
        {
            key: "view",
            label: "查看",
            icon: <Eye size={16} />,
            onClick: () => {
                console.log(row);
            },
        },
    ];

    const tableColumns = [
        columnHelper.accessor("profileID", {
            header: "合伙人ID",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => "合计",
        }),
        columnHelper.accessor("sum_fee", {
            header: "本期应收",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.total_fee,
        }),
        columnHelper.accessor("sum_put_amount", {
            header: "本期已收",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.total_put_amount,
        }),
        columnHelper.accessor("sum_fee_count", {
            header: "累计已收",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.total_fee_total,
        }),
        columnHelper.accessor("end_balance", {
            header: "期末余额",
            enableSorting: false,
            cell: (info) => info.getValue(),
            footer: () => apiTotal.total_end_balance,
        }),
        columnHelper.accessor("status", {
            header: "状态",
            enableSorting: false,
            cell: (info) => STATUS_MAP[info.getValue()], // todo confirm status
        }),
        columnHelper.accessor("profileName", {
            header: "合伙人姓名",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profilePartnerCode", {
            header: "合伙人编码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileNRIC", {
            header: "身份证",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("groupId", {
            header: "合伙人角色",
            enableSorting: false,
            cell: (info) => GROUP_ID_MAP[info.getValue()],
        }),
        columnHelper.accessor("teamRankName", {
            header: "三三制等级",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("sonName", {
            header: "旗下合伙人",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("divideTypeName", {
            header: "分成类型",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => {
                return <TableRowDropActionMenu items={rowActions(info.row.original)} />;
            },
        }),
    ];

    const handleExport = useMemoizedFn(async () => {
        const { globalFilters } = tableRef.current?.getState() || {};

        const requestParams = {
            start_date: globalFilters?.dateRange?.[0]
                ? dayjs(globalFilters.dateRange[0]).format("YYYY-MM-DD 00:00:00")
                : "",
            end_date: globalFilters?.dateRange?.[1]
                ? dayjs(globalFilters.dateRange[1]).format("YYYY-MM-DD 23:59:59")
                : "",
            page: 1,
            pageSize: totalCount,
            profile_code: globalFilters?.profile_code || "",
        };

        const { items, total } = await api.finance.associateReceivedTotal(requestParams);

        exportToExcel(
            items,
            [
                { key: "profileID", title: "合伙人ID" },
                { key: "sum_fee", title: "本期应收" },
                { key: "sum_put_amount", title: "本期已收" },
                { key: "sum_fee_count", title: "累计已收" },
                { key: "end_balance", title: "期末余额" },
                { key: "status", title: "状态", format: (value) => STATUS_MAP[value] },
                { key: "profileName", title: "合伙人姓名" },
                { key: "profilePartnerCode", title: "合伙人编码" },
                { key: "profileNRIC", title: "身份证" },
                { key: "groupId", title: "合伙人角色", format: (value) => GROUP_ID_MAP[value] },
                { key: "teamRankName", title: "三三制等级" },
                { key: "sonName", title: "旗下合伙人" },
                { key: "divideTypeName", title: "分成类型" },
            ],
            `合伙人加盟费应收汇总表_${dayjs().format("YYYYMMDD")}.xlsx`,
            {
                profileID: "合计",
                sum_fee: total.total_fee.toString(),
                sum_put_amount: total.total_put_amount.toString(),
                total_fee_count: total.total_fee_total.toString(),
                end_balance: total.total_end_balance.toString(),
            }
        );
    });

    const confirmExport = useMemoizedFn(async () => {
        openConfirm({
            title: "提示",
            message: "确定要导出数据吗？",
            onConfirm: handleExport,
        });
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="合伙人加盟费应收汇总表"
                desc="查询合伙人加盟费应收汇总"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={confirmExport}
                globalFilterFields={[
                    {
                        field: "dateRange",
                        label: "汇总日期范围",
                        type: "dateRange",
                    },
                    {
                        field: "profile_code",
                        label: "合伙人编码",
                        type: "text",
                    },
                ]}
            />
        </Stack>
    );
};

export default AssociateReceivedTotal;
