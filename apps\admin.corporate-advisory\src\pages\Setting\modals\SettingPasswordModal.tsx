import { <PERSON><PERSON>, Mo<PERSON>, Group, Button, PasswordInput } from "@mantine/core";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import { PasswordCheckerInput, CnaButton } from "@code.8cent/react/components";
import { useContext } from "react";
import SettingPageContext from "../context";
import api from "@/apis";
import { Check, X } from "@phosphor-icons/react";
import noty from "@code.8cent/react/noty";
import { Controller } from "react-hook-form";
import useProfileStore from "@/store/profile";

// 密码验证规则
const passwordRules = [
    {
        text: "password.requirement.letter",
        validate: (password: string) => password.length >= 8,
    },
    {
        text: "password.requirement.capital.letter",
        validate: (password: string) => /[A-Z]/.test(password),
    },
    {
        text: "password.requirement.small.letter",
        validate: (password: string) => /[a-z]/.test(password),
    },
    {
        text: "password.requirement.number",
        validate: (password: string) => /\d/.test(password),
    },
];

// 表单验证schema
const passwordSchema = z
    .object({
        currentPassword: z.string().min(1, "password.current.empty"),
        newPassword: z
            .string()
            .min(1, "password.new.empty")
            .refine(
                (password) => passwordRules.every((rule) => rule.validate(password)),
                "forget_password.label.password.invalid"
            ),
        confirmNewPassword: z.string().min(1, "password.enter.empty"),
    })
    .refine((data) => data.newPassword === data.confirmNewPassword, {
        message: "确认新密码不一致",
        path: ["confirmNewPassword"],
    });

type PasswordFormData = z.infer<typeof passwordSchema>;

const SettingPasswordModal = () => {
    const lang = useSettingStore.use.lang();
    const { SettingPasswordModal: opened, close } = useContext(SettingPageContext);

    const { profileID } = useProfileStore();

    const {
        control,
        handleSubmit,
        reset,
        trigger,
        formState: { isSubmitting, errors },
    } = useForm<PasswordFormData>({
        resolver: zodResolver(passwordSchema),
        defaultValues: {
            currentPassword: "",
            newPassword: "",
            confirmNewPassword: "",
        },
    });

    const onSubmit = async (data: PasswordFormData) => {
        if (!profileID) {
            noty.error(t("password.update.failed", lang));
            return;
        }

        try {
            const res = await api.account.updatePassword({
                id: profileID,
                password: data.newPassword,
                // todo 接口只要 newPassword, 后面的参数前端处理，后续接口改动，这里需要修改
                newPassword: data.currentPassword,
                confirmPassword: data.confirmNewPassword,
            });

            if (res) {
                noty.success(t("password.update.success", lang));
                close("SettingPasswordModal");
                reset();

                // todo 是否需要重新登录？当前修改密码后，原有的登录token接口校验依然有效
            } else {
                noty.error(t("password.update.failed", lang));
            }
        } catch (error) {
            noty.error(t("password.update.failed", lang));
        }
    };

    return (
        <Modal
            opened={opened}
            onClose={() => {
                close("SettingPasswordModal");
                reset();
            }}
            title="修改登录密码"
            size="md"
            classNames={{
                header: "tw-border-b tw-border-gray-200",
            }}
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack className="tw-mt-4">
                    <Controller
                        name="currentPassword"
                        control={control}
                        render={({ field }) => (
                            <PasswordInput
                                {...field}
                                label={t("password.current", lang)}
                                error={errors.currentPassword?.message}
                            />
                        )}
                    />

                    <PasswordCheckerInput
                        control={control}
                        name="newPassword"
                        label={t("password.new", lang)}
                        rules={passwordRules}
                        trigger={trigger}
                    />

                    <Controller
                        name="confirmNewPassword"
                        control={control}
                        render={({ field }) => (
                            <PasswordInput
                                {...field}
                                label={t("password.confirm", lang)}
                                error={errors.confirmNewPassword?.message}
                            />
                        )}
                    />
                </Stack>

                <Group
                    justify="end"
                    className="tw-border-t tw-pt-4 tw-mt-4"
                >
                    <CnaButton
                        type="submit"
                        loading={isSubmitting}
                        color="basic"
                        leftSection={<Check weight="bold" />}
                    >
                        {t("common.save", lang)}
                    </CnaButton>
                    <Button
                        color="basic"
                        variant="outline"
                        onClick={() => {
                            close("SettingPasswordModal");
                            reset();
                        }}
                        leftSection={<X weight="bold" />}
                    >
                        {t("common.close", lang)}
                    </Button>
                </Group>
            </form>
        </Modal>
    );
};

export default SettingPasswordModal;
