import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const visitor = {
    applyList: async (params: TVisitorSearchParams) => {
        const { error, result } = await cnaRequest<TVisitorApplyResponse>(
            "/api/v1/admin/visitorLogin/apply/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    applyDetail: async (id: number) => {
        const { error, result } = await cnaRequest<TVisitorApply>(
            "/api/v1/admin/visitorLogin/apply/detail",
            "GET",
            {
                id,
            }
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    reviewApply: async (params) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/visitorLogin/apply/check",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    downloadCard: async (params) => {
        const { error, result } = await cnaRequest<Blob>(
            "/api/v1/admin/visitorLogin/apply/downloadCard",
            "POST",
            params,
            {
                responseType: "blob",
            }
        );

        if (!error) {
            return result;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    vipList: async (params: TVisitorSearchParams) => {
        const { error, result } = await cnaRequest<TVisitorsVipResponse>(
            "/api/v1/admin/visitorLogin/signin/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    vipDetail: async (id: number) => {
        const { error, result } = await cnaRequest<TVisitorVip>(
            "/api/v1/admin/visitorLogin/signin/detail",
            "GET",
            { id }
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    visitorReport: async (params) => {
        const { error, result } = await cnaRequest<TVisitorReport>(
            "/api/v1/admin/visitorLogin/report/report",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    reportList: async (params: TVisitorSearchParams) => {
        const { error, result } = await cnaRequest<TVisitorReportResponse>(
            "/api/v1/admin/visitorLogin/report/index",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    reportDetail: async (id: number) => {
        const { error, result } = await cnaRequest<TVisitorReport>(
            "/api/v1/admin/visitorLogin/report/detail",
            "GET",
            { id }
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    downloadInvitationCard: async (params: {
        id: number;
        type: "cn" | "en";
        form: "app" | "vip";
    }) => {
        const { id, type, form } = params;

        let url = "";
        if (form === "app") {
            url = "/api/v1/admin/visitorLogin/apply/downloadCard";
        } else if (form === "vip") {
            url = "/api/v1/admin/visitorLogin/signin/downloadCard";
        }

        const { error, result } = await cnaRequest(
            url,
            "POST",
            { id, type },
            { responseType: "blob" }
        );

        if (!error) {
            return result;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default visitor;
