import api from "@/apis";
import useModalStore from "@/store/modal";
import { useState, useCallback, useMemo } from "react";
import { Group, Modal, Stack, TextInput } from "@mantine/core";
import { useMemoizedFn, useDebounce, useUpdateEffect, useMount } from "ahooks";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { X, MagnifyingGlass } from "@phosphor-icons/react";
import { useShallow } from "zustand/react/shallow";
import ModalFooter from "@/components/common/ModalFooter";
import CnaAdminTable from "@/components/common/CnaAdminTable";
import { CnaButton } from "@code.8cent/react/components";
import dayjs from "dayjs";

type Column = {
    header: string;
    accessor: string;
    sortable?: boolean;
    valueFormat?: (value: any) => string | React.ReactNode;
};

type Item = {
    [key: string]: any;
};

type TTableProps = {
    key: string;
    title: string;
    columns: Column[];
    fetchData: (
        page: number,
        pageSize: number,
        ...args: any
    ) => Promise<{ data: Item[]; totalPage: number }>;
    rowActions?: (row: Item) => React.ReactNode;
};

const TableModal = () => {
    const lang = useSettingStore.use.lang();

    // 获取传入的参数
    const tableParams = useModalStore((state) => state.modalParams.tableModal);
    const tableKey = tableParams?.tableKey;
    const profileID = tableParams?.profileID;

    const [currentTableKey, setCurrentTableKey] = useState(tableKey);

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.tableModal,
            close: state.close,
        }))
    );

    const [keyword, setKeyword] = useState("");
    const debouncedKeyword = useDebounce(keyword, { wait: 500 });

    const handleKeywordChange = useMemoizedFn((e: React.ChangeEvent<HTMLInputElement>) => {
        setKeyword(e.target.value);
    });

    const closeModal = useMemoizedFn(() => {
        close("tableModal");
    });

    const fetchDataForBilling = useCallback(
        async (page: number, page_size: number) => {
            if (!profileID) {
                return {
                    data: [],
                    totalPage: 0,
                };
            }

            const response = await api.bill.associatePayout({
                page,
                page_size,
                id: profileID,
            });

            return {
                data: response?.items,
                totalPage: response?.paginate?.total_page,
            };
        },
        [profileID]
    );

    const fetchDataForCompanyBilling = useCallback(
        async (page: number, page_size: number) => {
            const response = await api.bill.list({
                page,
                page_size,
                keyword: debouncedKeyword,
                billCompanyID: profileID,
            });

            return {
                data: response.items,
                totalPage: response.paginate.total_page,
            };
        },
        [debouncedKeyword, profileID]
    );

    const fetchDataForCompany = useCallback(
        async (page: number, page_size: number) => {
            if (!profileID) {
                return {
                    data: [],
                    totalPage: 0,
                };
            }

            const response = await api.client.associateCustomer({
                page,
                page_size,
                id: profileID,
            });

            return {
                data: response?.items,
                totalPage: response?.paginate?.total_page,
            };
        },
        [profileID]
    );

    const fetchDataForBenefit = useCallback(
        async (page: number, page_size: number) => {
            const response = await api.benefit.applications({
                page,
                page_size,
                keyword: debouncedKeyword,
                profileID,
            });

            return {
                data: response.items,
                totalPage: response.paginate.total_page,
            };
        },
        [debouncedKeyword, profileID]
    );

    const fetchDataForLog = useCallback(
        async (page: number, page_size: number) => {
            const response = await api.activityLog.list({
                page,
                page_size,
                keyword: debouncedKeyword,
                profileID,
            });

            return {
                data: response.items,
                totalPage: response.paginate.total_page,
            };
        },
        [debouncedKeyword, profileID]
    );

    const getColumns = (lang: string) => ({
        billing: [
            {
                header: "支付项目",
                accessor: "project_name",
            },
            {
                header: "项目明细",
                accessor: "detail_name",
            },
            {
                header: "单号",
                accessor: "paymentNumber",
            },
            {
                header: "金额",
                accessor: "fee",
            },
            {
                header: "支付时间",
                accessor: "paytime",
                valueFormat: (row: any) =>
                    row.paytime && dayjs(row.paytime).format("YYYY-MM-DD HH:mm:ss"),
            },
            {
                header: "付款方式",
                accessor: "pay_type", // 1微信支付;2支付宝;3网银
                valueFormat: (row: any) => {
                    if (row.pay_type == 1) {
                        return "微信支付";
                    } else if (row.pay_type == 2) {
                        return "支付宝";
                    } else if (row.pay_type == 3) {
                        return "网银";
                    }
                },
            },
            {
                header: "创建日期",
                accessor: "createtime",
                valueFormat: (row: any) =>
                    row.createtime && dayjs(row.createtime).format("YYYY-MM-DD HH:mm:ss"),
            },
        ],
        companyBilling: [
            {
                header: "编码",
                accessor: "billPaymentCode",
            },
            {
                header: "状态",
                accessor: "billState",
            },
            {
                header: "企业名字",
                accessor: "billCompanyName",
            },
            {
                header: "详情",
                accessor: "billDescription",
            },
            {
                header: "数额",
                accessor: "billAmount",
            },
            {
                header: "创建日期",
                accessor: "created_at",
                valueFormat: (row: any) =>
                    row.created_at && dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
            },
            {
                header: "付款方式",
                accessor: "billPaymentType",
            },
        ],
        benefit: [
            {
                header: "福利申请编码",
                accessor: "benefitXID",
            },
            {
                header: "状态",
                accessor: "benefitStatus",
            },
            {
                header: "福利项目",
                accessor: `benefitInfo.benefitTitle${lang}`,
            },
            {
                header: "申请者名字",
                accessor: "profileName",
            },
            {
                header: "申请者身份证",
                accessor: "profileNRIC",
            },
            {
                header: "申请者编码",
                accessor: "profilePartnerCode",
            },
            {
                header: "申请日期",
                accessor: "created_at",
                valueFormat: (row: any) =>
                    row.created_at && dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
            },
        ],
        company: [
            {
                header: "名称",
                accessor: "name",
            },
            {
                header: "统一社会信用代码",
                accessor: "credit_code",
            },
            {
                header: "简介",
                accessor: "desc",
            },
            {
                header: "联系人",
                accessor: "contact_name",
            },
            {
                header: "联系人职务",
                accessor: "contact_position",
            },
            {
                header: "联系电话",
                accessor: "phone",
            },
            {
                header: "邮箱",
                accessor: "email",
            },
        ],
        log: [
            {
                header: "创建日期",
                accessor: "created_at",
                valueFormat: (row: any) =>
                    row.created_at && dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
            },
            {
                header: "操作内容",
                accessor: `desc${lang}`,
            },
        ],
        history: [
            {
                header: "创建日期",
                accessor: "created_at",
                valueFormat: (row: any) =>
                    row.created_at && dayjs(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
            },
            {
                header: "操作内容",
                accessor: "content",
            },
        ],
    });

    const tableSettings: Omit<TTableProps, "fetchData">[] = useMemo(
        () => [
            {
                key: "billing",
                title: "付款记录",
                columns: getColumns(lang).billing,
            },
            {
                key: "companyBilling",
                title: "付款记录",
                columns: getColumns(lang).companyBilling,
            },
            {
                key: "benefit",
                title: "福利申请",
                columns: getColumns(lang).benefit,
            },
            {
                key: "company",
                title: "企业客户",
                columns: getColumns(lang).company,
            },
            {
                key: "log",
                title: "活动日志",
                columns: getColumns(lang).log,
            },
            {
                key: "history",
                title: "登录足迹",
                columns: getColumns(lang).history,
            },
        ],
        []
    );

    const currentTableSetting = useMemo(() => {
        const setting = tableSettings.find((item) => item.key === currentTableKey);

        if (!setting) {
            return null;
        }

        const fetchDataMap = {
            billing: fetchDataForBilling,
            companyBilling: fetchDataForCompanyBilling,
            benefit: fetchDataForBenefit,
            company: fetchDataForCompany,
            log: fetchDataForLog,
            history: () => Promise.resolve({ data: [], totalPage: 0 }),
        };

        const columns =
            getColumns(lang)[setting.key as keyof ReturnType<typeof getColumns>] || setting.columns;

        return {
            ...setting,
            columns,
            fetchData: fetchDataMap[setting.key as keyof typeof fetchDataMap],
        };
    }, [
        currentTableKey,
        lang,
        fetchDataForBilling,
        fetchDataForCompanyBilling,
        fetchDataForBenefit,
        fetchDataForCompany,
        fetchDataForLog,
    ]);

    useMount(() => {
        setCurrentTableKey(tableKey);
    });

    useUpdateEffect(() => {
        setCurrentTableKey(tableKey);
    }, [tableKey]);

    const handleSearch = useCallback(() => {
        // 可以在这里添加搜索逻辑，例如刷新表格数据
        console.log("Searching with keyword:", debouncedKeyword);
    }, [debouncedKeyword]);

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            title={currentTableSetting?.title ?? ""}
            size="xl"
        >
            {currentTableSetting && (
                <Stack gap="md">
                    <Group>
                        <TextInput
                            className="tw-flex-1"
                            placeholder="搜索"
                            value={keyword}
                            onChange={handleKeywordChange}
                            leftSection={
                                <MagnifyingGlass
                                    weight="bold"
                                    size={16}
                                />
                            }
                        />
                        <CnaButton
                            size="sm"
                            variant="outline"
                            leftSection={<MagnifyingGlass size={16} />}
                            onClick={handleSearch}
                        >
                            搜索
                        </CnaButton>
                    </Group>

                    <CnaAdminTable
                        columns={currentTableSetting.columns}
                        fetchData={currentTableSetting.fetchData}
                        rowActions={currentTableSetting.rowActions}
                        defaultPageSize={5}
                    />

                    <ModalFooter
                        buttons={[
                            {
                                key: "close",
                                label: "关闭",
                                style: "outline",
                                leftSection: <X size={16} />,
                                onClick: closeModal,
                            },
                        ]}
                    />
                </Stack>
            )}
        </Modal>
    );
};

export default TableModal;
