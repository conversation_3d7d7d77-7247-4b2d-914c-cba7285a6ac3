import { Checkbox, Group, Modal, Radio, Stack, Textarea, TextInput } from "@mantine/core";
import React, { useEffect } from "react";
import useModalStore from "@/store/modal";
import { useShallow } from "zustand/react/shallow";
import { TimeInput, DateInput } from "@mantine/dates";
import CnaButton from "@code.8cent/react/components/CnaButton";
import { useSetState } from "ahooks";
import dayjs from "dayjs";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";

type TEvent = {
    start: string;
    desc: string;
    end: string;
    title: string;
    notify_type: string;
};

type TEventInnerProp = {
    title: string;
    start_date: string;
    start_time: string;
    end_date: string;
    end_time: string;
    desc: string;
};

const initialValues = (defaultValue?: string) => ({
    desc: "",
    start_date: dayjs(defaultValue ?? new Date()).format("YYYY-MM-DD"),
    start_time: dayjs(defaultValue ?? new Date()).format("HH:mm"),
    end_date: dayjs(defaultValue ?? new Date())
        .add(1, "hour")
        .format("YYYY-MM-DD"),
    end_time: dayjs(defaultValue ?? new Date())
        .add(1, "hour")
        .format("HH:mm"),
    title: "",
    notify_type: "0",
});

const CalendarEventCreationModal: React.FC<{
    defaultValue?: string;
    onEventCreate?: (event: TEvent) => void;
}> = ({ defaultValue, onEventCreate }) => {
    const lang = useSettingStore(useShallow((state) => state.lang));

    const [eventForm, setEventForm] = useSetState(initialValues(defaultValue));

    const [isAllDay, setIsAllDay] = React.useState(false);

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.calendarEventCreation,
            close: state.close,
        }))
    );

    const closeModal = () => {
        close("calendarEventCreation");
    };

    useEffect(() => {
        if (show === false) {
            setEventForm(initialValues());
        }
    }, [show]);

    useEffect(() => {
        setEventForm(initialValues(defaultValue));
    }, [defaultValue]);

    useEffect(() => {
        if (isAllDay === true) {
            setEventForm({
                start_time: "00:00",
                end_time: "23:59",
            });
        } else {
            setEventForm({
                start_time: dayjs().format("HH:mm"),
                end_time: dayjs().add(1, "hour").format("HH:mm"),
            });
        }
    }, [isAllDay]);

    return (
        <Modal
            opened={show}
            onClose={closeModal}
            size="lg"
            title={t("calendar.event.add", lang)}
            closeOnClickOutside={false}
        >
            <Stack>
                <TextInput
                    label={t("calendar.event.title", lang)}
                    value={eventForm.title}
                    onChange={(e) => {
                        setEventForm({ title: e.target.value });
                    }}
                />

                <Textarea
                    label={t("calendar.event.description", lang)}
                    value={eventForm.desc}
                    onChange={(e) => {
                        setEventForm({ desc: e.target.value });
                    }}
                />

                <Checkbox
                    checked={isAllDay}
                    onChange={(e) => {
                        setIsAllDay(e.target.checked);
                    }}
                    label={t("calendar.event.all.day", lang)}
                />

                <Group grow>
                    <DateInput
                        label={t("calendar.date.start", lang)}
                        placeholder={`${t("introduction.label.select", lang)}${t(
                            "calendar.date.start",
                            lang
                        )}`}
                        value={dayjs(eventForm.start_date).toDate()}
                        onChange={(val) =>
                            setEventForm({
                                start_date: dayjs(val).format("YYYY-MM-DD"),
                            })
                        }
                    />
                    {!isAllDay && (
                        <TimeInput
                            label={t("calendar.time.start", lang)}
                            placeholder={`${t("introduction.label.select", lang)}${t(
                                "calendar.time.start",
                                lang
                            )}`}
                            value={eventForm.start_time}
                            onChange={(e) => {
                                setEventForm({ start_time: e.target.value });
                            }}
                        />
                    )}
                </Group>
                <Group grow>
                    <DateInput
                        label={t("calendar.date.end", lang)}
                        placeholder={`${t("introduction.label.select", lang)}${t(
                            "calendar.date.end",
                            lang
                        )}`}
                        value={dayjs(eventForm.end_date).toDate()}
                        onChange={(val) =>
                            setEventForm({
                                end_date: dayjs(val).format("YYYY-MM-DD"),
                            })
                        }
                    />
                    {!isAllDay && (
                        <TimeInput
                            label={t("calendar.time.end", lang)}
                            placeholder={`${t("introduction.label.select", lang)}${t(
                                "calendar.time.end",
                                lang
                            )}`}
                            value={eventForm.end_time}
                            onChange={(e) => {
                                setEventForm({ end_time: e.target.value });
                            }}
                        />
                    )}
                </Group>
                <Radio.Group
                    label={t("calendar.event.notify_type", lang)}
                    value={eventForm.notify_type}
                    onChange={(e) => {
                        setEventForm({ notify_type: e });
                    }}
                >
                    <Group className="tw-mt-3">
                        <Radio
                            value={"0"}
                            label={t("calendar.event.notify_type.0", lang)}
                        />
                        <Radio
                            value={"1"}
                            label={t("calendar.event.notify_type.1", lang)}
                        />
                        <Radio
                            value={"2"}
                            label={t("calendar.event.notify_type.2", lang)}
                        />
                    </Group>
                </Radio.Group>
                <Group justify="end">
                    <CnaButton
                        color="basic"
                        onClick={() => {
                            typeof onEventCreate === "function" &&
                                onEventCreate({
                                    start: `${eventForm.start_date} ${eventForm.start_time}`,
                                    end: `${eventForm.end_date} ${eventForm.end_time}`,
                                    title: eventForm.title,
                                    notify_type: eventForm.notify_type,
                                    desc: eventForm.desc
                                });
                        }}
                    >
                        {t("calendar.event.create", lang)}
                    </CnaButton>
                </Group>
            </Stack>
        </Modal>
    );
};

export default CalendarEventCreationModal;
