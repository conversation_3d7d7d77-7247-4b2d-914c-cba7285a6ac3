import api from "@/apis";
import { Stack } from "@mantine/core";
import { useMemoizedFn, useRequest } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import { Plus, GearSix } from "@phosphor-icons/react";
import PageActionButtons from "@/components/common/PageActionButtons";
import Permission from "@/components/modals/account/Permission";
import Create from "@/components/modals/fieldType/Create";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import useModalStore from "@/store/modal";
import Remark from "@/components/modals/Remark";
import { createColumnHelper } from "@tanstack/react-table";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import FieldTypeTeamListModal from "@/components/modals/fieldType/FieldTypeTeamListModal";

const columnHelper = createColumnHelper<TFieldItemResponse & { department_name?: string }>();

const AdminFieldTypePage = () => {
    const lang = useSettingStore.use.lang();

    const openModal = useModalStore.use.open();

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const pageButtons = [
        {
            key: "add",
            leftSection: (
                <Plus
                    weight="bold"
                    size={14}
                />
            ),
            label: "创建",
            onClick: () => openModal("accountCreateUpdateModal"),
        },
        {
            key: "setting",
            leftSection: (
                <GearSix
                    weight="bold"
                    size={14}
                />
            ),
            label: "设置",
            onClick: () => { },
        },
    ];

    const tableColumns = [
        // columnHelper.accessor("id", {
        //     header: "ID",
        //     enableSorting: false,
        //     cell: (info) => info.getValue(),
        // }),
        columnHelper.accessor("name", {
            header: "名字",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("market_code", {
            header: "标识符",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        }),
    ];

    const rowActions = (row) => [
        {
            key: "info",
            label: "分支信息",
            onClick: () => openModal("fieldTypeTeamListModal", { detail: row }),
            disabled: row.id === 0,
        },
        // {
        //     key: "remark",
        //     label: "备注信息",
        //     onClick: () => openModal("remarkModal", { objID: row.profileID, type: 2 }),
        // },
        // {
        //     key: "permission",
        //     label: "权限设置",
        //     onClick: () => openModal("accountPermissionModal", { profileID: row.profileID }),
        // },
    ];

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters } = params;

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                page,
                page_size: pageSize,
                // getType: "teamList"
            };

            const { items, paginate } = await api.fieldType.list(requestParams,);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    // 获取部门列表
    const { data: departments = [] } = useRequest(api.department.all);

    // 刷新表格
    const refreshTable = useMemoizedFn(() => {
        tableRef.current?.refresh();
    });

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="行业类型管理"
            // desc="查询行政"
            />

            <PageActionButtons
                buttons={pageButtons}
                className="tw-fixed tw-right-6"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
            />

            {/* 渲染创建/更新弹窗 */}
            <Create
                onUpdateSuccess={refreshTable}
            />

            <FieldTypeTeamListModal  />

        </Stack>
    );
};

export default AdminFieldTypePage;
