import useModalStore from "@/store/modal";
import { useShallow } from "zustand/react/shallow";
import { Modal, Stack, Textarea, TextInput, Select, Avatar } from "@mantine/core";
import { useMemoizedFn } from "ahooks";
import { useFileViewer } from "@code.8cent/react/FileViewer";

const SignUpInfo = () => {
    const signupParams = useModalStore((state) => state.modalParams.activitySignupInfoModal);
    const signup = signupParams?.signup;

    const { openFileView } = useFileViewer();

    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.activitySignupInfoModal,
            close: state.close,
        }))
    );

    const closeModal = useMemoizedFn(() => {
        close("activitySignupInfoModal");
    });

    // 机构类型（可多选）
    const orgTypeOptions = [
        { value: "1", label: "政府" },
        { value: "2", label: "学术" },
        { value: "3", label: "企业" },
        { value: "4", label: "非政府组织" },
        { value: "5", label: "国际组织" },
        { value: "6", label: "其他" },
    ];

    // 餐饮禁忌（可多选）
    // 选择其他时候，可以输入内容
    const foodBanOptions = [
        { value: "1", label: "素食" },
        { value: "2", label: "清真" },
        { value: "3", label: "犹太洁食" },
        { value: "4", label: "乳糖不耐" },
        { value: "5", label: "坚果过敏" },
        { value: "6", label: "其他" },
    ];

    // 健康需求（可多选）
    // 选择医疗协助时候，可以输入内容
    const healthNeedOptions = [
        { value: "1", label: "轮椅通道" },
        { value: "2", label: "临时休息室" },
        { value: "3", label: "心脏病/高血压" },
        { value: "4", label: "医疗协助" },
    ];

    // 交通安排（可多选）
    // 选择机场VIP接送,自驾时候，可以输入内容
    const transportNeedOptions = [
        { value: "1", label: "机场VIP接送" },
        { value: "2", label: "酒店穿梭巴士" },
        { value: "3", label: "自驾" },
    ];

    // 住宿偏好（可多选）
    const roomNeedOptions = [
        { value: "1", label: "组委会协议酒店" },
        { value: "2", label: "自行安排" },
        { value: "3", label: "需要套房" },
        { value: "4", label: "无烟房" },
        { value: "5", label: "同一楼层" },
    ];

    // 宗教/文化（可多选）
    // 选择禁忌颜色/物品时候，可以输入内容
    const cultureOptions = [
        { value: "1", label: "祈祷室需求" },
        { value: "2", label: "禁忌猪肉" },
        { value: "3", label: "禁忌饮酒" },
        { value: "4", label: "禁忌颜色/物品" },
    ];

    // 社交偏好（可多选）
    const socialHobbyOptions = [
        { value: "1", label: "参与企业对接" },
        { value: "2", label: "加入学者交流群" },
        { value: "3", label: "不参与晚间社交活动" },
    ];

    // 媒体互动（可多选）
    const mediaOptions = [
        { value: "1", label: "接受采访" },
        { value: "2", label: "仅限指定媒体" },
        { value: "3", label: "不参与采访" },
    ];

    // 证件信息隐藏（可多选）
    const idHideOptions = [
        { value: "1", label: "隐藏职务" },
        { value: "2", label: "隐藏机构名称（仅显示姓名）" },
    ];

    // 贵宾休息室使用权（可多选）
    const vipRoomOptions = [{ value: "1", label: "需要专属休息室（仅限部长级/CEO）" }];

    // 解析多选值
    const parseMultiSelect = (value: string | undefined, options: any[]) => {
        if (!value) return [];
        const values = value.split(",").map((v) => v.trim());
        return values.map((v) => {
            const option = options.find((opt) => opt.value === v);
            return option ? option.label : v;
        });
    };

    // 渲染多选字段
    const renderMultiSelect = (label: string, value: string | undefined, options: any[]) => {
        const selectedLabels = parseMultiSelect(value, options);
        return (
            <Textarea
                label={label}
                value={selectedLabels.length > 0 ? selectedLabels.join("\n") : ""}
                readOnly
                minRows={selectedLabels.length > 0 ? selectedLabels.length : 1}
            />
        );
    };

    // 预览照片
    const previewFile = useMemoizedFn((url: string) => {
        // 把 url 中的域名给去除
        const urlWithoutDomain = url.replace(/^(https?:\/\/)?([^\/]+)\//, "");
        openFileView(
            `${window.api_base_url}/api/v1/admin/file/resource?path=${urlWithoutDomain}&type=remote`,
            {
                title: "照片",
            }
        );
    });

    return (
        <Modal
            title={`报名信息详情`}
            opened={show}
            onClose={closeModal}
            size="lg"
        >
            <Stack>
                {signup?.photo && (
                    <Avatar
                        src={signup.photo}
                        alt="报名人照片"
                        radius="md"
                        size={100}
                        className="tw-mx-auto tw-cursor-pointer"
                        onClick={() => previewFile(signup.photo)}
                    />
                )}
                <TextInput
                    label="称谓"
                    value={signup?.call || ""}
                    readOnly
                />
                <TextInput
                    label="姓名"
                    value={signup?.name || ""}
                    readOnly
                />
                <TextInput
                    label="性别"
                    value={signup?.gender || ""}
                    readOnly
                />
                <TextInput
                    label="职位"
                    value={signup?.job || ""}
                    readOnly
                />
                <TextInput
                    label="随同人数"
                    value={signup?.follow_number || ""}
                    readOnly
                />
                <TextInput
                    label="邀约人"
                    value={signup?.inviter || ""}
                    readOnly
                />
                <TextInput
                    label="机构全称"
                    value={signup?.org_fullname || ""}
                    readOnly
                />
                {renderMultiSelect("机构类型", signup?.org_type, orgTypeOptions)}
                <TextInput
                    label="联系方式"
                    value={signup?.phone || ""}
                    readOnly
                />
                <TextInput
                    label="紧急联系人"
                    value={signup?.crash_contact || ""}
                    readOnly
                />
                <TextInput
                    label="电子邮箱"
                    value={signup?.email || ""}
                    readOnly
                />
                {renderMultiSelect("餐饮禁忌", signup?.food_ban, foodBanOptions)}
                {renderMultiSelect("健康需求", signup?.health_need, healthNeedOptions)}
                {renderMultiSelect("交通安排", signup?.transport_need, transportNeedOptions)}
                {renderMultiSelect("住宿偏好", signup?.room_need, roomNeedOptions)}
                {renderMultiSelect("宗教/文化", signup?.culture, cultureOptions)}
                {renderMultiSelect("社交偏好", signup?.social_hobby, socialHobbyOptions)}
                <TextInput
                    label="随行人员-助理数量"
                    value={signup?.follow_help_number || ""}
                    readOnly
                />
                <Textarea
                    label="随行人员-助理名单"
                    value={signup?.follow_help_list || ""}
                    readOnly
                />
                <Textarea
                    label="商务对接需求"
                    value={signup?.business_need || ""}
                    readOnly
                />
                {renderMultiSelect("媒体互动", signup?.media, mediaOptions)}
                {renderMultiSelect("证件信息隐藏", signup?.id_hide, idHideOptions)}
                {renderMultiSelect("贵宾休息室使用权", signup?.vip_room, vipRoomOptions)}
                <Textarea
                    label="行程安排"
                    value={signup?.scheduleValue?.join("\n") || ""}
                    minRows={signup?.scheduleValue?.length || 1}
                    autosize
                    readOnly
                />
                <Textarea
                    label="其它"
                    value={signup?.others || ""}
                    readOnly
                />
                <Textarea
                    label="其他个性化服务需求"
                    value={signup?.social_hobby_value4 || ""}
                    readOnly
                />
            </Stack>
        </Modal>
    );
};

export default SignUpInfo;
