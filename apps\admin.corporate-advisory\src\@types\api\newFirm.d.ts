declare global {
    type TFirmSearchParams = {
        status?: string;
        keyword: string | null;
    } & TPageQueryParams;

    type TNewFirm = {
        id: number;
        profile_id: number; // 申请人user_id
        level: number; // 事务所等级：1省 2市 3区 4镇
        province: number; // 省
        city: number; // 市
        district: number; // 区
        town: number; // 镇
        address: string; // 详细地址
        total_area_space: string; // 场地总面积
        space_type: number; // 场地所属：0自由 1租赁
        space_property: string; // 场地产权或租赁合同，存放文件url
        completion_date: string; // 装修竣工日期 年月格式
        process: number; // 10第一步 20第二步 50已完成
        status: number; // 审核状态：10待审核 20审核通过 30审核不通过
        created_at?: string;
        updated_at?: string;
        space?: TFirmSpace[];
        processes?: TFirmProcesses[];
        profile?: {
            profileID?: number;
            profileName?: string;
        };
        profile_contracts?: {
            contract?: string;
        }[];
    };

    // 联号事务所场地
    type TFirmSpace = {
        id: number;
        affiliated_firm_id: number;
        space_name: string; // 场地名称
        space_photos: string[]; // 场地照片
        space_area: string; // 容纳面积
        space_capacity: number; // 容纳人数
        space_equipment_detail: string; // 设备明细
        created_at?: string;
        updated_at?: string;
    };

    // 联号事务所申请流程
    type TFirmProcesses = {
        id: number;
        affiliated_firm_id: number; // cna_affiliated_firm表的id
        profile_id: number; // 审核人user_id
        step: number; // 负责第几步的审核
        reason: string; // 审核意见
        process_status: number; // 审核状态 0待审核 10同意，20拒绝
        process_time?: string; // 审核时间
        created_at?: string;
        updated_at?: string;
    };

    // 联号事务所申请流程详情
    type TFirmProcess = TFirmProcesses & {
        affiliated_firm: TNewFirm;
        affiliated_firm_spaces: TFirmSpace[];
    };
}

export {};
