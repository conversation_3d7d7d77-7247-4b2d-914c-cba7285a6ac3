import { useState, useCallback, useRef } from "react";
import { Stack, Text, Badge } from "@mantine/core";
import { ColumnDef } from "@tanstack/react-table";
import { PageHeader, DataTable, DataTableRef } from "@code.8cent/react/components";
import api from "@/apis";
import dayjs from "dayjs";

// 财务类型映射
const FINANCE_TYPE_MAP = {
    1: { label: "业务收入", color: "green" },
    2: { label: "业务支出", color: "red" },
    3: { label: "豁免", color: "gray" },
    4: { label: "津贴收入", color: "blue" },
    5: { label: "津贴支出", color: "orange" },
};

// 支付方式映射
const PAY_TYPE_MAP = {
    1: { label: "微信", color: "green" },
    2: { label: "支付宝", color: "blue" },
    3: { label: "网银", color: "violet" },
    4: { label: "豁免", color: "gray" },
};

const AdminCompanyReceivedDetailPage = () => {
    const tableRef = useRef<DataTableRef>(null);

    const [tableData, setTableData] = useState<TCompanyReceivedDetail[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    // 表格列配置
    const columns: ColumnDef<TCompanyReceivedDetail>[] = [
        {
            accessorKey: "paymentNumber",
            header: "支付单号",
            enableSorting: false,
        },
        {
            accessorKey: "project_name",
            header: "业务类型",
            enableSorting: false,
        },
        {
            accessorKey: "type",
            header: "财务类型",
            enableSorting: false,
            cell: ({ row }) => {
                const type = row.getValue("type") as number;
                const typeInfo = FINANCE_TYPE_MAP[type as keyof typeof FINANCE_TYPE_MAP];
                return typeInfo ? (
                    <Badge
                        color={typeInfo.color}
                        variant="light"
                    >
                        {typeInfo.label}
                    </Badge>
                ) : (
                    type
                );
            },
        },
        {
            accessorKey: "pay_type",
            header: "支付方式",
            enableSorting: false,
            cell: ({ row }) => {
                const payType = row.getValue("pay_type") as number;
                const payTypeInfo = PAY_TYPE_MAP[payType as keyof typeof PAY_TYPE_MAP];
                return payTypeInfo ? (
                    <Badge
                        color={payTypeInfo.color}
                        variant="light"
                    >
                        {payTypeInfo.label}
                    </Badge>
                ) : (
                    payType
                );
            },
        },
        {
            accessorKey: "fee",
            header: "应收金额",
            enableSorting: false,
        },
        {
            accessorKey: "amount",
            header: "已收金额",
            enableSorting: false,
            cell: ({ row }) => {
                const amount = row.getValue("amount") as string;
                const type = row.getValue("type") as number;
                return (
                    <Text
                        fw={500}
                        c={type === 1 || type === 4 ? "green" : "red"}
                    >
                        {type === 1 || type === 4 ? "+" : "-"}¥{amount}
                    </Text>
                );
            },
        },
        {
            accessorKey: "amount",
            header: "实收金额",
            enableSorting: false,
        },
        {
            accessorKey: "detail_name",
            header: "项目名称",
            enableSorting: false,
        },
        {
            accessorKey: "main",
            header: "公司主体",
            enableSorting: false,
        },
        {
            accessorKey: "commission",
            header: "手续费",
            enableSorting: false,
        },
        {
            accessorKey: "company_info.name",
            header: "公司名称",
            enableSorting: false,
        },
        {
            accessorKey: "company_info.credit_code",
            header: "统一社会信用代码",
            enableSorting: false,
        },
        {
            accessorKey: "company_info.contact_name",
            header: "联系人",
            enableSorting: false,
        },
        {
            accessorKey: "company_info.contact_position",
            header: "联系人职位",
            enableSorting: false,
        },
        {
            accessorKey: "created_at",
            header: "创建时间",
            enableSorting: false,
            cell: ({ row }) => {
                const date = row.getValue("created_at") as string;
                return new Date(date).toLocaleString("zh-CN");
            },
        },
    ];

    // 全局搜索字段配置
    const globalFilterFields = [
        {
            field: "type",
            label: "财务类型",
            type: "select" as const,
            options: [
                { label: "业务收入", value: "1" },
                { label: "业务支出", value: "2" },
                { label: "豁免", value: "3" },
            ],
        },
        {
            field: "profile_name",
            label: "合伙人姓名",
            type: "text" as const,
        },
        {
            field: "date_range",
            label: "时间范围",
            type: "dateRange" as const,
        },
        {
            field: "profile_code",
            label: "合伙人编码",
            type: "text" as const,
        },
    ];

    // 获取表格数据
    const fetchTableData = useCallback(
        async (params: {
            page: number;
            pageSize: number;
            sorting: any[];
            columnFilters: any[];
            globalFilters: Record<string, any>;
        }) => {
            setLoading(true);
            try {
                // 处理日期范围
                let start_time, end_time;
                if (
                    params.globalFilters.date_range &&
                    Array.isArray(params.globalFilters.date_range)
                ) {
                    start_time =
                        params.globalFilters.date_range[0] &&
                        dayjs(params.globalFilters.date_range[0]).format("YYYY-MM-DD 00:00:00");
                    end_time =
                        params.globalFilters.date_range[1] &&
                        dayjs(params.globalFilters.date_range[1]).format("YYYY-MM-DD 23:59:59");
                }

                const apiParams: TCompanyReceivedDetailSearchParams = {
                    page: params.page,
                    page_size: params.pageSize,
                    type: params.globalFilters.type
                        ? (Number(params.globalFilters.type) as 1 | 2 | 3)
                        : undefined,
                    profile_name: params.globalFilters.profile_name || undefined,
                    profile_code: params.globalFilters.profile_code || undefined,
                    start_time: start_time || undefined,
                    end_time: end_time || undefined,
                };

                const response = await api.finance.companyReceivedDetail(apiParams);

                if (response) {
                    setTableData(response.data);
                    setTotalCount(response.total);
                } else {
                    setTableData([]);
                    setTotalCount(0);
                }
            } catch (error) {
                console.error("获取表格数据失败:", error);
                setTableData([]);
                setTotalCount(0);
            } finally {
                setLoading(false);
            }
        },
        []
    );

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="企业应收明细"
                desc="查询和管理企业应收明细"
            />

            <DataTable
                ref={tableRef}
                columns={columns}
                data={tableData}
                totalCount={totalCount}
                loading={loading}
                onFetch={fetchTableData}
                globalFilterFields={globalFilterFields}
                serverSideSort={true}
                getRowId={(row) => row.id}
            />
        </Stack>
    );
};

export default AdminCompanyReceivedDetailPage;
