import { notifications } from "@mantine/notifications";
import React from "react";
import AlertErrorIcon from "./icons/icon-alert-error.svg?react";
import AlertSuccessIcon from "./icons/icon-alert-success.svg?react";
import AlertWarningIcon from "./icons/icon-alert-warning.svg?react";

export default {
    error: (title: string = "Error", message?: string) =>
        notifications.show({
            title: title,
            message: typeof message === "string" ? message : null,
            color: "red.1",
            position: "top-right",
            autoClose: 5000,
            icon: React.createElement(AlertErrorIcon),
            withBorder: true,
            classNames: {
                // root: "tw-bg-[--notification-color]",
                title: "tw-text-gray-900",
                description: "tw-text-gray-800",
                closeButton: "hover:!tw-bg-black/5",
            },
        }),

    success: (title: string = "Error", message?: string) =>
        notifications.show({
            title: title,
            message: typeof message === "string" ? message : null,
            color: "green.1",
            position: "top-right",
            autoClose: 5000,
            icon: React.createElement(AlertSuccessIcon),
            withBorder: true,
            classNames: {
                // root: "tw-bg-[--notification-color]",
                title: "tw-text-gray-900",
                description: "tw-text-gray-800",
                closeButton: "hover:!tw-bg-black/5",
            },
        }),
};
