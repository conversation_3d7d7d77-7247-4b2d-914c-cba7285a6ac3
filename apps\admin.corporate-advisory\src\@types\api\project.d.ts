declare global {
    type ProjectItem = Omit<ProjectSelectItem, "projectCategoriesID"> & {
        projectItem: CompanyItem[];
    };

    type CompanyItem = {
        companyID: number;
        companyName: string;
        companyState:
            | "project.edit.complete"
            | "project.edit.submit_form"
            | "project.edit.submit_pay"
            | "project.edit.account_setting"
            | "project.edit.prepare_report";
        projectMore: string; 
    };

    type CompanyApplictaionInfo = {
        companyID: number;
        companyName: string;
        companyState: number;
        companyProgressState: number;
        companyReason: string;
        projectCategoriesName: string;
        progressTxt: string;
    };

    type ProjectServiceItem = {
        serviceID: number;
        serviceTitle: string;
        serviceCurrency: string;
        servicePrice: string;
        serviceTip: string;
    };

    type ProjectCompanyCreateParams = {
        projectID: string;
        companyName: string;
        companyRegisterCode: string;
        companyCategoriesID: string;
        countryID: string;
        email: string;
        mobilePrefixID: string;
        phone: string;
    };

    type PreliminarySubmissionData = {
        companyServices: string[];
        reason: string;
    };

    type PaymentSubmissionData = {
        bankID: number;
        paymentCategoryID: number;
        paymentCode: string;
        totalPrice: string;
        currency: string;
    };
}

export {};
