import {
    Modal,
    Stack,
    TextInput,
    Select,
    Button,
    Group,
    Paper,
    Text,
    Checkbox,
    ActionIcon,
    NumberInput,
} from "@mantine/core";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useEffect } from "react";
import useModalStore from "@/store/modal";
import examQuestion from "@/apis/station/examQuestion";
import noty from "@code.8cent/react/noty";
import { Plus, Trash } from "@phosphor-icons/react";
import { useShallow } from "zustand/react/shallow";

interface FormProps {
    refreshTable: () => void;
}

const optionSchema = z.object({
    option_name: z.string().min(1, "请输入选项内容"),
    is_answer: z.number(),
});

const formSchema = z.object({
    exam_id: z.number().min(1, "请选择所属考试"),
    type: z.number().min(1, "请选择问题类型"),
    question: z.string().min(1, "请输入问题内容"),
    option: z.array(optionSchema).min(1, "请添加选项"),
    score: z.number().optional(), // 分数非必填
}) as z.ZodType<TExamQuestionStoreOrUpdateParams>;

type FormValues = TExamQuestionStoreOrUpdateParams;

const Form = ({ refreshTable }: FormProps) => {
    const { show, close } = useModalStore(
        useShallow((state) => ({
            show: state.examQuestionForm,
            close: state.close,
        }))
    );

    const modalParams = useModalStore((state) => state.modalParams.examQuestionForm);
    const data = modalParams as TQuestion | undefined;

    const {
        register,
        handleSubmit,
        setValue,
        reset,
        control,
        watch,
        formState: { errors },
    } = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            type: 0,
            question: "",
            score: 0,
            option: [],
        },
    });

    const { fields, append, remove } = useFieldArray({
        control,
        name: "option",
    });

    const type = watch("type");

    useEffect(() => {
        if (data) {
            setValue("exam_id", data.exam_id);
            setValue("type", data.type);
            setValue("question", data.question);
            setValue("score", data.score);
            setValue("option", data.option);
        } else {
            reset();
        }
    }, [data, setValue, reset]);

    const onSubmit = async (values: FormValues) => {
        try {
            let success;
            if (data) {
                success = await examQuestion.update(data.id, values);
            } else {
                success = await examQuestion.store(values);
            }

            if (success) {
                noty.success(data ? "更新成功" : "创建成功");
                close("examQuestionForm");
                refreshTable();
            }
        } catch (error) {
            console.error("保存失败", error);
            noty.error("保存失败");
        }
    };

    const handleAddOption = () => {
        append({
            option_name: "",
            is_answer: 0,
        });
    };

    const handleOptionAnswerChange = (index: number, checked: boolean) => {
        const options = watch("option");
        const newOptions = options.map((option, i) => ({
            ...option,
            is_answer: i === index ? (checked ? 1 : 0) : type === 0 ? 0 : option.is_answer,
        }));
        setValue("option", newOptions);
    };

    return (
        <Modal
            opened={show}
            onClose={() => close("examQuestionForm")}
            title={data ? "编辑问题" : "新增问题"}
            size="lg"
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack>
                    <Select
                        label="所属考试"
                        data={[{ value: "1", label: "管理合伙人晋升" }]}
                        defaultValue={String(data?.exam_id)}
                        onChange={(value) => setValue("exam_id", Number(value))}
                        error={errors.exam_id?.message}
                    />
                    <Select
                        label="问题类型"
                        data={[
                            { value: "0", label: "单选题" },
                            { value: "1", label: "多选题" },
                            { value: "2", label: "文本填写" },
                            { value: "3", label: "判断题" },
                        ]}
                        defaultValue={String(data?.type)}
                        onChange={(value) => setValue("type", Number(value))}
                        error={errors.type?.message}
                    />

                    <TextInput
                        label="问题内容"
                        placeholder="请输入问题内容"
                        {...register("question")}
                        error={errors.question?.message}
                    />

                    <NumberInput
                        label="分数"
                        placeholder="请输入问题答对的分数"
                        value={watch("score")}
                        onChange={(value) => setValue("score", value === "" ? 0 : Number(value))}
                        error={errors.score?.message}
                    />

                    <Stack gap="xs">
                        <Group
                            justify="space-between"
                            align="center"
                        >
                            <Stack gap={0}>
                                <Text fw={500}>选项列表</Text>
                                <Text size="xs" c="dimmed">
                                    * 判断题请添加两个选项，选项文案必须为“正确”和“错误”
                                </Text>
                            </Stack>
                            <Button
                                variant="outline"
                                size="xs"
                                leftSection={<Plus size={14} />}
                                onClick={handleAddOption}
                            >
                                添加选项
                            </Button>
                        </Group>

                        {fields.map((field, index) => (
                            <Paper
                                key={field.id}
                                p="xs"
                                withBorder
                            >
                                <Group align="flex-start">
                                    <Checkbox
                                        checked={watch(`option.${index}.is_answer`) === 1}
                                        onChange={(event) =>
                                            handleOptionAnswerChange(
                                                index,
                                                event.currentTarget.checked
                                            )
                                        }
                                        label="正确答案"
                                    />
                                    <TextInput
                                        placeholder="请输入选项内容"
                                        style={{ flex: 1 }}
                                        {...register(`option.${index}.option_name`)}
                                        error={errors.option?.[index]?.option_name?.message}
                                    />
                                    <ActionIcon
                                        variant="subtle"
                                        color="red"
                                        onClick={() => remove(index)}
                                    >
                                        <Trash size={16} />
                                    </ActionIcon>
                                </Group>
                            </Paper>
                        ))}

                        {errors.option && (
                            <Text
                                size="sm"
                                c="red"
                            >
                                {errors.option.message}
                            </Text>
                        )}
                    </Stack>

                    <Group
                        justify="flex-end"
                        mt="md"
                    >
                        <Button
                            variant="default"
                            onClick={() => close("examQuestionForm")}
                        >
                            取消
                        </Button>
                        <Button type="submit">确定</Button>
                    </Group>
                </Stack>
            </form>
        </Modal>
    );
};

export default Form;
