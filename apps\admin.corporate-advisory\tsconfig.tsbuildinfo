{"root": ["../../packages/shared-types/global.d.ts", "../../packages/shared-types/api.d.ts", "./src/main.tsx", "./src/vite-env.d.ts", "./src/@types/global.d.ts", "./src/@types/api/bill.d.ts", "./src/@types/api/document.d.ts", "./src/@types/api/gernal.d.ts", "./src/@types/api/index.d.ts", "./src/@types/api/notification.d.ts", "./src/@types/api/project.d.ts", "./src/@types/api/register.d.ts", "./src/@types/api/user.d.ts", "./src/apis/config.ts", "./src/apis/email.ts", "./src/apis/gernal.ts", "./src/apis/index.ts", "./src/apis/profile.ts", "./src/apis/project.ts", "./src/apis/register.ts", "./src/apis/user.ts", "./src/apis/wechat.ts", "./src/components/company/application/accountsetting.tsx", "./src/components/company/application/applicationcomplete.tsx", "./src/components/company/application/applicationstepper.tsx", "./src/components/company/application/documentsubmission.tsx", "./src/components/company/application/paymentsubmission.tsx", "./src/components/company/application/preliminarysubmission.tsx", "./src/components/layouts/accountlayout.tsx", "./src/components/layouts/dashboardlayout.tsx", "./src/components/layouts/rootlayout.tsx", "./src/components/modals/alertmodal.tsx", "./src/components/modals/confirmmodal.tsx", "./src/components/modals/uploadmodal.tsx", "./src/components/modals/calendar/calendareventcreationmodal.tsx", "./src/components/modals/profile/passwordresetmodal.tsx", "./src/components/modals/profile/profilevalidatemodal.tsx", "./src/components/modals/project/companyapplicationmodal.tsx", "./src/components/modals/project/companydetailmodal.tsx", "./src/components/modals/project/createcompanymodal.tsx", "./src/components/modals/register/infovalidatemodal.tsx", "./src/components/modals/setting/settingactivitymodal.tsx", "./src/components/modals/setting/settingdefaultmodal.tsx", "./src/components/modals/setting/settingloginmodal.tsx", "./src/components/modals/setting/settingnotificationmodal.tsx", "./src/components/modals/setting/settingregionmodal.tsx", "./src/components/modals/setting/settingsupportmodal.tsx", "./src/components/password/passwordcheckerinput.tsx", "./src/components/password/passwordmatcherinput.tsx", "./src/components/profile/addressinput.tsx", "./src/components/profile/profileavatar.tsx", "./src/components/profile/profileuserdata.tsx", "./src/components/register/emailphoneform.tsx", "./src/components/register/informationform.tsx", "./src/components/register/pay.tsx", "./src/components/register/payresult.tsx", "./src/components/wizard/completesetup.tsx", "./src/components/wizard/languagesetup.tsx", "./src/components/wizard/securitysetup.tsx", "./src/components/wizard/usersetup.tsx", "./src/contexts/project.ts", "./src/hooks/project/usecompanyapplicationformenabled.ts", "./src/pages/account/register.tsx", "./src/pages/member/benefit.tsx", "./src/pages/member/billings.tsx", "./src/pages/member/calendar.tsx", "./src/pages/member/community.tsx", "./src/pages/member/document.tsx", "./src/pages/member/notification.tsx", "./src/pages/member/profile.tsx", "./src/pages/member/project.tsx", "./src/pages/member/setting.tsx", "./src/pages/wizard/index.tsx", "./src/router/index.tsx", "./src/store/modal.ts", "./src/store/profile.ts", "./src/store/register.ts", "./src/utils/cnarequest.ts", "./src/utils/eventbus.tsx", "./src/utils/sleepms.ts"], "version": "5.6.3"}