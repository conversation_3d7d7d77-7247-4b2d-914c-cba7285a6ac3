import api from "@/apis";
import { Mo<PERSON>, Stack, TextInput, Select, Button, Flex, Input } from "@mantine/core";
import { useState, useEffect } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import noty from "@code.8cent/react/noty";
import useModalStore from "@/store/modal";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import ModalFooter from "@/components/common/ModalFooter";
import { Check, X } from "@phosphor-icons/react";
import { useMemoizedFn, useRequest } from "ahooks";
import { useShallow } from "zustand/react/shallow";
import { DataTable } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";

const accountSchema = z.object({
    industry_name: z.string().min(1, "请输入行业类型名称"),
    market_code: z.string().min(1, "请输入市场标识符"),
    id: z.string().optional(),
});


type AccountForm = z.infer<typeof accountSchema>;



const FieldTypeTeamListModal = ({

    onUpdateSuccess = () => { },
}: {

    onUpdateSuccess?: () => void;
}) => {
    const [loading, setLoading] = useState(false);
    const openConfirm = useModalStore.use.openConfirm();
    const [opened, { open, close: addTeamClose }] = useDisclosure(false);
    const [number, setNumber] = useState('');
    const columnHelper = createColumnHelper<TFieldItemListResponse & { department_name?: string }>();

    const { isVisible, close } = useModalStore(
        useShallow((state) => ({
            isVisible: state.fieldTypeTeamListModal,
            close: state.close,
        }))
    );

    useEffect(() => {
        if (isVisible) {
            getList()
        }
    }, [isVisible])

    const modalParams = useModalStore((state) => state.modalParams.fieldTypeTeamListModal);

    //获取队伍列表
    const { run: getList, data: teamList = [] } = useRequest(async () => {
        console.log('modalParams:', modalParams)
        const res = await api.fieldType.teamList(modalParams?.detail?.id);
        console.log('res:', res)
        if (res) {
            //如果需要接口某些数据要在此处添加
            let data = []
            res.branch_list.map(x => {
                data.push({
                    ...x,
                    teamName: `${res.name}-${res.market_code}-${x.number}组`,
                    profileContact: x.user_info?.profileContact,
                    profileEmail: x.user_info?.profileEmail,
                    profileID: x.user_info?.profileID,
                    profileName: x.user_info?.profileName,
                    link: `CNACN00000001-${res.market_code}-${x.number}`
                })


            })
            console.log('data:', data)
            return data;
        } else {
            return []
        }

    }, { manual: true })

    //添加队伍
    const { run: handleAddTeam, loading: addTeamLoading } = useRequest(async () => {
        console.log('modalParams:', modalParams)
        let submitData = {
            industry_name: modalParams?.detail?.name,
            market_code: modalParams?.detail?.market_code,
            number,
        }
        const res = await api.fieldType.addTeam(submitData);
        console.log('res:', res)
        if (res) {
            notifications.show({
                title: '添加成功',
                message: '添加队伍成功'
            })
            getList()
            addTeamClose()
        }
    }, { manual: true })


    const copyLink = (link: string) => {
        navigator.clipboard.writeText(
            `请进入我的专属邀请链接：https://ai-desk.corporate-advisory.cn/team?refer=${link}，完成合伙人注册流程。
注册须知：
1、注册时请输入邀请码：${link}
2、请提前准备好有效身份证、中国工商银行储蓄卡。
3、支付时请添加备注“注册人姓名”。`
        );
        noty.success("合伙人邀请链接已复制到剪贴板");
    }

    const tableColumns = [
        columnHelper.accessor("teamName", {
            header: "委员会分组",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileName", {
            header: "创会主席",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("link", {
            header: "分享链接",
            enableSorting: false,
            cell: (info) => {
                return <Button onClick={() => copyLink(info.getValue())}>
                    点击分享
                </Button>

            }
        }),
        // columnHelper.accessor("profileContact", {
        //     header: "联系电话",
        //     enableSorting: false,
        //     cell: (info) => info.getValue(),
        // }),
        // columnHelper.accessor("profileEmail", {
        //     header: "邮箱",
        //     enableSorting: false,
        //     cell: (info) => info.getValue(),
        // }),


        // columnHelper.display({
        //     id: "actions",
        //     header: "操作",
        //     cell: (info) => <TableRowDropActionMenu items={rowActions(info.row.original)} />,
        // }),
    ];

    const rowActions = (row) => [
        {
            key: "info",
            label: "复制连接",
            onClick: () => { },
        },

    ];

    const closeModal = useMemoizedFn(() => {
        close("fieldTypeTeamListModal");
    });


    const addTeamModal = <Modal opened={opened} onClose={addTeamClose} title='新增队伍'>
        <div className="tw-gap-2 tw-flex tw-flex-col">

            <Input placeholder="请填写队伍号（只可填入大写字母与数字）" value={number} onChange={(e) => setNumber(e.target.value)} />
            <Button fullWidth onClick={() => handleAddTeam()} >
                添加队伍
            </Button>
        </div>
    </Modal>

    return (
        <Modal
            opened={isVisible}
            onClose={closeModal}
            title='团队信息'
            size="100%"
        >
            <Flex justify='flex-end' className="tw-mb-2">
                <Button onClick={open}>新建分组</Button>
            </Flex>

            {isVisible && <DataTable
                data={teamList}
                columns={tableColumns as any}
                loading={addTeamLoading}
                onFetch={getList as any}
                totalCount={teamList.length}
            />}

            {addTeamModal}
        </Modal>
    );
};

export default FieldTypeTeamListModal;
