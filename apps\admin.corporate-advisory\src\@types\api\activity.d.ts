declare global {
    type TActivitySearchParams = {
        keyword?: string | null;
    } & TPageQueryParams;

    type TActivitySignupSearchParams = {
        id?: number | string;
        keyword?: string | null;
    } & TPageQueryParams;

    type TActivity = {
        id: number;
        active_name: string;
        active_time: string;
        active_place: string;
        active_url?: string;
        created_at: string | null;
        updated_at: string | null;
    };

    type TActivitySignup = {
        call?: string; // 称谓
        name?: string; // 全名(中英文)
        gender?: number; // 性别
        job?: string; // 职位
        follow_number?: string; // 随同人数
        inviter?: string; // 邀约人
        org_fullname?: string; // 机构全称
        org_type?: string; // 机构类型
        phone?: string; // 联系方式
        crash_contact?: string; // 紧急联系人
        email?: string; // 电子邮箱
        photo?: string; // 照片
        food_ban?: string; // 餐饮禁忌
        health_need?: string; // 健康需求
        transport_need?: string; // 交通安排
        room_need?: string; // 住宿偏好
        culture?: string; // 宗教/文化
        social_hobby?: string; // 社交偏好
        others?: string; // 其它
        follow_help_number?: string; // 随行人员-助理
        follow_help_list?: string; // 随行人员-助理-名单
        follow_translate_number?: string; // 随行人员-翻译
        follow_translate_list?: string; // 随行人员-翻译-名单
        follow_mate_number?: string; // 随行人员-配偶
        follow_mate_list?: string; // 随行人员-配偶-名单
        business_need?: string; // 商务对接需求
        media?: string; // 媒体互动
        id_hide?: string; // 证件信息隐藏
        vip_room?: string; // 贵宾休息室使用权
        scheduleValue?: string[]; // 行程安排
        social_hobby_value4?: string; // 其他个性化服务需求
    };

    type TActivitiesResponse = {
        items: TActivity[];
        paginate: BasePaginateResponse;
    };

    type TActivitySignupsResponse = {
        items: TActivitySignup[];
        paginate: BasePaginateResponse;
    };
}

export {};
