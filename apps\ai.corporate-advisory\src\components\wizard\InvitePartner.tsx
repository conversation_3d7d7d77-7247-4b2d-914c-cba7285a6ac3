import { useEventBus } from "@/utils/eventBus";
import { CnaButton } from "@code.8cent/react/components";
import useWizardStore from "@code.8cent/store/wizard";
import { cnaRequest } from "@code.8cent/utils";
import { ActionIcon, Box, Group, Stack, Text, Textarea, TextInput, Title } from "@mantine/core";
import { Copy } from "@phosphor-icons/react";
import { useMount, useRequest, useUnmount } from "ahooks";

const InvitePartner = () => {
    const bus = useEventBus();

    const { state, setState } = useWizardStore();

    const { data: inviteToken = null } = useRequest(async () => {
        const { result, error } = await cnaRequest<{ token: string }>(
            "/api/v1/team/inviteToken",
            "POST"
        );

        if (!error) {
            return result.data.token;
        } else {
            return null;
        }
    });

    useMount(() => {
        bus.on("wizard.submit.click", () => {
            setState(state + 1);
        });
    });

    useUnmount(() => {
        bus.off("wizard.submit.click");
    });

    return (
        <Stack className="tw-text-center">
            <Title order={3}>邀请合伙人加入</Title>
            <Text size="sm">以下是你的邀请链接，复制并分享给你朋友吧: </Text>
            <Textarea
                autosize
                readOnly
                value={`${location.origin}/team?refer=${inviteToken}`}
                placeholder="邀请链接"
                className="tw-mt-4"
            />

            <Group justify="center">
                <CnaButton
                    onClick={() => {
                        navigator.clipboard?.writeText?.(
                            `你被邀请成为加盟合伙人，邀请链接: \n${location.origin}/team?refer=${inviteToken}`
                        );
                    }}
                >
                    点击复制链接
                </CnaButton>
            </Group>
        </Stack>
    );
};

export default InvitePartner;
