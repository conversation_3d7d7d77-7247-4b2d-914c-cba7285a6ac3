import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const finance = {
    // 合伙人结算应付明细
    associatePaidDetail: async (params: TAssociatePaidDetailSearchParams) => {
        const { error, result } = await cnaRequest<TAssociatePaidDetailResponse>(
            "/api/v1/admin/payment/out",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 合伙人月收益分成结算应付汇总
    associatePaidTotal: async (params: TAssociatePaidTotalSearchParams) => {
        const { error, result } = await cnaRequest<TAssociatePaidTotalResponse>(
            "/api/v1/admin/payment/totalOut",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 合伙人加盟费应收明细
    associateReceivedDetail: async (params: TAssociateReceivedDetailSearchParams) => {
        const { error, result } = await cnaRequest<TAssociateReceivedDetailResponse>(
            `/api/v1/admin/payment/joinDetail`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 合伙人加盟费应收汇总
    associateReceivedTotal: async (params: TAssociateReceivedTotalSearchParams) => {
        const { error, result } = await cnaRequest<TAssociateReceivedTotalResponse>(
            `/api/v1/admin/payment/joinTotal`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    // 上传发票
    uploadInvoice: async (params: TUploadInvoiceParams) => {
        const formData = new FormData();
        formData.append("id", params.id.toString());
        formData.append("file", params.file);

        const { error } = await cnaRequest(
            "/api/v1/admin/payment/upload/joinInvoice",
            "POST",
            formData
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    // 企业应收明细表
    companyReceivedDetail: async (params: TCompanyReceivedDetailSearchParams) => {
        const { error, result } = await cnaRequest<TCompanyReceivedDetailResponse>(
            `/api/v1/admin/payment/companyIn`,
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
};

export default finance;
