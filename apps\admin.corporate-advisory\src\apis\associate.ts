import noty from "@code.8cent/react/noty";
import cnaRequest from "@code.8cent/utils/cnaRequest";

const associate = {
    list: async (params: TAssociateSearchParams) => {
        const { error, result } = await cnaRequest<AssociatesResponse>(
            "/api/v1/admin/partner/list",
            "GET",
            params
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    review: async (params: TReviewAssociateParams) => {
        const { error, result } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/partner/check",
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    updateSetting: async (params: TUpdateSettingParams, id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/partner/setting/${id}`,
            "POST",
            params
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    setTeamLeader: async (id: number) => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/partner/setTeamTop`,
            "POST",
            {
                id,
            }
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
    options: async () => {
        const { error, result } = await cnaRequest<BaseApiResponse>(
            "/api/v1/admin/partner/options",
            "GET"
        );

        if (!error) {
            return result;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    info: async (id: number) => {
        const { error, result } = await cnaRequest<UserProfileResponse>(
            `/api/v1/admin/partner/info/${id}`,
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            noty.error(error.message);
            return null;
        }
    },
    markReviewed: async (id: number, type: "info_status" | "register_payment_status") => {
        const { error } = await cnaRequest<BaseApiResponse>(
            `/api/v1/admin/partner/mark-reviewed`,
            "POST",
            {
                id,
                type,
            }
        );

        if (!error) {
            return true;
        } else {
            noty.error(error.message);
            return false;
        }
    },
};

export default associate;
