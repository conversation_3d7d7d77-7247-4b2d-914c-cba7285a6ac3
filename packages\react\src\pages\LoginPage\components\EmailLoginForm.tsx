import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import { cnaRequest } from "@code.8cent/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { TextInput, PasswordInput } from "@mantine/core";
import { ArrowClockwise } from "@phosphor-icons/react";
import { useTitle, useRequest } from "ahooks";
import { SHA256 } from "crypto-js";
import { useForm, Controller } from "react-hook-form";
import { useNavigate, Link } from "react-router-dom";
import { CnaButton } from "../../../components";
import noty from "../../../library/noty";
import { z } from "zod";

type EmailLoginFormInput = {
    email: string;
    captcha: string;
    password: string;
};

const emailLoginSchema = z.object({
    email: z.string().email("form.email.incorrect"),
    captcha: z.string().min(1),
    password: z.string().min(1),
});

const initialEmailLoginFormValues = {
    email: "",
    captcha: "",
    password: "",
};

const EmailLoginForm = () => {
    const navigate = useNavigate();

    const { lang } = useSettingStore();

    const { setState: setWizardState, setRegisterSetting } = useWizardStore();

    const {
        register,
        getValues,
        handleSubmit,
        formState: { errors },
        setError,
        reset,
        control,
        clearErrors,
        setValue,
    } = useForm<EmailLoginFormInput>({
        defaultValues: initialEmailLoginFormValues,
        resolver: zodResolver(emailLoginSchema),
    });

    const {
        run: getCaptchaCode,
        data: captchaRes,
        loading: gettingCode,
    } = useRequest(async () => {
        let { result, error } = await cnaRequest<{ key: string; img: string }>(
            "/api/v1/captcha",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return null;
        }
    });

    const { run: login, loading: loggingIn } = useRequest(
        async (data) => {
            const { result, error } = await cnaRequest<{
                token: string;
                profile_status: number;
                register_setting: string[];
            }>("/api/v1/login", "POST", {
                email: data.email,
                password: SHA256(data.password).toString(),
                captcha_code: data.captcha,
                captcha_key: captchaRes?.key,
            });

            if (error) {
                getCaptchaCode();
                noty.error(t("login.fail", lang), error.message);
            }

            if (result) {
                let { data } = result;

                await window.localForage.setItem("cna-token", data.token);

                if (data.register_setting?.length > 0) {
                    setRegisterSetting(data.register_setting);
                    setWizardState(0);
                    navigate("/account/wizard", { replace: true });
                } else {
                    navigate("/member/profile", { replace: true });
                }
            }
        },
        {
            manual: true,
        }
    );

    return (
        <form onSubmit={handleSubmit(login)}>
            <Controller
                name="email"
                control={control}
                render={({ field }) => (
                    <TextInput
                        autoComplete="username"
                        label={t("login.label.email", lang)}
                        placeholder={t("login.placeholder.email", lang)}
                        className="tw-mb-4"
                        withAsterisk
                        {...field}
                        error={errors.email && t(errors.email.message, lang)}
                    />
                )}
            />

            <Controller
                name="password"
                control={control}
                render={({ field }) => (
                    <PasswordInput
                        autoComplete="current-password"
                        label={t("login.label.password", lang)}
                        placeholder={t("login.placeholder.password", lang)}
                        className="tw-mb-4"
                        withAsterisk
                        {...field}
                        error={errors.password ? true : false}
                    />
                )}
            />

            <Controller
                name="captcha"
                control={control}
                render={({ field, fieldState }) => (
                    <TextInput
                        classNames={{
                            input: "tw-pl-[120px]",
                        }}
                        label={t("login.label.captcha", lang)}
                        placeholder={t("login.placeholder.captcha", lang)}
                        className="tw-mb-5"
                        leftSectionWidth={110}
                        leftSectionProps={{
                            className: "tw-justify-start",
                        }}
                        leftSection={
                            captchaRes && (
                                <img
                                    src={captchaRes.img}
                                    className="tw-block"
                                    alt="reCaptcha"
                                />
                            )
                        }
                        rightSection={
                            <CnaButton
                                variant="transparent"
                                color={"dark.3"}
                                onClick={getCaptchaCode}
                                className="tw-font-normal tw-px-2"
                                disabled={gettingCode}
                            >
                                <ArrowClockwise
                                    className={gettingCode ? "tw-animate-spin" : ""}
                                    size={24}
                                />
                            </CnaButton>
                        }
                        withAsterisk
                        {...field}
                        onChange={(e) => {
                            field.onChange(e.target.value.toUpperCase());
                        }}
                        error={fieldState.error ? true : false}
                    />
                )}
            />

            <div className="tw-flex tw-my-3 tw-space-x-3">
                <div className="tw-w-1/2">
                    <CnaButton
                        size="md"
                        fullWidth
                        variant="outline"
                        color="basic"
                        component={Link}
                        to="/account/register"
                    >
                        {t("form.sign.up", lang)}
                    </CnaButton>
                </div>
                <div className="tw-w-1/2">
                    <CnaButton
                        size="md"
                        fullWidth
                        variant="filled"
                        color="basic"
                        loading={loggingIn}
                        type="submit"
                    >
                        {t("login.btn.login", lang)}
                    </CnaButton>
                </div>
            </div>
        </form>
    );
};

export default EmailLoginForm;
