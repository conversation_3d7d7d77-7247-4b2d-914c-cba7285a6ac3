import api from "@/apis";
import { t } from "@code.8cent/i18n";
import useSettingStore from "@code.8cent/store/setting";
import useWizardStore from "@code.8cent/store/wizard";
import { useEventBus } from "@/utils/eventBus";
import { zodResolver } from "@hookform/resolvers/zod";
import { Stack, Center, Group, Text, Image, AspectRatio, Title, Grid } from "@mantine/core";
import { Dropzone, MIME_TYPES } from "@mantine/dropzone";
import {
    Check,
    X,
    FileImage,
    IdentificationCard,
    Cards,
    CheckCircle,
    XCircle,
} from "@phosphor-icons/react";
import { useMount, useRequest, useUnmount } from "ahooks";
import { filesize } from "filesize";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

type UserSetupFormInput = {
    frontFile: File | null;
    backFile: File | null;
};

const accept = [MIME_TYPES.jpeg, MIME_TYPES.png] as string[];
const acceptFiles = ['jpeg', 'png'] as string[];

const required = true;

const maxSize = 1024 * 1024 * 2;
const ProfileNRIC = () => {
    const bus = useEventBus();

    const lang = useSettingStore.use.lang();

    const { setState: setWizardState, state } = useWizardStore();

    const [frontRes, setFrontRes] = useState({
        success: false,
        message: "",
    });
    const [backRes, setBackRes] = useState({
        success: false,
        message: "",
    });

    const {
        handleSubmit,
        setValue,
        formState: { errors },
        getValues,
    } = useForm<UserSetupFormInput>({
        defaultValues: {
            frontFile: null,
            backFile: null,
        },
        resolver: zodResolver(
            z.object({
                frontFile: z
                    .custom<File>((v) => v instanceof File)
                    .refine((file) => file.size <= maxSize, {})
                    .refine((file) => accept.includes(file.type), {}),
                backFile: z
                    .custom<File>((v) => v instanceof File)
                    .refine((file) => file.size <= maxSize, {})
                    .refine((file) => accept.includes(file.type), {}),
            })
        ),
    });

    const { run: setupProfileNRIC, loading } = useRequest(
        async (data: UserSetupFormInput) => {
            if (frontRes.success && backRes.success) {
                setWizardState(state + 1);
            }
        },
        {
            manual: true,
        }
    );

    useEffect(() => {
        bus.emit("wizard.submitting", loading);
    }, [loading]);

    useMount(() => {
        bus.on("wizard.submit.click", handleSubmit(setupProfileNRIC));
    });

    useUnmount(() => {
        bus.emit("wizard.submitting", false);
        bus.off("wizard.submit.click");
    });

    const renderDropzone = (
        fileKey: "frontFile" | "backFile",
        res: { success: boolean; message: string },
        setRes: React.Dispatch<React.SetStateAction<{ success: boolean; message: string }>>
    ) => (
        <Stack>
            <Center>
                <Stack>
                    {getValues(fileKey) ? (
                        <div className="tw-relative tw-inline-block">
                            <Image
                                className="tw-w-1/2 tw-h-1/2 tw-border tw-mx-auto"
                                fit="contain"
                                src={`/images/idcard/${
                                    fileKey === "frontFile" ? "front" : "back"
                                }.jpg`}
                                alt={`${fileKey === "frontFile" ? "身份证正面" : "身份证反面"}`}
                            />
                            {/* 只在有响应结果时显示图标 */}
                            {res.message && (
                                <div className="tw-absolute tw-inset-0 tw-bg-black/20 tw-flex tw-items-center tw-justify-center">
                                    {res.success ? (
                                        <CheckCircle
                                            size={40}
                                            weight="fill"
                                            color="#12e23c"
                                        />
                                    ) : (
                                        <XCircle
                                            size={40}
                                            weight="fill"
                                            color="#ca2121"
                                        />
                                    )}
                                </div>
                            )}
                        </div>
                    ) : (
                        <Image
                            className="tw-w-1/2 tw-h-1/2 tw-border tw-mx-auto"
                            fit="contain"
                            src={`/images/idcard/${fileKey === "frontFile" ? "front" : "back"}.jpg`}
                            alt={`${fileKey === "frontFile" ? "身份证正面" : "身份证反面"}`}
                        />
                    )}
                    <Text className="tw-text-center tw-text-gray-500">
                        {fileKey === "frontFile" ? "身份证正面" : "身份证反面"}
                    </Text>
                </Stack>
            </Center>
            <Dropzone
                onDrop={async (_files) => {
                    setValue(fileKey, _files[0], { shouldValidate: true });
                    const res = await api.register.setProfileNRIC(
                        _files[0],
                        fileKey === "frontFile" ? "1" : "2"
                    );
                    setRes(res);
                }}
                multiple={false}
                accept={accept}
                className={`${errors[fileKey] && "!tw-border-[var(--mantine-color-red-6)]"}`}
            >
                <Center>
                    <Stack>
                        <FileImage
                            size={36}
                            className="tw-text-dimmed tw-mx-auto tw-mb-3"
                        />
                        <Text
                            size="sm"
                            c="dimmed"
                        >
                            {getValues(fileKey) ? getValues(fileKey).name : t("upload.files", lang)}
                        </Text>
                    </Stack>
                </Center>
            </Dropzone>
            <Stack gap={2}>
                <Group gap={4} wrap="nowrap">
                    {getValues(fileKey) instanceof File ? (
                        <Check
                            size={24}
                            className="tw-text-green-600"
                        />
                    ) : (
                        <X
                            size={24}
                            className="tw-text-red-600"
                        />
                    )}
                    <Text
                        size="sm"
                        c="dimmed"
                    >
                        {t("upload.files.requirement", lang)}
                    </Text>
                </Group>
                <Group gap={4} wrap="nowrap">
                    {getValues(fileKey) instanceof File && getValues(fileKey).size <= maxSize ? (
                        <Check
                            size={24}
                            className="tw-text-green-600"
                        />
                    ) : (
                        <X
                            size={24}
                            className="tw-text-red-600"
                        />
                    )}
                    <Text
                        size="sm"
                        c="dimmed"
                    >
                        {t("upload.files.exceed", lang)} {filesize(maxSize, { standard: "jedec" })}
                    </Text>
                </Group>
                <Group gap={4} wrap="nowrap">
                    {getValues(fileKey) instanceof File &&
                    accept.includes(getValues(fileKey).type) ? (
                        <Check
                            size={24}
                            className="tw-text-green-600"
                        />
                    ) : (
                        <X
                            size={24}
                            className="tw-text-red-600"
                        />
                    )}
                    <Text
                        size="sm"
                        c="dimmed"
                    >
                        {t("upload.files.format", lang)} {acceptFiles.join(",")}
                    </Text>
                </Group>
            </Stack>
            {res.message && !res.success && (
                <Text className="tw-text-red-600 tw-text-sm tw-mt-2">{res.message}</Text>
            )}
        </Stack>
    );

    return (
        <Stack>
            <Title
                order={5}
                fw="normal"
            >
                请上传您的身份证照片
            </Title>
            <Grid>
                <Grid.Col span={{ base: 12, md: 6 }}>
                    {renderDropzone("frontFile", frontRes, setFrontRes)}
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 6 }}>
                    {renderDropzone("backFile", backRes, setBackRes)}
                </Grid.Col>
            </Grid>
        </Stack>
    );
};

export default ProfileNRIC;
