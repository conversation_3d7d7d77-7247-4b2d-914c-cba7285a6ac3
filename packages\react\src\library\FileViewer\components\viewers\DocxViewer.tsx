import { Box, ScrollArea } from "@mantine/core";
import { renderAsync } from "docx-preview";
import { useEffect, useRef } from "react";

const DocxViewer = ({ file }: { file: File }) => {
    const docxPreviewContainer = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (file instanceof File === true && docxPreviewContainer.current) {
            renderAsync(file, docxPreviewContainer.current, null, {
                ignoreFonts: false,
            });
        }
    }, [docxPreviewContainer, file]);

    return (
        <Box className="tw-h-full tw-relative">
            <ScrollArea w="100%" h="100%">
                <div ref={docxPreviewContainer} />
            </ScrollArea>
        </Box>
    );
};

export default DocxViewer;
