import cnaRequest from "@code.8cent/utils/cnaRequest";

const gernal = {
    getIPAddress: async () => {
        try {
            let response = await fetch("https://api.ipify.org?format=json");

            let json = await response.json();

            if (json.ip) {
                return json.ip as string;
            } else {
                return "";
            }
        } catch (err) {
            return "";
        }
    },
    getCompanyCategoryList: async () => {
        let { result, error } = await cnaRequest<CompanyCategory[]>(
            "/api/v1/company/categories",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return [];
        }
    },

    getProjectSelectList: async () => {
        let { result, error } = await cnaRequest<ProjectSelectItem[]>(
            "/api/v1/company/projects",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return [];
        }
    },

    getBankList: async () => {
        let { result, error } = await cnaRequest<BankItem[]>(
            "/api/v1/config/bankList",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return [];
        }
    },

    getPaymentMethodList: async () => {
        let { result, error } = await cnaRequest<PaymentMethod[]>(
            "/api/v1/company/progressTwo/payments",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return [];
        }
    },
    getAreaData: async () => {
        let { result, error } = await cnaRequest<BaseApiResponse>(
            "/api/v1/config/divisionCnList",
            "GET"
        );

        if (!error) {
            return result.data;
        } else {
            return [];
        }
    },
};

export default gernal;
