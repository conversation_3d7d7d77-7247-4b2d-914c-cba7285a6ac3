import deskApi from "@/apis/desk";
import { <PERSON><PERSON>, Badge, ActionIcon, Group, Text, Tooltip } from "@mantine/core";
import { useMount, useUnmount, useMemoizedFn } from "ahooks";
import { useState, useRef } from "react";
import useSettingStore from "@code.8cent/store/setting";
import { t } from "@code.8cent/i18n";
import {
    CreditCard,
    FilePdf,
    GearSix,
    IdentificationCard,
    Image,
    NotePencil,
} from "@phosphor-icons/react";
import ProfileInfoModal from "@/components/modals/deskAssociate/ProfileInfo";
import TableRowDropActionMenu from "@/components/common/TableRowDropActionMenu";
import dayjs from "dayjs";
import useModalStore from "@/store/modal";
import PersonalSetting from "@/components/modals/deskAssociate/PersonalSetting";
import Remark from "@/components/modals/Remark";
import TableModal from "@/components/modals/TableModal";
import { getProfileStatus } from "@/utils/constants";
import noty from "@code.8cent/react/noty";
import { PageHeader, DataTable, DataTableRef, CnaButton } from "@code.8cent/react/components";
import { createColumnHelper } from "@tanstack/react-table";
import { exportToExcel } from "@/utils/xlsx";
import useProfileStore from "@/store/profile";
import { useFileViewer } from "@code.8cent/react/FileViewer";
import EditParent from "@/components/modals/deskAssociate/EditParent";
import OrganizationModal from "@/components/modals/deskAssociate/Organization";
import SetTeamTop from "@/components/modals/deskAssociate/SetTeamTop";

const columnHelper = createColumnHelper<
    UserProfileResponse & {
        bankName: string;
        bank_account: string;
        manageCount: number;
        teamCount: number;
        rankName: string;
        preName: string;
        id_file?: {
            profile_id?: number;
            face_file?: string;
            back_file?: string;
        };
        bank_file?: {
            profile_id?: number;
            file?: string;
        };
        contract_file?: {
            contract?: string;
        };
        groupId: number;
        can_affiliated_firm: number;
    }
>();

const GROUP_MAP = [
    {
        value: 0,
        label: "普通人",
    },
    {
        value: 1,
        label: "合伙人",
    },
    {
        value: 2,
        label: "管理合伙人",
    },
    {
        value: 3,
        label: "见习管理合伙人",
    },
];

const AdminDeskAssociatesPage = () => {
    const lang = useSettingStore.use.lang();
    const openModal = useModalStore.use.open();
    const openConfirm = useModalStore.use.openConfirm();
    const { permissions } = useProfileStore();

    const [inited, setInited] = useState(false);

    const tableRef = useRef<DataTableRef | null>(null);
    const [data, setData] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [loading, setLoading] = useState(false);

    const STATUS_MAP = getProfileStatus(lang);

    const { openFileView } = useFileViewer();

    const showTableModel = (key: string, profileID: number) => {
        openModal("tableModal", { tableKey: key, profileID });
    };

    // 处理表格行操作按钮
    const rowActions = (row) => [
        {
            key: "team",
            label: "设置三三制总",
            onClick: () => openModal("setTeamTopModal", { row }),
            disabled: row.team_rank != 0 || row.register_pre_id != 1,
        },
        {
            key: "firm",
            label: "联号事务所",
            onClick: () => setCanApplyFirm(row.profileID, row.profileName),
            disabled: row.can_affiliated_firm !== 0,
        },
        {
            key: "profile",
            label: "简介信息",
            onClick: () =>
                openModal("associateProfileModal", {
                    profile: row,
                    readOnly: row.status == "2",
                }),
        },
        {
            key: "setting",
            label: "个人设置",
            onClick: () => openModal("associatePersonalSettingModal", { profile: row }),
        },
        {
            key: "remark",
            label: "备注信息",
            onClick: () => openModal("remarkModal", { objID: row.profileID, type: 1 }),
        },
        {
            key: "billing",
            label: "付款记录",
            onClick: () => showTableModel("billing", row.profileID),
        },
        {
            key: "benefit",
            label: "福利申请",
            onClick: () => showTableModel("benefit", row.profileID),
        },
        {
            key: "company",
            label: "企业客户",
            onClick: () => showTableModel("company", row.profileID),
        },
        {
            key: "log",
            label: "活动日志",
            onClick: () => showTableModel("log", row.profileID),
        },
        {
            key: "history",
            label: "登入足迹",
            onClick: () => showTableModel("history", row.profileID),
            disabled: true,
        },
    ];

    const refreshTable = useMemoizedFn(() => {
        if (tableRef?.current) {
            tableRef.current.refresh();
        }
    });

    const handleUpdateSettingSuccess = useMemoizedFn(() => {
        refreshTable();
        return true;
    });

    useMount(() => {
        setInited(true);
    });

    useUnmount(() => {
        setInited(false);
    });

    const setCanApplyFirm = useMemoizedFn((profileID: number, profileName: string) => {
        openConfirm({
            title: "提示",
            message: `您确定设置合伙人 ${profileName} 可以申请联号事务所吗？`,
            onConfirm: async () => {
                try {
                    const res = await deskApi.associate.setCanApplyFirm(profileID);

                    if (res) {
                        noty.success("设置成功");
                        refreshTable();
                    }
                } catch (error) {
                    console.error(error);
                }
            },
        });
    });

    const previewFile = useMemoizedFn((path: string, type: "local" | "oss") => {
        openFileView(`/api/v1/admin/firm/files?path=${path}&type=${type}`, {
            title: "查看文件",
        });
    });

    const handleFetch = async (params) => {
        setLoading(true);
        try {
            const { page, pageSize, globalFilters, columnFilters } = params;

            const getFilterValue = (field: string) =>
                columnFilters.find((item) => item.id === field)?.value || "";

            const requestParams = {
                keyword: globalFilters?.keyword || "",
                status: getFilterValue("status"),
                pre_name: getFilterValue("preName"),
                profilePartnerCode: getFilterValue("profilePartnerCode"),
                profileName: getFilterValue("profileName"),
                profileEmail: getFilterValue("profileEmail"),
                profileContact: getFilterValue("profileContact"),
                profileNRIC: getFilterValue("profileNRIC"),
                bank_account: getFilterValue("bank_account"),
                page,
                page_size: pageSize,
            };

            const { items, paginate } = await deskApi.associate.list(requestParams);
            setData(items || []);
            setTotalCount(paginate?.total || 0);
        } finally {
            setLoading(false);
        }
    };

    const handleExport = async () => {
        if (!permissions.includes("partner.export")) {
            noty.error("您没有导出数据的权限！");
            return;
        }

        openConfirm({
            title: "提示",
            message: "您确定要导出数据吗？",
            onConfirm: async () => {
                const { globalFilters, columnFilters } = tableRef.current?.getState() || {};
                const getFilterValue = (field: string) =>
                    columnFilters.find((item) => item.id === field)?.value || "";

                const requestParams = {
                    keyword: globalFilters?.keyword || "",
                    status: getFilterValue("status"),
                    pre_name: getFilterValue("preName"),
                    profilePartnerCode: getFilterValue("profilePartnerCode"),
                    profileName: getFilterValue("profileName"),
                    profileEmail: getFilterValue("profileEmail"),
                    profileContact: getFilterValue("profileContact"),
                    profileNRIC: getFilterValue("profileNRIC"),
                    bank_account: getFilterValue("bank_account"),
                    page: 1,
                    page_size: totalCount,
                };

                const { items } = await deskApi.associate.list(requestParams);

                exportToExcel(
                    items,
                    [
                        { key: "profilePartnerCode", title: "编码" },
                        {
                            key: "status",
                            title: "状态",
                            format: (value) =>
                                STATUS_MAP.find((item) => item.value == value)?.label,
                        },
                        { key: "profileName", title: "名字" },
                        {
                            key: "groupId",
                            title: "角色",
                            format: (value) => GROUP_MAP.find((item) => item.value == value)?.label,
                        },
                        { key: "profileEmail", title: "邮件地址" },
                        { key: "profileContact", title: "手机号码" },
                        { key: "profileNRIC", title: "身份证" },
                        { key: "bankName", title: "银行名称" },
                        { key: "bank_account", title: "银行账号" },
                        { key: "preName", title: "上级推荐人" },
                        { key: "rankName", title: "三三制等级" },
                        { key: "manageCount", title: "管理人数" },
                        { key: "teamCount", title: "团队人数" },
                        {
                            key: "created_at",
                            title: "申请日期",
                            format: (value) => value && dayjs(value).format("YYYY-MM-DD HH:mm:ss"),
                        },
                    ],
                    `合伙人表_${dayjs().format("YYYYMMDD")}.xlsx`
                );
            },
        });
    };

    const tableColumns = [
        columnHelper.accessor("profilePartnerCode", {
            header: "编码",
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("status", {
            header: "状态",
            enableSorting: false,
            cell: (info) => {
                const matchedStatus = STATUS_MAP.find((item) => item.value == info.getValue());
                return <Badge color={matchedStatus?.color}>{matchedStatus?.label}</Badge>;
            },
        }),
        columnHelper.accessor("profileName", {
            header: "名字",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("groupId", {
            header: "角色",
            enableSorting: false,
            cell: (info) => GROUP_MAP.find((item) => item.value == info.getValue())?.label,
        }),
        columnHelper.accessor("profileEmail", {
            header: "邮件地址",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileContact", {
            header: "手机号码",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("profileNRIC", {
            header: "身份证",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            size: 250,
            cell: (info) => {
                const value = info.getValue();
                const idFile = info.row.original.id_file;
                return (
                    <Group wrap="nowrap">
                        <Text size="sm">{value}</Text>
                        {idFile?.face_file && (
                            <ActionIcon
                                variant="default"
                                onClick={() => previewFile(idFile.face_file, "local")}
                            >
                                <IdentificationCard />
                            </ActionIcon>
                        )}
                        {idFile?.back_file && (
                            <ActionIcon
                                variant="default"
                                onClick={() => previewFile(idFile.back_file, "local")}
                            >
                                <Image />
                            </ActionIcon>
                        )}
                    </Group>
                );
            },
        }),
        columnHelper.accessor("bankName", {
            header: "银行名称",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("bank_account", {
            header: "银行账号",
            enableSorting: false,
            meta: {
                enableTooltip: true,
            },
            cell: (info) => {
                const value = info.getValue();
                const bankFile = info.row.original.bank_file;
                return (
                    <Group wrap="nowrap">
                        <Text size="sm">{value}</Text>
                        {bankFile?.file && (
                            <ActionIcon
                                variant="default"
                                onClick={() => previewFile(bankFile.file, "oss")}
                            >
                                <CreditCard />
                            </ActionIcon>
                        )}
                    </Group>
                );
            },
        }),
        columnHelper.accessor("preName", {
            header: "上级推荐人",
            enableSorting: false,
            cell: (info) => {
                const value = info.getValue();
                return (
                    <Group
                        wrap="nowrap"
                        justify="space-between"
                    >
                        <Text size="sm">{value}</Text>
                        <Tooltip label="修改">
                            <ActionIcon
                                variant="default"
                                onClick={() =>
                                    openModal("associatePreNameModal", {
                                        profile: info.row.original,
                                    })
                                }
                            >
                                <NotePencil />
                            </ActionIcon>
                        </Tooltip>
                    </Group>
                );
            },
        }),
        columnHelper.accessor("contract_file", {
            header: "合同文件",
            enableSorting: false,
            cell: (info) => {
                const contractFile = info.row.original.contract_file;
                return (
                    <>
                        {contractFile?.contract && (
                            <ActionIcon
                                variant="default"
                                onClick={() => previewFile(contractFile.contract, "local")}
                            >
                                <FilePdf />
                            </ActionIcon>
                        )}
                    </>
                );
            },
        }),
        columnHelper.accessor("rankName", {
            header: "三三制等级",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        columnHelper.accessor("manageCount", {
            header: "管理人数",
            enableSorting: false,
            cell: (info) => info.getValue(),
        }),
        // columnHelper.accessor("teamCount", {
        //     header: "团队人数",
        //     enableSorting: false,
        //     cell: (info) => {
        //         const value = info.getValue();
        //         const profileID = info.row.original.profileID;
        //         return (
        //             <Tooltip label="查看团队架构">
        //                 <CnaButton
        //                     variant="outline"
        //                     size="xs"
        //                     onClick={() => openModal("associateOrganizationModal", { profileID })}
        //                 >
        //                     {value}
        //                 </CnaButton>
        //             </Tooltip>
        //         );
        //     },
        // }),
        columnHelper.accessor("can_affiliated_firm", {
            header: "可申请联号事务所",
            enableSorting: false,
            cell: (info) => (info.getValue() == 1 ? "是" : "否"),
        }),
        columnHelper.accessor("created_at", {
            header: "申请日期",
            enableSorting: false,
            cell: (info) => info.getValue() && dayjs(info.getValue()).format("YYYY-MM-DD"),
        }),
        columnHelper.display({
            id: "actions",
            header: "操作",
            meta: {
                pinned: "right",
            },
            cell: (info) => {
                return <TableRowDropActionMenu items={rowActions(info.row.original)} />;
            },
        }),
    ];

    return (
        <Stack className="tw-bg-white tw-p-6 tw-overflow-y-auto tw-h-full tw-w-full tw-gap-0">
            <PageHeader
                title="AI 个人办公室合伙人"
                desc="查询 AI 个人办公室合伙人"
            />

            <DataTable
                ref={tableRef}
                data={data}
                columns={tableColumns as any}
                totalCount={totalCount}
                loading={loading}
                onFetch={handleFetch}
                onExport={handleExport}
                serverSideSort={false}
                globalFilterFields={[
                    {
                        field: "keyword",
                        label: "搜索关键字",
                        type: "text",
                    },
                ]}
                filterTypes={{
                    profilePartnerCode: "text",
                    status: "select",
                    profileName: "text",
                    profileEmail: "text",
                    profileContact: "text",
                    profileNRIC: "text",
                    bank_account: "text",
                    preName: "text",
                }}
                filterOptions={{
                    status: STATUS_MAP,
                }}
            />

            {/* 加载渲染详情弹窗 */}
            <ProfileInfoModal />
            {/* 渲染个人设置弹窗 */}
            <PersonalSetting onUpdateSettingSuccess={handleUpdateSettingSuccess} />
            {/* 渲染备注信息弹窗 */}
            <Remark />
            {/* 渲染表格弹窗 */}
            <TableModal />
            {/* 渲染修改上级弹窗 */}
            <EditParent onSubmitSuccess={refreshTable} />
            {/* 渲染团队架构弹窗 */}
            <OrganizationModal />
            {/* 渲染设置三三制总弹窗 */}
            <SetTeamTop />
        </Stack>
    );
};

export default AdminDeskAssociatesPage;
