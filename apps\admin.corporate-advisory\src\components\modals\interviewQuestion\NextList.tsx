import { Group, Modal, <PERSON>roll<PERSON><PERSON>, Stack, Table, Text } from "@mantine/core";
import { useMemoizedFn, useRequest } from "ahooks";
import { useState } from "react";
import useModalStore from "@/store/modal";
import interviewQuestion from "@/apis/interviewQuestion";
import { useConfirm } from "@/hooks/useConfirm";
import { Plus } from "@phosphor-icons/react";
import InterviewQuestionNextForm from "./NextForm";
import noty from "@code.8cent/react/noty";
import { CnaButton } from "@code.8cent/react/components";

const InterviewQuestionNextList = () => {
    const [loading, setLoading] = useState(false);
    const [nextQuestions, setNextQuestions] = useState<TInterviewQuestionNext[]>([]);
    const { confirm } = useConfirm();

    const {
        interviewQuestionNextList: opened,
        modalParams,
        close,
        open,
    } = useModalStore((state) => ({
        interviewQuestionNextList: state.interviewQuestionNextList,
        modalParams: state.modalParams,
        close: state.close,
        open: state.open,
    }));

    // 基础问题
    const baseQuestion = modalParams?.interviewQuestionNextList as TInterviewQuestion | undefined;
    const baseId = baseQuestion?.id;

    // 关闭弹窗
    const handleClose = useMemoizedFn(() => {
        close("interviewQuestionNextList");
    });

    // 获取追问问题列表
    const { loading: fetchLoading, run: fetchNextQuestions } = useRequest(
        async () => {
            if (!baseId) return;

            setLoading(true);
            try {
                const result = await interviewQuestion.next.list({ id: baseId });
                if (result) {
                    setNextQuestions(result);
                }
            } finally {
                setLoading(false);
            }
        },
        {
            ready: !!baseId && opened,
            refreshDeps: [baseId, opened],
            debounceWait: 300,
        }
    );

    // 删除追问问题
    const handleDelete = useMemoizedFn(async (id: number) => {
        try {
            const success = await interviewQuestion.next.destroy(id.toString());
            if (success) {
                noty.success("删除成功");
                fetchNextQuestions();
            }
        } catch (error) {
            console.error("删除失败", error);
            noty.error("删除失败");
        }
    });

    // 确认删除
    const confirmDelete = useMemoizedFn((id: number) => {
        confirm({
            title: "确认删除",
            message: "您确定要删除这个追问问题吗？此操作不可撤销。",
            onConfirm: () => handleDelete(id),
        });
    });

    // 添加追问问题
    const handleAdd = useMemoizedFn(() => {
        open("interviewQuestionNextForm", {
            baseId,
        });
    });

    // 编辑追问问题
    const handleEdit = useMemoizedFn((item: TInterviewQuestionNext) => {
        open("interviewQuestionNextForm", {
            baseId,
            data: item,
        });
    });

    return (
        <>
            <Modal
                opened={opened}
                onClose={handleClose}
                title={`追问问题列表 - ${baseQuestion?.question || ""}`}
                size="xl"
            >
                <Stack gap="md">
                    <Group justify="space-between">
                        <Text>共 {nextQuestions.length} 个追问问题</Text>
                        <CnaButton
                            leftSection={<Plus size={14} />}
                            onClick={handleAdd}
                            loading={loading}
                            disabled={!baseId}
                            size="xs"
                        >
                            添加追问问题
                        </CnaButton>
                    </Group>

                    <ScrollArea h={400}>
                        <Table
                            striped
                            highlightOnHover
                            withTableBorder
                        >
                            <Table.Thead>
                                <Table.Tr>
                                    <Table.Th>追问问题</Table.Th>
                                    <Table.Th>操作</Table.Th>
                                </Table.Tr>
                            </Table.Thead>
                            <Table.Tbody>
                                {nextQuestions.length === 0 ? (
                                    <Table.Tr>
                                        <Table.Td
                                            colSpan={2}
                                            align="center"
                                        >
                                            {fetchLoading ? "加载中..." : "暂无追问问题"}
                                        </Table.Td>
                                    </Table.Tr>
                                ) : (
                                    nextQuestions.map((item) => (
                                        <Table.Tr key={item.id}>
                                            <Table.Td>{item.next}</Table.Td>
                                            <Table.Td>
                                                <Group gap="xs">
                                                    <CnaButton
                                                        variant="outline"
                                                        size="xs"
                                                        onClick={() => handleEdit(item)}
                                                    >
                                                        编辑
                                                    </CnaButton>
                                                    {/* <CnaButton
                                                        size="xs"
                                                        color="red"
                                                        onClick={() => confirmDelete(item.id)}
                                                    >
                                                        删除
                                                    </CnaButton> */}
                                                </Group>
                                            </Table.Td>
                                        </Table.Tr>
                                    ))
                                )}
                            </Table.Tbody>
                        </Table>
                    </ScrollArea>
                </Stack>
            </Modal>

            {/* 追问问题表单弹窗 */}
            <InterviewQuestionNextForm refreshNextList={fetchNextQuestions} />
        </>
    );
};

export default InterviewQuestionNextList;
