import { Box } from "@mantine/core";
import CnaAdminButton from "./CnaAdminButton";

interface ButtonProps {
    key: string;
    label: string;
    leftSection?: React.ReactNode;
    onClick: () => void;
    needPermission?: string; // 所需权限
    noPermissionHidden?: boolean; // 没有权限后是否隐藏
}

const PageActionButtons: React.FC<{
    buttons: ButtonProps[];
    className?: string;
}> = ({ buttons = [], className = "" }) => {
    return (
        <Box className={className}>
            <Box className="tw-flex tw-gap-2">
                {buttons.map((button) => (
                    <Box
                        key={button.key}
                        className="tw-flex tw-items-center"
                    >
                        <CnaAdminButton
                            size="sm"
                            variant="outline"
                            color="basic"
                            leftSection={button.leftSection}
                            onClick={button.onClick}
                            needPermission={button.needPermission}
                            noPermissionHidden={button.noPermissionHidden}
                        >
                            {button.label}
                        </CnaAdminButton>
                    </Box>
                ))}
            </Box>
        </Box>
    );
};

export default PageActionButtons;
